import raf from './raf';
import getScroll, { isWindow } from './getScroll';
import { easeInOutCubic } from './easings';
export default function scrollTo(y) {
  var options = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};
  var _options$getContainer = options.getContainer,
    getContainer = _options$getContainer === void 0 ? function () {
      return window;
    } : _options$getContainer,
    callback = options.callback,
    _options$duration = options.duration,
    duration = _options$duration === void 0 ? 450 : _options$duration;
  var container = getContainer();
  var scrollTop = getScroll(container, true);
  var startTime = Date.now();
  var frameFunc = function frameFunc() {
    var timestamp = Date.now();
    var time = timestamp - startTime;
    var nextScrollTop = easeInOutCubic(time > duration ? duration : time, scrollTop, y, duration);
    if (isWindow(container)) {
      container.scrollTo(window.pageXOffset, nextScrollTop);
    } else if (container instanceof HTMLDocument || container.constructor.name === 'HTMLDocument') {
      container.documentElement.scrollTop = nextScrollTop;
    } else {
      container.scrollTop = nextScrollTop;
    }
    if (time < duration) {
      raf(frameFunc);
    } else if (typeof callback === 'function') {
      callback();
    }
  };
  raf(frameFunc);
}
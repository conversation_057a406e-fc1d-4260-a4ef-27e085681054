'use strict';

var _extends = Object.assign || function (target) { for (var i = 1; i < arguments.length; i++) { var source = arguments[i]; for (var key in source) { if (Object.prototype.hasOwnProperty.call(source, key)) { target[key] = source[key]; } } } return target; };

var _react = require('react');

var _react2 = _interopRequireDefault(_react);

var _immutable = require('immutable');

var _immutable2 = _interopRequireDefault(_immutable);

var _testUtils = require('testUtils');

function _interopRequireDefault(obj) { return obj && obj.__esModule ? obj : { default: obj }; }

var getComponent = function getComponent() {
  return require('ui/box/chrome').default;
};

jest.mock('ui/box/header', function () {
  return (0, _testUtils.mockComponent)('header');
});
jest.mock('ui/box/multisize_slide', function () {
  return (0, _testUtils.mockComponent)('div');
});
jest.mock('ui/box/global_message', function () {
  return (0, _testUtils.mockComponent)('div');
});

var mockEventRegister = {};

var triggerEvent = function triggerEvent(name) {
  if (name in mockEventRegister) {
    return mockEventRegister[name]();
  }

  throw new Error('Unknown event \'' + name + '\'');
};

jest.mock('core/index', function () {
  return {
    handleEvent: jest.fn(function (_, event, fn) {
      mockEventRegister[event] = fn;
    }),
    ui: {
      forceAutoHeight: jest.fn().mockReturnValue(false)
    },
    id: jest.fn(function () {
      return 'lock';
    })
  };
});

var defaultProps = {
  contentComponent: (0, _testUtils.mockComponent)('content'),
  contentProps: {
    model: _immutable2.default.fromJS({
      id: '__lock_id__'
    })
  },
  avatar: 'avatar',
  isSubmitting: false,
  logo: 'logo',
  primaryColor: 'white',
  screenName: 'screen name',
  classNames: '',
  color: 'black'
};

describe('Chrome', function () {
  var Chrome = void 0;

  beforeEach(function () {
    Chrome = getComponent();
    Chrome.prototype.getHeaderSize = jest.fn(function () {
      return 200;
    });

    mockEventRegister = {};
  });

  it('renders correctly with basic props', function () {
    (0, _testUtils.expectComponent)(_react2.default.createElement(Chrome, defaultProps)).toMatchSnapshot();
  });

  it('renders correctly when there is a global message', function () {
    var props = _extends({}, defaultProps, {
      error: 'There is an error'
    });

    (0, _testUtils.expectComponent)(_react2.default.createElement(Chrome, props)).toMatchSnapshot();
  });

  it('renders correctly when there is a global success message', function () {
    var props = _extends({}, defaultProps, {
      success: 'This is a success message'
    });

    (0, _testUtils.expectComponent)(_react2.default.createElement(Chrome, props)).toMatchSnapshot();
  });

  it('renders correctly when there is a global information message', function () {
    var props = _extends({}, defaultProps, {
      info: 'This is an information message'
    });

    (0, _testUtils.expectComponent)(_react2.default.createElement(Chrome, props)).toMatchSnapshot();
  });

  it('can dislay all global messages together', function () {
    var props = _extends({}, defaultProps, {
      info: 'This is an information message',
      success: 'This is a success message',
      error: 'There is an error'
    });

    (0, _testUtils.expectComponent)(_react2.default.createElement(Chrome, props)).toMatchSnapshot();
  });

  it('adds the auto-height class when forceAutoHeight UI prop is true', function () {
    require('core/index').ui.forceAutoHeight.mockReturnValue(true);

    var props = _extends({}, defaultProps, {
      info: 'This is an information message',
      success: 'This is a success message',
      error: 'There is an error'
    });

    (0, _testUtils.expectComponent)(_react2.default.createElement(Chrome, props)).toMatchSnapshot();
  });
});

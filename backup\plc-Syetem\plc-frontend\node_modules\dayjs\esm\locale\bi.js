// Bislama [bi]
import dayjs from '../index';
var locale = {
  name: 'bi',
  weekdays: '<PERSON><PERSON>_<PERSON>_Tusde_Wenesde_Tosde_Fraede_Sarade'.split('_'),
  months: '<PERSON><PERSON><PERSON>_<PERSON><PERSON><PERSON>_Maj_E<PERSON><PERSON>_<PERSON>_<PERSON>_<PERSON><PERSON>_<PERSON>is_Septemba_Oktoba_Novemba_Disemba'.split('_'),
  weekStart: 1,
  weekdaysShort: '<PERSON>_<PERSON>_<PERSON>_Wen_Tos_Frae_Sar'.split('_'),
  monthsShort: 'Jan_Feb_Maj_Epr_Mai_Jun_Jul_Oki_Sep_Okt_Nov_Dis'.split('_'),
  weekdaysMin: 'San_Ma_Tu_We_To_Fr_Sar'.split('_'),
  ordinal: function ordinal(n) {
    return n;
  },
  formats: {
    LT: 'h:mm A',
    LTS: 'h:mm:ss A',
    L: 'DD/MM/YYYY',
    LL: 'D MMMM YYYY',
    LLL: 'D MMMM YYYY h:mm A',
    LLLL: 'dddd, D MMMM YYYY h:mm A'
  },
  relativeTime: {
    future: 'lo %s',
    past: '%s bifo',
    s: 'sam seken',
    m: 'wan minit',
    mm: '%d minit',
    h: 'wan haoa',
    hh: '%d haoa',
    d: 'wan dei',
    dd: '%d dei',
    M: 'wan manis',
    MM: '%d manis',
    y: 'wan yia',
    yy: '%d yia'
  }
};
dayjs.locale(locale, null, true);
export default locale;
const set = require('regenerate')(0x23F0, 0x23F3, 0x267F, 0x2693, 0x26A1, 0x26CE, 0x26D4, 0x26EA, 0x26F5, 0x26FA, 0x26FD, 0x2705, 0x2728, 0x274C, 0x274E, 0x2757, 0x27B0, 0x27BF, 0x2B50, 0x2B55, 0x1F004, 0x1F0CF, 0x1F18E, 0x1F201, 0x1F21A, 0x1F22F, 0x1F3F4, 0x1F440, 0x1F57A, 0x1F5A4, 0x1F6CC, 0x1F7F0);
set.addRange(0x231A, 0x231B).addRange(0x23E9, 0x23EC).addRange(0x25FD, 0x25FE).addRange(0x2614, 0x2615).addRange(0x2648, 0x2653).addRange(0x26AA, 0x26AB).addRange(0x26BD, 0x26BE).addRange(0x26C4, 0x26C5).addRange(0x26F2, 0x26F3).addRange(0x270A, 0x270B).addRange(0x2753, 0x2755).addRange(0x2795, 0x2797).addRange(0x2B1B, 0x2B1C).addRange(0x1F191, 0x1F19A).addRange(0x1F232, 0x1F236).addRange(0x1F238, 0x1F23A).addRange(0x1F250, 0x1F251).addRange(0x1F300, 0x1F320).addRange(0x1F32D, 0x1F335).addRange(0x1F337, 0x1F37C).addRange(0x1F37E, 0x1F393).addRange(0x1F3A0, 0x1F3CA).addRange(0x1F3CF, 0x1F3D3).addRange(0x1F3E0, 0x1F3F0).addRange(0x1F3F8, 0x1F43E).addRange(0x1F442, 0x1F4FC).addRange(0x1F4FF, 0x1F53D).addRange(0x1F54B, 0x1F54E).addRange(0x1F550, 0x1F567).addRange(0x1F595, 0x1F596).addRange(0x1F5FB, 0x1F64F).addRange(0x1F680, 0x1F6C5).addRange(0x1F6D0, 0x1F6D2).addRange(0x1F6D5, 0x1F6D7).addRange(0x1F6DC, 0x1F6DF).addRange(0x1F6EB, 0x1F6EC).addRange(0x1F6F4, 0x1F6FC).addRange(0x1F7E0, 0x1F7EB).addRange(0x1F90C, 0x1F93A).addRange(0x1F93C, 0x1F945).addRange(0x1F947, 0x1F9FF).addRange(0x1FA70, 0x1FA7C).addRange(0x1FA80, 0x1FA89).addRange(0x1FA8F, 0x1FAC6).addRange(0x1FACE, 0x1FADC).addRange(0x1FADF, 0x1FAE9).addRange(0x1FAF0, 0x1FAF8);
exports.characters = set;
exports.strings = ['\xA9\uFE0F','\xAE\uFE0F','\u203C\uFE0F','\u2049\uFE0F','\u2122\uFE0F','\u2139\uFE0F','\u2194\uFE0F','\u2195\uFE0F','\u2196\uFE0F','\u2197\uFE0F','\u2198\uFE0F','\u2199\uFE0F','\u21A9\uFE0F','\u21AA\uFE0F','\u2328\uFE0F','\u23CF\uFE0F','\u23ED\uFE0F','\u23EE\uFE0F','\u23EF\uFE0F','\u23F1\uFE0F','\u23F2\uFE0F','\u23F8\uFE0F','\u23F9\uFE0F','\u23FA\uFE0F','\u24C2\uFE0F','\u25AA\uFE0F','\u25AB\uFE0F','\u25B6\uFE0F','\u25C0\uFE0F','\u25FB\uFE0F','\u25FC\uFE0F','\u2600\uFE0F','\u2601\uFE0F','\u2602\uFE0F','\u2603\uFE0F','\u2604\uFE0F','\u260E\uFE0F','\u2611\uFE0F','\u2618\uFE0F','\u261D\uFE0F','\u2620\uFE0F','\u2622\uFE0F','\u2623\uFE0F','\u2626\uFE0F','\u262A\uFE0F','\u262E\uFE0F','\u262F\uFE0F','\u2638\uFE0F','\u2639\uFE0F','\u263A\uFE0F','\u2640\uFE0F','\u2642\uFE0F','\u265F\uFE0F','\u2660\uFE0F','\u2663\uFE0F','\u2665\uFE0F','\u2666\uFE0F','\u2668\uFE0F','\u267B\uFE0F','\u267E\uFE0F','\u2692\uFE0F','\u2694\uFE0F','\u2695\uFE0F','\u2696\uFE0F','\u2697\uFE0F','\u2699\uFE0F','\u269B\uFE0F','\u269C\uFE0F','\u26A0\uFE0F','\u26A7\uFE0F','\u26B0\uFE0F','\u26B1\uFE0F','\u26C8\uFE0F','\u26CF\uFE0F','\u26D1\uFE0F','\u26D3\uFE0F','\u26E9\uFE0F','\u26F0\uFE0F','\u26F1\uFE0F','\u26F4\uFE0F','\u26F7\uFE0F','\u26F8\uFE0F','\u26F9\uFE0F','\u2702\uFE0F','\u2708\uFE0F','\u2709\uFE0F','\u270C\uFE0F','\u270D\uFE0F','\u270F\uFE0F','\u2712\uFE0F','\u2714\uFE0F','\u2716\uFE0F','\u271D\uFE0F','\u2721\uFE0F','\u2733\uFE0F','\u2734\uFE0F','\u2744\uFE0F','\u2747\uFE0F','\u2763\uFE0F','\u2764\uFE0F','\u27A1\uFE0F','\u2934\uFE0F','\u2935\uFE0F','\u2B05\uFE0F','\u2B06\uFE0F','\u2B07\uFE0F','\u3030\uFE0F','\u303D\uFE0F','\u3297\uFE0F','\u3299\uFE0F','\u{1F170}\uFE0F','\u{1F171}\uFE0F','\u{1F17E}\uFE0F','\u{1F17F}\uFE0F','\u{1F202}\uFE0F','\u{1F237}\uFE0F','\u{1F321}\uFE0F','\u{1F324}\uFE0F','\u{1F325}\uFE0F','\u{1F326}\uFE0F','\u{1F327}\uFE0F','\u{1F328}\uFE0F','\u{1F329}\uFE0F','\u{1F32A}\uFE0F','\u{1F32B}\uFE0F','\u{1F32C}\uFE0F','\u{1F336}\uFE0F','\u{1F37D}\uFE0F','\u{1F396}\uFE0F','\u{1F397}\uFE0F','\u{1F399}\uFE0F','\u{1F39A}\uFE0F','\u{1F39B}\uFE0F','\u{1F39E}\uFE0F','\u{1F39F}\uFE0F','\u{1F3CB}\uFE0F','\u{1F3CC}\uFE0F','\u{1F3CD}\uFE0F','\u{1F3CE}\uFE0F','\u{1F3D4}\uFE0F','\u{1F3D5}\uFE0F','\u{1F3D6}\uFE0F','\u{1F3D7}\uFE0F','\u{1F3D8}\uFE0F','\u{1F3D9}\uFE0F','\u{1F3DA}\uFE0F','\u{1F3DB}\uFE0F','\u{1F3DC}\uFE0F','\u{1F3DD}\uFE0F','\u{1F3DE}\uFE0F','\u{1F3DF}\uFE0F','\u{1F3F3}\uFE0F','\u{1F3F5}\uFE0F','\u{1F3F7}\uFE0F','\u{1F43F}\uFE0F','\u{1F441}\uFE0F','\u{1F4FD}\uFE0F','\u{1F549}\uFE0F','\u{1F54A}\uFE0F','\u{1F56F}\uFE0F','\u{1F570}\uFE0F','\u{1F573}\uFE0F','\u{1F574}\uFE0F','\u{1F575}\uFE0F','\u{1F576}\uFE0F','\u{1F577}\uFE0F','\u{1F578}\uFE0F','\u{1F579}\uFE0F','\u{1F587}\uFE0F','\u{1F58A}\uFE0F','\u{1F58B}\uFE0F','\u{1F58C}\uFE0F','\u{1F58D}\uFE0F','\u{1F590}\uFE0F','\u{1F5A5}\uFE0F','\u{1F5A8}\uFE0F','\u{1F5B1}\uFE0F','\u{1F5B2}\uFE0F','\u{1F5BC}\uFE0F','\u{1F5C2}\uFE0F','\u{1F5C3}\uFE0F','\u{1F5C4}\uFE0F','\u{1F5D1}\uFE0F','\u{1F5D2}\uFE0F','\u{1F5D3}\uFE0F','\u{1F5DC}\uFE0F','\u{1F5DD}\uFE0F','\u{1F5DE}\uFE0F','\u{1F5E1}\uFE0F','\u{1F5E3}\uFE0F','\u{1F5E8}\uFE0F','\u{1F5EF}\uFE0F','\u{1F5F3}\uFE0F','\u{1F5FA}\uFE0F','\u{1F6CB}\uFE0F','\u{1F6CD}\uFE0F','\u{1F6CE}\uFE0F','\u{1F6CF}\uFE0F','\u{1F6E0}\uFE0F','\u{1F6E1}\uFE0F','\u{1F6E2}\uFE0F','\u{1F6E3}\uFE0F','\u{1F6E4}\uFE0F','\u{1F6E5}\uFE0F','\u{1F6E9}\uFE0F','\u{1F6F0}\uFE0F','\u{1F6F3}\uFE0F'];

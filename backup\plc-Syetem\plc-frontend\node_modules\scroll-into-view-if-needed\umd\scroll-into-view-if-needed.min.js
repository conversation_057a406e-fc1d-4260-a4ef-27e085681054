!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e="undefined"!=typeof globalThis?globalThis:e||self).scrollIntoView=t()}(this,(function(){"use strict";function e(e){return"object"==typeof e&&null!=e&&1===e.nodeType}function t(e,t){return(!t||"hidden"!==e)&&"visible"!==e&&"clip"!==e}function n(e,n){if(e.clientHeight<e.scrollHeight||e.clientWidth<e.scrollWidth){var o=getComputedStyle(e,null);return t(o.overflowY,n)||t(o.overflowX,n)||function(e){var t=function(e){if(!e.ownerDocument||!e.ownerDocument.defaultView)return null;try{return e.ownerDocument.defaultView.frameElement}catch(e){return null}}(e);return!!t&&(t.clientHeight<e.scrollHeight||t.clientWidth<e.scrollWidth)}(e)}return!1}function o(e,t,n,o,i,r,l,f){return r<e&&l>t||r>e&&l<t?0:r<=e&&f<=n||l>=t&&f>=n?r-e-o:l>t&&f<n||r<e&&f>n?l-t+i:0}var i=function(t,i){var r=window,l=i.scrollMode,f=i.block,u=i.inline,c=i.boundary,d=i.skipOverflowHiddenElements,s="function"==typeof c?c:function(e){return e!==c};if(!e(t))throw new TypeError("Invalid target");for(var a,h,p=document.scrollingElement||document.documentElement,m=[],g=t;e(g)&&s(g);){if((g=null==(h=(a=g).parentElement)?a.getRootNode().host||null:h)===p){m.push(g);break}null!=g&&g===document.body&&n(g)&&!n(document.documentElement)||null!=g&&n(g,d)&&m.push(g)}for(var v=r.visualViewport?r.visualViewport.width:innerWidth,w=r.visualViewport?r.visualViewport.height:innerHeight,b=window.scrollX||pageXOffset,y=window.scrollY||pageYOffset,W=t.getBoundingClientRect(),H=W.height,E=W.width,M=W.top,T=W.right,V=W.bottom,k=W.left,x="start"===f||"nearest"===f?M:"end"===f?V:M+H/2,I="center"===u?k+E/2:"end"===u?T:k,C=[],O=0;O<m.length;O++){var j=m[O],B=j.getBoundingClientRect(),D=B.height,R=B.width,L=B.top,X=B.right,Y=B.bottom,S=B.left;if("if-needed"===l&&M>=0&&k>=0&&V<=w&&T<=v&&M>=L&&V<=Y&&k>=S&&T<=X)return C;var N=getComputedStyle(j),q=parseInt(N.borderLeftWidth,10),z=parseInt(N.borderTopWidth,10),A=parseInt(N.borderRightWidth,10),F=parseInt(N.borderBottomWidth,10),G=0,J=0,K="offsetWidth"in j?j.offsetWidth-j.clientWidth-q-A:0,P="offsetHeight"in j?j.offsetHeight-j.clientHeight-z-F:0,Q="offsetWidth"in j?0===j.offsetWidth?0:R/j.offsetWidth:0,U="offsetHeight"in j?0===j.offsetHeight?0:D/j.offsetHeight:0;if(p===j)G="start"===f?x:"end"===f?x-w:"nearest"===f?o(y,y+w,w,z,F,y+x,y+x+H,H):x-w/2,J="start"===u?I:"center"===u?I-v/2:"end"===u?I-v:o(b,b+v,v,q,A,b+I,b+I+E,E),G=Math.max(0,G+y),J=Math.max(0,J+b);else{G="start"===f?x-L-z:"end"===f?x-Y+F+P:"nearest"===f?o(L,Y,D,z,F+P,x,x+H,H):x-(L+D/2)+P/2,J="start"===u?I-S-q:"center"===u?I-(S+R/2)+K/2:"end"===u?I-X+A+K:o(S,X,R,q,A+K,I,I+E,E);var Z=j.scrollLeft,$=j.scrollTop;x+=$-(G=Math.max(0,Math.min($+G/U,j.scrollHeight-D/U+P))),I+=Z-(J=Math.max(0,Math.min(Z+J/Q,j.scrollWidth-R/Q+K)))}C.push({el:j,top:G,left:J})}return C};function r(e){return e===Object(e)&&0!==Object.keys(e).length}return function(e,t){var n=e.isConnected||e.ownerDocument.documentElement.contains(e);if(r(t)&&"function"==typeof t.behavior)return t.behavior(n?i(e,t):[]);if(n){var o=function(e){return!1===e?{block:"end",inline:"nearest"}:r(e)?e:{block:"start",inline:"nearest"}}(t);return function(e,t){void 0===t&&(t="auto");var n="scrollBehavior"in document.body.style;e.forEach((function(e){var o=e.el,i=e.top,r=e.left;o.scroll&&n?o.scroll({top:i,left:r,behavior:t}):(o.scrollTop=i,o.scrollLeft=r)}))}(i(e,o),o.behavior)}}}));

let theme;_506‍.w("./src/config/theme/themeVariables",[["theme",["theme"],function(v){theme=v}]]);
const NodePolyfillPlugin = require("node-polyfill-webpack-plugin");
const TerserPlugin = require('terser-webpack-plugin');

module.exports = {
  publicPath:
    process.env.NODE_ENV === "production"
      ? process.env.VUE_APP_SUB_ROUTE
        ? process.env.VUE_APP_SUB_ROUTE
        : process.env.BASE_URL
      : process.env.BASE_URL,
  css: {
    loaderOptions: {
      less: {
        lessOptions: {
          modifyVars: {
            ...theme,
          },
          javascriptEnabled: true,
        },
      },
    },
  },
  productionSourceMap: false,
  configureWebpack: {
    plugins: [new NodePolyfillPlugin()],
    optimization: {
      minimize: true,
      minimizer: [
        new TerserPlugin({
          parallel: true,
          terserOptions: {
            compress: {
              drop_console: process.env.NODE_ENV === 'production',
              drop_debugger: process.env.NODE_ENV === 'production'
            }
          }
        })
      ],
      splitChunks: {
        chunks: 'all',
        minSize: 20000,
        maxSize: 250000,
        minChunks: 1,
        maxAsyncRequests: 30,
        maxInitialRequests: 30,
        cacheGroups: {
          defaultVendors: {
            test: /[\\/]node_modules[\\/]/,
            priority: -10,
            reuseExistingChunk: true
          },
          default: {
            minChunks: 2,
            priority: -20,
            reuseExistingChunk: true
          }
        }
      }
    },
    output: {
      filename:
        process.env.NODE_ENV === "production"
          ? "js/[name].[hash].js"
          : "js/[name].js",
      chunkFilename:
        process.env.NODE_ENV === "production"
          ? "js/[name].[hash].js"
          : "js/[name].js",
    },
    cache: {
      type: 'filesystem',
      buildDependencies: {
        config: [__filename]
      }
    }
  },
  devServer: {
    proxy: {
      "/api": {
        target: process.env.VUE_APP_API_ENDPOINT,
        changeOrigin: true,
        pathRewrite: {
          "/api": "",
        },
      },
      "/exportApi": {
        target: "http://localhost:5146",
        changeOrigin: true,
        pathRewrite: {
          "^/exportApi": ""
        }
      },
      "/imgApi": {
        target: process.env.IMAGE_URL,
        changeOrigin: true,
        pathRewrite: {
          "/imgApi": "",
        },
      },

    },
  },
};

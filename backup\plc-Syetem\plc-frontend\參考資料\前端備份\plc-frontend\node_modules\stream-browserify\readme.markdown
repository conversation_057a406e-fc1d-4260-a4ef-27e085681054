# stream-browserify

the stream module from node core, for browsers!

This module uses [`readable-stream`](https://github.com/nodejs/readable-stream), with additions for compatibility with npm packages that use old Node.js stream APIs.

[![build status](https://secure.travis-ci.org/browserify/stream-browserify.svg?branch=master)](http://travis-ci.org/browserify/stream-browserify)

## Install

You usually do not have to install `stream-browserify` yourself! If your code runs in Node.js, `stream` is built in, or `readable-stream` can be used. If your code runs in the browser, bundlers like [browserify](https://github.com/browserify/browserify) also include the `stream-browserify` module.

But if none of those apply, with [npm](https://npmjs.org) do:

```bash
npm install stream-browserify
```

## API

Consult the node core
[documentation on streams](http://nodejs.org/docs/latest/api/stream.html).

## Browser Support

Cross-browser testing generously provided by [Sauce Labs](https://saucelabs.com).

[![Sauce Test Status](https://saucelabs.com/browser-matrix/stream-browserify.svg)](https://saucelabs.com/u/stream-browserify)

## License

[MIT](./LICENSE)

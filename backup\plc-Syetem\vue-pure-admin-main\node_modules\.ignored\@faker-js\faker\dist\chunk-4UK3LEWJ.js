import{a as e}from"./chunk-KERBADJJ.js";import{n as a,o as r}from"./chunk-YQYVFZYE.js";var o=["Alu\xEDsio Azevedo","Ariano Suassuna","<PERSON>\xE3es","<PERSON><PERSON><PERSON>","<PERSON>","Carolina Maria de Jesus","Castro Alves","Cec\xEDlia Meireles","Clarice Lispector","Concei\xE7\xE3o Evaristo","Cora Coralina","Cruz e Sousa","Gon\xE7alves Dias","<PERSON>\xF3<PERSON>","<PERSON>","<PERSON><PERSON>\xE9 de Al<PERSON>car","Jo\xE3o Gui<PERSON>\xE3es Rosa","Luis Fernando Verissi<PERSON>","Lygia Bojunga","<PERSON><PERSON><PERSON> de As<PERSON>","<PERSON><PERSON><PERSON> de Barros","<PERSON> Cola<PERSON>ti","<PERSON>","<PERSON><PERSON><PERSON> So<PERSON>","<PERSON><PERSON> Lobato","<PERSON>\xE1rio de Andrade","<PERSON>","<PERSON><PERSON><PERSON>","<PERSON>","Rubem Fonseca","<PERSON><PERSON><PERSON>","\xC1lvar<PERSON> de Azevedo"];var i=["<PERSON>livro","Capa dura","Capa mole","Ebook"];var t=["Aventura","Biografia","Cl\xE1ssico","Com\xE9dia","Detetive","Drama","Fantasia","Faroeste","Fic\xE7\xE3o Cient\xEDfica","Fic\xE7\xE3o Hist\xF3rica","Filosofia","Literatura Infantil","Mem\xF3rias","Mist\xE9rio","Mitologia","Neg\xF3cios","Poesia","Psicologia","Quadrinhos","Religi\xE3o","Romance","Romance Gr\xE1fico","Suspense","Terror"];var n=["Companhia das Letras","Editora Abril","Editora Aleph","Editora Antof\xE1gica","Editora Conrad","Editora Darkside","Editora FTD","Editora Gente","Editora HarperCollins Brasil","Editora Intr\xEDnseca","Editora L&PM","Editora Martin Claret","Editora Melhoramentos","Editora Moderna","Editora Panda Books","Editora Pipoca & Nanquim","Editora Planeta de Livros Brasil","Editora Rocco","Editora Saraiva","Editora Sextante","Editora Viseu","Editora Voo","Globo Livros"];var s=["Ed Mort","O Tempo e o Vento","Os Subterr\xE2neos da Liberdade","S\xE9rie Vaga-Lume","S\xEDtio do Picapau Amarelo","Trilogia do Descobrimento"];var l=["A Estrela sobe","A coleira do c\xE3o","A escrava Isaura","A hora da estrela","A moreninha","A m\xE3o e a luva","A paix\xE3o segundo G.H.","A rosa do povo","A vida como ela \xE9","Ang\xFAstia","As meninas","Ba\xFA de ossos","Broqu\xE9is","Br\xE1s, bexiga e barra funda","Cana\xE3","Cartas chilenas","Casa grande e senzala","Cascalho","Claro enigma","Contos gauchescos","Corpo de baile","Cr\xF4nica da casa assassinada","Dom Casmurro","Dona Flor e seus dois maridos","Espumas flutuantes","Estrela da manh\xE3","Eu","Farda, fard\xE3o, camisola de dormir","Fogo morto","Fundador","Gabriela, cravo e canela","Gram\xE1tica expositiva do ch\xE3o","Grande sert\xE3o: veredas","Iai\xE1 Garcia","Inoc\xEAncia","Inven\xE7\xE3o de Orfeu","Iracema","Jubiab\xE1","Lavoura arcaica","La\xE7os de fam\xEDlia","Libertinagem","Luc\xEDola","Macuna\xEDma","Malagueta, Perus e Bacana\xE7o","Mar morto","Mar\xEDlia de Dirceu","Memorial de Aires","Mem\xF3rias do c\xE1rcere","Mem\xF3rias p\xF3stumas de Br\xE1s Cubas","Mem\xF3rias sentimentais de Jo\xE3o Miramar","Mem\xF3rias sgto de mil\xEDcias","Minha forma\xE7\xE3o","Morte e vida severina","Noite na taverna","O ateneu","O coronel e o lobisomem","O corti\xE7o","O dem\xF4nio familiar","O encontro marcado","O feij\xE3o e o sonho","O guarani","O mez da grippe","O pagador de promessas","O quinze","O tempo e o vento","O uraguai","O vampiro de Curitiba","Obra po\xE9tica","Os cavalinhos de platiplanto","Os ratos","Os sert\xF5es","Pap\xE9is avulsos","Paulic\xE9ia desvairada","Pedra Bonita","Poema sujo","Poesias","Primeiras est\xF3rias","Primeiros Cantos","Quarup","Quincas Borba","Ra\xEDzes do Brasil","Ritmo dissoluto","Romance da Pedra do Reino","Romanceiro da inconfid\xEAncia","Sagarana","Senhora","Serm\xF5es","S\xE3o Bernardo","Tenda dos milagres","Terras do sem fim","Triste fim de Policarpo Quaresma","Uma aprendizagem","Veronika decide morrer","Vestido de noiva","Vidas secas","Viva o povo brasileiro","Zero","\xD3pera dos mortos"];var sa={author:o,format:i,genre:t,publisher:n,series:s,title:l},u=sa;var m=["amarelo","ametista","azul","azul celeste","azul marinho","azul petr\xF3leo","a\xE7afr\xE3o","bord\xF4","bronze","caramelo","castanho","cenoura","cinza","cobre","coral","dourado","escarlate","esmeralda","ferrugem","fuligem","f\xFAchsia","gren\xE1","jade","laranja","lil\xE1s","lim\xE3o","madeira","magenta","marrom","ouro","pele","prata","preto","p\xFArpura","rosa","roxo","salm\xE3o","turquesa","verde","verde lima","verde-azulado","vermelho","violeta","\xE2mbar","\xEDndigo"];var la={human:m},d=la;var c=["Automotivo","Beb\xEA","Beleza","Brinquedos","Casa","Computadores","Crian\xE7as","Eletr\xF4nicos","Esportes","Ferramentas","Filmes","Industrial","Jardim","Jogos","J\xF3ias","Livros","Mercearia","M\xFAsica","Roupas","Sapatos","Sa\xFAde","Turismo"];var p={adjective:["Ergon\xF4mico","Fant\xE1stico","Feito \xE0 m\xE3o","Gen\xE9rico","Gostoso","Impressionante","Incr\xEDvel","Inteligente","Licenciado","Lindo","Lustroso","Pequeno","Pr\xE1tico","Refinado","R\xFAstico","Sem marca"],material:["Algod\xE3o","A\xE7o","Borracha","Concreto","Congelado","Fresco","Granito","Macio","Madeira","Metal","Pl\xE1stico"],product:["Atum","Bacon","Bicicleta","Bola","Cadeira","Cal\xE7as","Camiseta","Carro","Chap\xE9u","Computador","Frango","Luvas","Mesa","Mouse","Peixe","Pizza","Queijo","Sabonete","Salada","Salgadinhos","Salsicha","Sapatos","Teclado","Toalhas"]};var ua={department:c,product_name:p},f=ua;var g=["Com\xE9rcio","EIRELI","LTDA","S.A.","e Associados"];var b=["{{person.last_name.generic}} {{company.legal_entity_type}}","{{person.last_name.generic}}, {{person.last_name.generic}} e {{person.last_name.generic}}","{{person.last_name.generic}}-{{person.last_name.generic}}"];var ma={legal_entity_type:g,name_pattern:b},v=ma;var h={wide:["Abril","Agosto","Dezembro","Fevereiro","Janeiro","Julho","Junho","Maio","Mar\xE7o","Novembro","Outubro","Setembro"],abbr:["Abr","Ago","Dez","Fev","Jan","Jul","Jun","Mai","Mar","Nov","Out","Set"]};var M={wide:["Domingo","Quarta","Quinta","Segunda","Sexta","S\xE1bado","Ter\xE7a"],abbr:["Dom","Qua","Qui","Seg","Sex","S\xE1b","Ter"]};var da={month:h,weekday:M},C=da;var A=["biz","br","com","info","name","net","org"];var x=["bol.com.br","gmail.com","hotmail.com","live.com","yahoo.com"];var ca={domain_suffix:A,free_email:x},S=ca;var L=["#####","####","###"];var P=["{{person.firstName}}{{location.city_suffix}}","{{person.last_name.generic}}{{location.city_suffix}}"];var z=null;var E=[" do Descoberto"," de Nossa Senhora"," do Norte"," do Sul"];var B=["Afeganist\xE3o","Alb\xE2nia","Alg\xE9ria","Samoa","Andorra","Angola","Anguila","Antigua and Barbada","Argentina","Arm\xEAnia","Aruba","Austr\xE1lia","\xC1ustria","Azerbaij\xE3o","Bahamas","Bar\xE9m","Bangladesh","Barbados","B\xE9lgica","Belize","Benin","Bermuda","But\xE3o","Bol\xEDvia","B\xF4snia","Botsuana","Ilha Bouvet","Brasil","Arquip\xE9lago de Chagos","Ilhas Virgens","Brunei","Bulg\xE1ria","Burkina Faso","Burundi","Camboja","Camar\xF5es","Canad\xE1","Cabo Verde","Ilhas Caiman","Rep\xFAblica da \xC1frica Central","Chade","Chile","China","Ilha do Natal","Ilhas Cocos","Col\xF4mbia","Comores","Congo","Ilhas Cook","Costa Rica","Costa do Marfim","Cro\xE1cia","Cuba","Chipre","Rep\xFAblica Tcheca","Dinamarca","Jibuti","Dominica","Rep\xFAblica Dominicana","Equador","Egito","El Salvador","Guin\xE9 Equatorial","Eritreia","Est\xF4nia","Eti\xF3pia","Ilhas Faroe","Malvinas","Fiji","Finl\xE2ndia","Fran\xE7a","Guin\xE9 Francesa","Polin\xE9sia Francesa","Gab\xE3o","G\xE2mbia","Georgia","Alemanha","Gana","Gibraltar","Gr\xE9cia","Groel\xE2ndia","Granada","Guadalupe","Guatemala","Guernesey","Guin\xE9","Guin\xE9-Bissau","Guiana","Haiti","Ilhas Heard e McDonald","Vaticano","Honduras","Hong Kong","Hungria","Isl\xE2ndia","\xCDndia","Indon\xE9sia","Ir\xE3","Iraque","Irlanda","Ilha de Man","Israel","It\xE1lia","Jamaica","Jap\xE3o","Jersey","Jord\xE2nia","Cazaquist\xE3o","Qu\xEAnia","Quiribati","Coreia do Norte","Coreia do Sul","Kuwait","Quirguist\xE3o","Laos","Latvia","L\xEDbano","Lesoto","Lib\xE9ria","L\xEDbia","Liechtenstein","Litu\xE2nia","Luxemburgo","Macao","Maced\xF4nia","Madagascar","Malawi","Mal\xE1sia","Maldives","Mali","Malta","Ilhas Marshall","Martinica","Maurit\xE2nia","Maur\xEDcia","Maiote","M\xE9xico","Micron\xE9sia","Mold\xE1via","M\xF4naco","Mong\xF3lia","Montenegro","Montserrat","Marrocos","Mo\xE7ambique","Myanmar","Namibia","Nauru","Nepal","Antilhas Holandesas","Pa\xEDses Baixos","Nova Caledonia","Nova Zel\xE2ndia","Nicar\xE1gua","Nig\xE9ria","Niue","Ilha Norfolk","Marianas Setentrionais","Noruega","Om\xE3","Paquist\xE3o","Palau","Territ\xF3rio da Palestina","Panam\xE1","Papua-Nova Guin\xE9","Paraguai","Peru","Filipinas","Pol\xF4nia","Portugal","Porto Rico","Qatar","Rom\xEAnia","R\xFAssia","Ruanda","S\xE3o Bartolomeu","Santa Helena","Santa L\xFAcia","S\xE3o Martinho","S\xE3o Pedro e Miquel\xE3o","S\xE3o Vicente e Granadinas","San Marino","Sao Tom\xE9 e Pr\xEDncipe","Ar\xE1bia Saudita","Senegal","S\xE9rvia","Seicheles","Serra Leoa","Singapura","Eslov\xE1quia","Eslov\xEAnia","Ilhas Salom\xE3o","Som\xE1lia","\xC1frica do Sul","Ilhas Ge\xF3rgia do Sul e Sandwich do Sul","Espanha","Sri Lanka","Sud\xE3o","Suriname","Ilhas Svalbard & Jan Mayen","Suazil\xE2ndia","Su\xE9cia","Su\xED\xE7a","S\xEDria","Taiwan","Tajiquist\xE3o","Tanz\xE2nia","Tail\xE2ndia","Timor-Leste","Togo","Toquelau","Tonga","Trinidad e Tobago","Tun\xEDsia","Turquia","Turcomenist\xE3o","Turcas e Caicos","Tuvalu","Uganda","Ucr\xE2nia","Emirados \xC1rabes Unidos","Reino Unido","Estados Unidos da Am\xE9rica","Estados Unidos das Ilhas Virgens","Uruguai","Uzbequist\xE3o","Vanuatu","Venezuela","Vietn\xE3","Wallis e Futuna","I\xEAmen","Z\xE2mbia","Zimb\xE1bue"];var D=["#####-###"];var q=["Apto. ###","Sobrado ##","Casa #","Lote ##","Quadra ##"];var I=["Acre","Alagoas","Amap\xE1","Amazonas","Bahia","Cear\xE1","Distrito Federal","Esp\xEDrito Santo","Goi\xE1s","Maranh\xE3o","Mato Grosso","Mato Grosso do Sul","Minas Gerais","Par\xE1","Para\xEDba","Paran\xE1","Pernambuco","Piau\xED","Rio de Janeiro","Rio Grande do Norte","Rio Grande do Sul","Rond\xF4nia","Roraima","Santa Catarina","S\xE3o Paulo","Sergipe","Tocantins"];var R=["AC","AL","AP","AM","BA","CE","DF","ES","GO","MA","MT","MS","MG","PA","PB","PR","PE","PI","RJ","RN","RS","RO","RR","SC","SP","SE","TO"];var F=["{{location.street_prefix}} {{person.firstName}}","{{location.street_prefix}} {{person.lastName}}"];var G=["Rua","Avenida","Travessa","Alameda","Marginal","Rodovia"];var pa={building_number:L,city_pattern:P,city_prefix:z,city_suffix:E,country:B,postcode:D,secondary_address:q,state:I,state_abbr:R,street_pattern:F,street_prefix:G},y=pa;var N=["alias","consequatur","aut","perferendis","sit","voluptatem","accusantium","doloremque","aperiam","eaque","ipsa","quae","ab","illo","inventore","veritatis","et","quasi","architecto","beatae","vitae","dicta","sunt","explicabo","aspernatur","odit","fugit","sed","quia","consequuntur","magni","dolores","eos","qui","ratione","sequi","nesciunt","neque","dolorem","ipsum","dolor","amet","consectetur","adipisci","velit","non","numquam","eius","modi","tempora","incidunt","ut","labore","dolore","magnam","aliquam","quaerat","enim","ad","minima","veniam","quis","nostrum","exercitationem","ullam","corporis","nemo","ipsam","voluptas","suscipit","laboriosam","nisi","aliquid","ex","ea","commodi","autem","vel","eum","iure","reprehenderit","in","voluptate","esse","quam","nihil","molestiae","iusto","odio","dignissimos","ducimus","blanditiis","praesentium","laudantium","totam","rem","voluptatum","deleniti","atque","corrupti","quos","quas","molestias","excepturi","sint","occaecati","cupiditate","provident","perspiciatis","unde","omnis","iste","natus","error","similique","culpa","officia","deserunt","mollitia","animi","id","est","laborum","dolorum","fuga","harum","quidem","rerum","facilis","expedita","distinctio","nam","libero","tempore","cum","soluta","nobis","eligendi","optio","cumque","impedit","quo","porro","quisquam","minus","quod","maxime","placeat","facere","possimus","assumenda","repellendus","temporibus","quibusdam","illum","fugiat","nulla","pariatur","at","vero","accusamus","officiis","debitis","necessitatibus","saepe","eveniet","voluptates","repudiandae","recusandae","itaque","earum","hic","tenetur","a","sapiente","delectus","reiciendis","voluptatibus","maiores","doloribus","asperiores","repellat"];var fa={word:N},J=fa;var ga={title:"Portuguese (Brazil)",code:"pt_BR",country:"BR",language:"pt",endonym:"Portugu\xEAs (Brasil)",dir:"ltr",script:"Latn"},T=ga;var _={generic:["Alessandra","Alessandro","Alexandre","Alice","Aline","Al\xEDcia","Ana Clara","Ana J\xFAlia","Ana Laura","Ana Luiza","Anthony","Antonella","Ant\xF4nio","Arthur","Beatriz","Benjamin","Ben\xEDcio","Bernardo","Breno","Bruna","Bryan","Caio","Calebe","Carla","Carlos","Cau\xE3","Cec\xEDlia","Clara","C\xE9lia","C\xE9sar","Dalila","Daniel","Danilo","Davi","Davi Lucca","Deneval","Eduarda","Eduardo","Elisa","Elo\xE1","El\xEDsio","Emanuel","Emanuelly","Enzo","Enzo Gabriel","Esther","Fabiano","Fabr\xEDcia","Fabr\xEDcio","Feliciano","Felipe","Fel\xEDcia","Frederico","F\xE1bio","F\xE9lix","Gabriel","Gael","Giovanna","Guilherme","Gustavo","G\xFAbio","Heitor","Helena","Helo\xEDsa","Henrique","Hugo","H\xE9lio","Isaac","Isabel","Isabela","Isabella","Isabelly","Isadora","Isis","Jana\xEDna","Joana","Joaquim","Jo\xE3o","Jo\xE3o Lucas","Jo\xE3o Miguel","Jo\xE3o Pedro","J\xFAlia","J\xFAlio","J\xFAlio C\xE9sar","Karla","Kl\xE9ber","Ladislau","Lara","Larissa","Laura","Lav\xEDnia","Leonardo","Liz","Lorena","Lorenzo","Lorraine","Lucas","Lucca","Luiza","L\xEDvia","Mait\xEA","Manuela","Marcela","Marcelo","Marcos","Margarida","Maria","Maria Alice","Maria Cec\xEDlia","Maria Clara","Maria Eduarda","Maria Helena","Maria J\xFAlia","Maria Luiza","Mariana","Marina","Marli","Matheus","Meire","Melissa","Miguel","Morgana","Murilo","M\xE1rcia","M\xE9rcia","Nataniel","Nat\xE1lia","Nicolas","Noah","Norberto","N\xFAbia","Of\xE9lia","Pablo","Paula","Paulo","Pedro","Pedro Henrique","Pietro","Rafael","Rafaela","Raul","Rebeca","Ricardo","Roberta","Roberto","Salvador","Samuel","Sara","Sarah","Silas","Sirineu","Sophia","Su\xE9len","S\xEDlvia","Talita","Tertuliano","Th\xE9o","Valentina","Vicente","Vitor","Vit\xF3ria","V\xEDctor","Warley","Washington","Yago","Yango","Yasmin","Yuri","\xCDgor"],female:["Alessandra","Alice","Aline","Al\xEDcia","Ana Clara","Ana J\xFAlia","Ana Laura","Ana Luiza","Antonella","Beatriz","Bruna","Carla","Cec\xEDlia","Clara","C\xE9lia","Dalila","Eduarda","Elisa","Elo\xE1","Emanuelly","Esther","Fabr\xEDcia","Fel\xEDcia","Giovanna","Helena","Helo\xEDsa","Isabel","Isabela","Isabella","Isabelly","Isadora","Isis","Jana\xEDna","Joana","J\xFAlia","Karla","Lara","Larissa","Laura","Lav\xEDnia","Liz","Lorena","Lorraine","Luiza","L\xEDvia","Mait\xEA","Manuela","Marcela","Margarida","Maria","Maria Alice","Maria Cec\xEDlia","Maria Clara","Maria Eduarda","Maria Helena","Maria J\xFAlia","Maria Luiza","Mariana","Marina","Marli","Meire","Melissa","Morgana","M\xE1rcia","M\xE9rcia","Nat\xE1lia","N\xFAbia","Of\xE9lia","Paula","Rafaela","Rebeca","Roberta","Sara","Sarah","Sophia","Su\xE9len","S\xEDlvia","Talita","Valentina","Vit\xF3ria","Yasmin"],male:["Alessandro","Alexandre","Anthony","Ant\xF4nio","Arthur","Benjamin","Ben\xEDcio","Bernardo","Breno","Bryan","Caio","Calebe","Carlos","Cau\xE3","C\xE9sar","Daniel","Danilo","Davi","Davi Lucca","Deneval","Eduardo","El\xEDsio","Emanuel","Enzo","Enzo Gabriel","Fabiano","Fabr\xEDcio","Feliciano","Felipe","Frederico","F\xE1bio","F\xE9lix","Gabriel","Gael","Guilherme","Gustavo","G\xFAbio","Heitor","Henrique","Hugo","H\xE9lio","Isaac","Joaquim","Jo\xE3o","Jo\xE3o Lucas","Jo\xE3o Miguel","Jo\xE3o Pedro","J\xFAlio","J\xFAlio C\xE9sar","Kl\xE9ber","Ladislau","Leonardo","Lorenzo","Lucas","Lucca","Marcelo","Marcos","Matheus","Miguel","Murilo","Nataniel","Nicolas","Noah","Norberto","Pablo","Paulo","Pedro","Pedro Henrique","Pietro","Rafael","Raul","Ricardo","Roberto","Salvador","Samuel","Silas","Sirineu","Tertuliano","Th\xE9o","Vicente","Vitor","V\xEDctor","Warley","Washington","Yago","Yango","Yuri","\xCDgor"]};var j=["Solu\xE7\xF5es","Programa","Marca","Seguran\xE7a","Pesquisar","Marketing","Diretivas","Implementation","Implementa\xE7\xE3o","Funcionalidade","Resposta","Paradigma","T\xE1ticas","Identidade","Mercados","Grupo","Divis\xE3o","Aplica\xE7\xF5es","Otimiza\xE7\xE3o","Opera\xE7\xF5es","Infraestrutura","Intranet","Comunica\xE7\xF5es","Web","Branding","Qualidade","Assurance","Mobilidade","Contas","Dados","Criativo","Configuration","Presta\xE7\xE3o de contas","Intera\xE7\xF5es","Fatores","Usabilidade","M\xE9tricas"];var O=["L\xEDder","Senior","Direto","Corporativo","Din\xE2mico","Futuro","Produto","Nacional","Regional","Distrito","Central","Global","Cliente","Investidor","International","Legado","Avan\xE7ar","Interno","Humano","Chefe","Principal"];var V=["Supervisor","Associado","Executivo","Atentende","Policial","Gerente","Engenheiro","Especialista","Diretor","Coordenador","Administrador","Arquiteto","Analista","Designer","Planejador","Orquestrador","T\xE9cnico","Desenvolvedor","Produtor","Consultor","Assistente","Facilitador","Agente","Representante","Estrategista"];var H={generic:["Albuquerque","Barros","Batista","Braga","Carvalho","Costa","Franco","Macedo","Martins","Melo","Moraes","Moreira","Nogueira","Oliveira","Pereira","Reis","Santos","Saraiva","Silva","Souza","Xavier"]};var k={generic:[{value:"{{person.last_name.generic}}",weight:1}]};var w=[{value:"{{person.prefix}} {{person.firstName}} {{person.lastName}}",weight:1},{value:"{{person.firstName}} {{person.lastName}} {{person.suffix}}",weight:1},{value:"{{person.firstName}} {{person.lastName}}",weight:8}];var Q={generic:["Dr.","Dra.","Sr.","Sra.","Srta."],female:["Dra.","Sra.","Srta."],male:["Dr.","Sr."]};var U=["Feminino","Masculino"];var W=["Jr.","Neto","Filho"];var Y=["Aqu\xE1rio","Peixes","\xC1ries","Touro","G\xEAmeos","C\xE2ncer","Le\xE3o","Virgem","Libra","Escorpi\xE3o","Sagit\xE1rio","Capric\xF3rnio"];var ba={first_name:_,job_area:j,job_descriptor:O,job_type:V,last_name:H,last_name_pattern:k,name:w,prefix:Q,sex:U,suffix:W,western_zodiac_sign:Y},K=ba;var Z=["(##) ####-####","+55 (##) ####-####","(##) #####-####"];var X=["+55##########","+55###########"];var $=["(##) ####-####","(##) #####-####"];var va={human:Z,international:X,national:$},aa=va;var ha={format:aa},ra=ha;var ea=["aberto","afiado","alegre","alto","amargo","anormal","ansioso","art\xEDstico","ativo","azedo","baixo","barato","barulhento","belo","bom","brilhante","burro","calmo","cansado","caro","cego","celestial","chato","cheio","cient\xEDfico","claro","colorido","complexo","confiante","consciente","contente","corajoso","criativo","curioso","decidido","desonesto","desorganizado","destrutivo","dif\xEDcil","disposto","divino","doce","doente","duro","educado","ego\xEDsta","energ\xE9tico","engra\xE7ado","escuro","estranho","estreito","extrovertido","falso","familiar","fechado","feio","feliz","flex\xEDvel","forte","fosco","fraco","frio","fr\xE1gil","furioso","f\xE1cil","gasoso","generoso","gentil","gordo","grande","honesto","humano","impaciente","inconsciente","indeciso","indiferente","insatisfeito","inteligente","introvertido","invis\xEDvel","irrespons\xE1vel","jovem","largo","lento","leve","limpo","liso","livre","l\xEDquido","macio","magro","mal-educado","mau","medroso","mentiroso","molhado","monocrom\xE1tico","musculoso","musical","nervoso","normal","novo","oco","ocupado","opaco","organizado","otimista","oval","paciente","passivo","pequeno","perigoso","pesado","pessimista","pobre","pontudo","preocupado","quadrado","quente","quieto","redondo","relaxado","resistente","respons\xE1vel","retangular","rico","rude","r\xE1pido","r\xEDgido","salgado","satisfeito","saud\xE1vel","seco","seguro","simples","sincero","sujo","s\xF3lido","terrestre","transparente","triangular","triste","t\xEDmido","vazio","velho","verdadeiro","vis\xEDvel","\xE1spero"];var oa=["abajur","advogado","aeroporto","agenda","alum\xEDnio","aluno","alvorecer","amigo","anivers\xE1rio","ano","aplicativo","areia","arm\xE1rio","arroz","artista","atmosfera","ator","atriz","avi\xE3o","azeite","a\xE7o","bairro","barco","barriga","biblioteca","bicho","bicicleta","blog","boca","boi","boleto","bolo","bombeiro","borracha","bra\xE7o","brinquedo","brisa","cabe\xE7a","cachoeira","cachorro","cadeira","caderno","caf\xE9","calend\xE1rio","calor","cal\xE7a","caminh\xE3o","camisa","campanha","caneta","canoa","cantor","carne","carro","carro\xE7a","carteira","cart\xE3o","casa","cavalo","chave","cheque","chocolate","chuva","ch\xE1","ch\xE3o","cidade","cimento","cinema","clima","cobertor","cobra","coelho","colch\xE3o","computador","congresso","constitui\xE7\xE3o","conta","controle","copo","cora\xE7\xE3o","costas","cozinheiro","crep\xFAsculo","crian\xE7a","cr\xE9dito","c\xE9rebro","c\xE9u","data","dedo","democracia","dente","dentista","deputado","desconto","dia","dinheiro","doce","d\xE9bito","d\xE9cada","d\xEDvida","economia","edredom","elefante","elei\xE7\xE3o","email","encontro","engenheiro","entardecer","escada","escola","escrit\xF3rio","espelho","estante","estojo","estrela","est\xE1dio","est\xF4mago","evento","fatura","fazenda","feij\xE3o","feriado","ferro","festa","flor","fogo","folha","frango","frio","fruta","furac\xE3o","galinha","galo","garrafa","gar\xE7om","gasto","gato","gaveta","giz","governo","grama","granizo","hamb\xFArguer","hardware","hashtag","helic\xF3ptero","homem","hora","horizonte","hospital","hotel","igreja","imposto","inimigo","internet","interruptor","irm\xE3","irm\xE3o","jacar\xE9","janela","joelho","juros","lago","lei","leite","len\xE7ol","le\xE3o","link","livraria","livro","lixeira","login","loja","lua","lucro","luz","l\xE2mpada","l\xEDngua","macaco","mandato","manh\xE3","manteiga","mar","massa","mel","meme","mensagem","mercado","mesa","metr\xF4","ministro","minuto","mochila","moeda","montanha","moto","motorista","mulher","m\xE3e","m\xE3o","m\xE9dico","m\xEAs","nariz","navio","neblina","neve","noite","nota","not\xEDcia","nuvem","oceano","olho","ombro","osso","ouvido","ovelha","ovo","padaria","pai","papel","papel\xE3o","paredes","parque","partido","patinete","pato","pedra","peixe","pele","perfil","perna","pesco\xE7o","pix","pizza","pl\xE1stico","podcast","poder","policial","pol\xEDtica","porco","porta","praia","pra\xE7a","prefeito","presidente","pre\xE7o","professor","p\xE1ssaro","p\xE3o","p\xE9","quadro","queijo","raio","raiz","ralo","rato","rede","refrigerante","rel\xE2mpago","rel\xF3gio","rep\xFAblica","restaurante","reuni\xE3o","rio","rocha","rodovi\xE1ria","rosto","salada","sal\xE1rio","sangue","sapato","segundo","semana","senado","senha","site","skate","software","sof\xE1","sol","sombra","sopa","sorvete","streaming","suco","supermercado","s\xE9culo","tapete","tarde","tartaruga","teatro","tecido","telefone","tempestade","tempo","terra","teto","tigre","tijolo","tomada","tornado","trator","travesseiro","trem","trov\xE3o","tv","t\xE1xi","uber","unha","universidade","urna","urso","vaca","vag\xE3o","vela","vento","vereador","vidro","vizinho","voto","v\xEDdeo","v\xEDrus","zebra","\xE1guia","\xE1rvore","\xF3culos","\xF4nibus"];var ia=["abra\xE7ar","abrir","acariciar","aceitar","acelerar","acender","acordar","acreditar","acusar","administrar","adoecer","afirmar","alegrar","alugar","amar","analisar","andar","animar","anotar","anunciar","apagar","aparecer","apertar","apitar","apostar","aprender","arrastar","arremessar","arrendar","arrumar","assar","assistir","atacar","atirar","atualizar","avaliar","baixar","balan\xE7ar","beber","beijar","beliscar","bloquear","brilhar","brincar","brotar","buscar","cair","calcular","carregar","categorizar","cavar","chegar","cheirar","chorar","chutar","classificar","clicar","cobrar","colar","colher","colorir","comentar","comer","comparar","compartilhar","competir","comprar","comunicar","conectar","confiar","confirmar","congelar","considerar","construir","consultar","contar","conversar","correr","cortar","cotar","cozinhar","co\xE7ar","crescer","criar","cuidar","cultivar","curar","curtir","cutucar","defender","degustar","demolir","denunciar","derreter","derrubar","desaparecer","descer","desconectar","desconfiar","descongelar","descrever","desenhar","desinstalar","desligar","deslizar","deslogar","detestar","devolver","diagnosticar","digitalizar","dirigir","dizer","doar","dobrar","dormir","driblar","duvidar","economizar","editar","eleger","elogiar","emocionar","emprestar","empurrar","encaminhar","encostar","ensinar","entrar","entristecer","envergonhar","enviar","enxergar","esbarrar","escanear","esconder","escrever","esculpir","escutar","esperar","espirrar","esquecer","esquentar","esticar","estudar","examinar","excluir","experimentar","expirar","explicar","falar","fechar","ferver","ficar","financiar","florescer","formatar","frear","fritar","ganhar","gastar","germinar","girar","gostar","governar","grelhar","gritar","guardar","identificar","iluminar","imaginar","imprimir","inspirar","instalar","internar","investigar","investir","jogar","julgar","lamber","lan\xE7ar","lavar","legislar","lembrar","ler","levantar","ligar","limpar","logar","manifestar","marcar","medir","meditar","melhorar","memorizar","mergulhar","misturar","modelar","monitorar","morder","mudar","multar","nadar","navegar","negar","negociar","nivelar","nomear","notar","observar","odiar","oferecer","olhar","operar","organizar","orgulhar","ouvir","pagar","parar","participar","partir","passar","pensar","perceber","perder","perguntar","pesquisar","pintar","piorar","planejar","plantar","pontuar","postar","preocupar","prescrever","presidir","processar","produzir","programar","projetar","prometer","protestar","provar","pular","punir","puxar","queimar","rebater","receber","receitar","reclamar","recusar","refletir","refogar","regar","reivindicar","rejeitar","relembrar","representar","resfriar","resolver","respirar","responder","resumir","reunir","revisar","rir","sair","salvar","sangrar","secar","seguir","selecionar","sentar","sentir","servir","sofrer","soltar","sonhar","suar","subir","sumir","surpreender","sussurrar","temer","temperar","testar","tirar","tossir","trabalhar","transformar","transpirar","tratar","treinar","trocar","trope\xE7ar","usar","vacinar","validar","varrer","vender","ver","vestir","vetar","viajar","vibrar","vigiar","voar","voltar","votar"];var Ma={adjective:ea,noun:oa,verb:ia},ta=Ma;var Ca={book:u,color:d,commerce:f,company:v,date:C,internet:S,location:y,lorem:J,metadata:T,person:K,phone_number:ra,word:ta},na=Ca;var Re=new a({locale:[na,e,r]});export{na as a,Re as b};

var VueDemi=function(a,i,E){if(a.install)return a;if(!i)return console.error("[vue-demi] no Vue instance found, please be sure to import `vue` before `vue-demi`."),a;if(i.version.slice(0,4)==="2.7."){let O=function(P,A){var m,N={},W={config:i.config,use:i.use.bind(i),mixin:i.mixin.bind(i),component:i.component.bind(i),provide:function($,S){return N[$]=S,this},directive:function($,S){return S?(i.directive($,S),W):i.directive($)},mount:function($,S){return m||(m=new i(Object.assign({propsData:A},P,{provide:Object.assign(N,P.provide)})),m.$mount($,S),m)},unmount:function(){m&&(m.$destroy(),m=void 0)}};return W};var zt=O;for(var b in i)a[b]=i[b];a.isVue2=!0,a.isVue3=!1,a.install=function(){},a.Vue=i,a.Vue2=i,a.version=i.version,a.warn=i.util.warn,a.createApp=O}else if(i.version.slice(0,2)==="2.")if(E){for(var b in E)a[b]=E[b];a.isVue2=!0,a.isVue3=!1,a.install=function(){},a.Vue=i,a.Vue2=i,a.version=i.version}else console.error("[vue-demi] no VueCompositionAPI instance found, please be sure to import `@vue/composition-api` before `vue-demi`.");else if(i.version.slice(0,2)==="3."){for(var b in i)a[b]=i[b];a.isVue2=!1,a.isVue3=!0,a.install=function(){},a.Vue=i,a.Vue2=void 0,a.version=i.version,a.set=function(O,P,A){return Array.isArray(O)?(O.length=Math.max(O.length,P),O.splice(P,1,A),A):(O[P]=A,A)},a.del=function(O,P){if(Array.isArray(O)){O.splice(P,1);return}delete O[P]}}else console.error("[vue-demi] Vue version "+i.version+" is unsupported.");return a}(this.VueDemi=this.VueDemi||(typeof VueDemi!="undefined"?VueDemi:{}),this.Vue||(typeof Vue!="undefined"?Vue:void 0),this.VueCompositionAPI||(typeof VueCompositionAPI!="undefined"?VueCompositionAPI:void 0));(function(a,i){"use strict";var E=Object.defineProperty,b=Object.defineProperties,zt=Object.getOwnPropertyDescriptors,O=Object.getOwnPropertySymbols,P=Object.prototype.hasOwnProperty,A=Object.prototype.propertyIsEnumerable,m=(t,e,n)=>e in t?E(t,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[e]=n,N=(t,e)=>{for(var n in e||(e={}))P.call(e,n)&&m(t,n,e[n]);if(O)for(var n of O(e))A.call(e,n)&&m(t,n,e[n]);return t},W=(t,e)=>b(t,zt(e));function $(t,e){var n;const r=i.shallowRef();return i.watchEffect(()=>{r.value=t()},W(N({},e),{flush:(n=e==null?void 0:e.flush)!=null?n:"sync"})),i.readonly(r)}var S;const M=typeof window!="undefined",Zt=t=>typeof t!="undefined",qt=(t,...e)=>{t||console.warn(...e)},x=Object.prototype.toString,Jt=t=>typeof t=="boolean",U=t=>typeof t=="function",Xt=t=>typeof t=="number",Kt=t=>typeof t=="string",Qt=t=>x.call(t)==="[object Object]",Vt=t=>typeof window!="undefined"&&x.call(t)==="[object Window]",Dt=()=>Date.now(),tt=()=>+Date.now(),xt=(t,e,n)=>Math.min(n,Math.max(e,t)),F=()=>{},te=(t,e)=>(t=Math.ceil(t),e=Math.floor(e),Math.floor(Math.random()*(e-t+1))+t),ee=M&&((S=window==null?void 0:window.navigator)==null?void 0:S.userAgent)&&/iP(ad|hone|od)/.test(window.navigator.userAgent),ne=(t,e)=>Object.prototype.hasOwnProperty.call(t,e);function y(t){return typeof t=="function"?t():i.unref(t)}function R(t,e){function n(...r){return new Promise((o,l)=>{Promise.resolve(t(()=>e.apply(this,r),{fn:e,thisArg:this,args:r})).then(o).catch(l)})}return n}const L=t=>t();function z(t,e={}){let n,r,o=F;const l=c=>{clearTimeout(c),o(),o=F};return c=>{const d=y(t),_=y(e.maxWait);return n&&l(n),d<=0||_!==void 0&&_<=0?(r&&(l(r),r=null),Promise.resolve(c())):new Promise((s,f)=>{o=e.rejectOnCancel?f:s,_&&!r&&(r=setTimeout(()=>{n&&l(n),r=null,s(c())},_)),n=setTimeout(()=>{r&&l(r),r=null,s(c())},d)})}}function Z(t,e=!0,n=!0,r=!1){let o=0,l,u=!0,c=F,d;const _=()=>{l&&(clearTimeout(l),l=void 0,c(),c=F)};return f=>{const p=y(t),h=Date.now()-o,v=()=>d=f();return _(),p<=0?(o=Date.now(),v()):(h>p&&(n||!u)?(o=Date.now(),v()):e&&(d=new Promise((w,g)=>{c=r?g:w,l=setTimeout(()=>{o=Date.now(),u=!0,w(v()),_()},Math.max(0,p-h))})),!n&&!l&&(l=setTimeout(()=>u=!0,p)),u=!1,d)}}function et(t=L){const e=i.ref(!0);function n(){e.value=!1}function r(){e.value=!0}const o=(...l)=>{e.value&&t(...l)};return{isActive:i.readonly(e),pause:n,resume:r,eventFilter:o}}function re(t="this function"){if(!i.isVue3)throw new Error(`[VueUse] ${t} is only works on Vue 3.`)}function nt(t="this function"){if(!(i.isVue3||i.version.startsWith("2.7.")))throw new Error(`[VueUse] ${t} is only works on Vue 2.7 or above.`)}const oe={mounted:i.isVue3?"mounted":"inserted",updated:i.isVue3?"updated":"componentUpdated",unmounted:i.isVue3?"unmounted":"unbind"};function q(t,e=!1,n="Timeout"){return new Promise((r,o)=>{setTimeout(e?()=>o(n):r,t)})}function ae(t){return t}function ie(t){let e;function n(){return e||(e=t()),e}return n.reset=async()=>{const r=e;e=void 0,r&&await r},n}function le(t){return t()}function ue(t,...e){return e.some(n=>n in t)}function ce(t,e){var n;if(typeof t=="number")return t+e;const r=((n=t.match(/^-?[0-9]+\.?[0-9]*/))==null?void 0:n[0])||"",o=t.slice(r.length),l=parseFloat(r)+e;return Number.isNaN(l)?t:l+o}function se(t,e,n=!1){return e.reduce((r,o)=>(o in t&&(!n||t[o]!==void 0)&&(r[o]=t[o]),r),{})}function rt(t,e){let n,r,o;const l=i.ref(!0),u=()=>{l.value=!0,o()};i.watch(t,u,{flush:"sync"});const c=U(e)?e:e.get,d=U(e)?void 0:e.set,_=i.customRef((s,f)=>(r=s,o=f,{get(){return l.value&&(n=c(),l.value=!1),r(),n},set(p){d==null||d(p)}}));return Object.isExtensible(_)&&(_.trigger=u),_}function j(t){return i.getCurrentScope()?(i.onScopeDispose(t),!0):!1}function fe(){const t=[],e=o=>{const l=t.indexOf(o);l!==-1&&t.splice(l,1)};return{on:o=>{t.push(o);const l=()=>e(o);return j(l),{off:l}},off:e,trigger:o=>{t.forEach(l=>l(o))}}}function de(t){let e=!1,n;const r=i.effectScope(!0);return()=>(e||(n=r.run(t),e=!0),n)}function pe(t){const e=Symbol("InjectionState");return[(...o)=>{const l=t(...o);return i.provide(e,l),l},()=>i.inject(e)]}function ye(t){let e=0,n,r;const o=()=>{e-=1,r&&e<=0&&(r.stop(),n=void 0,r=void 0)};return(...l)=>(e+=1,n||(r=i.effectScope(!0),n=r.run(()=>t(...l))),j(o),n)}function ot(t,e,{enumerable:n=!1,unwrap:r=!0}={}){nt();for(const[o,l]of Object.entries(e))o!=="value"&&(i.isRef(l)&&r?Object.defineProperty(t,o,{get(){return l.value},set(u){l.value=u},enumerable:n}):Object.defineProperty(t,o,{value:l,enumerable:n}));return t}function _e(t,e){return e==null?i.unref(t):i.unref(t)[e]}function ve(t){return i.unref(t)!=null}var he=Object.defineProperty,at=Object.getOwnPropertySymbols,Oe=Object.prototype.hasOwnProperty,we=Object.prototype.propertyIsEnumerable,it=(t,e,n)=>e in t?he(t,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[e]=n,ge=(t,e)=>{for(var n in e||(e={}))Oe.call(e,n)&&it(t,n,e[n]);if(at)for(var n of at(e))we.call(e,n)&&it(t,n,e[n]);return t};function Pe(t,e){if(typeof Symbol!="undefined"){const n=ge({},t);return Object.defineProperty(n,Symbol.iterator,{enumerable:!1,value(){let r=0;return{next:()=>({value:e[r++],done:r>e.length})}}}),n}else return Object.assign([...e],t)}function J(t,e){const n=(e==null?void 0:e.computedGetter)===!1?i.unref:y;return function(...r){return i.computed(()=>t.apply(this,r.map(o=>n(o))))}}function me(t,e={}){let n=[],r;if(Array.isArray(e))n=e;else{r=e;const{includeOwnProperties:o=!0}=e;n.push(...Object.keys(t)),o&&n.push(...Object.getOwnPropertyNames(t))}return Object.fromEntries(n.map(o=>{const l=t[o];return[o,typeof l=="function"?J(l.bind(t),r):l]}))}function lt(t){if(!i.isRef(t))return i.reactive(t);const e=new Proxy({},{get(n,r,o){return i.unref(Reflect.get(t.value,r,o))},set(n,r,o){return i.isRef(t.value[r])&&!i.isRef(o)?t.value[r].value=o:t.value[r]=o,!0},deleteProperty(n,r){return Reflect.deleteProperty(t.value,r)},has(n,r){return Reflect.has(t.value,r)},ownKeys(){return Object.keys(t.value)},getOwnPropertyDescriptor(){return{enumerable:!0,configurable:!0}}});return i.reactive(e)}function ut(t){return lt(i.computed(t))}function be(t,...e){const n=e.flat();return ut(()=>Object.fromEntries(Object.entries(i.toRefs(t)).filter(r=>!n.includes(r[0]))))}function $e(t,...e){const n=e.flat();return i.reactive(Object.fromEntries(n.map(r=>[r,i.toRef(t,r)])))}function ct(t,e=1e4){return i.customRef((n,r)=>{let o=t,l;const u=()=>setTimeout(()=>{o=t,r()},y(e));return j(()=>{clearTimeout(l)}),{get(){return n(),o},set(c){o=c,r(),clearTimeout(l),l=u()}}})}function st(t,e=200,n={}){return R(z(e,n),t)}function X(t,e=200,n={}){const r=i.ref(t.value),o=st(()=>{r.value=t.value},e,n);return i.watch(t,()=>o()),r}function Se(t,e){return i.computed({get(){var n;return(n=t.value)!=null?n:e},set(n){t.value=n}})}function ft(t,e=200,n=!1,r=!0,o=!1){return R(Z(e,n,r,o),t)}function K(t,e=200,n=!0,r=!0){if(e<=0)return t;const o=i.ref(t.value),l=ft(()=>{o.value=t.value},e,n,r);return i.watch(t,()=>l()),o}function dt(t,e={}){let n=t,r,o;const l=i.customRef((p,h)=>(r=p,o=h,{get(){return u()},set(v){c(v)}}));function u(p=!0){return p&&r(),n}function c(p,h=!0){var v,w;if(p===n)return;const g=n;((v=e.onBeforeChange)==null?void 0:v.call(e,p,g))!==!1&&(n=p,(w=e.onChanged)==null||w.call(e,p,g),h&&o())}return ot(l,{get:u,set:c,untrackedGet:()=>u(!1),silentSet:p=>c(p,!1),peek:()=>u(!1),lay:p=>c(p,!1)},{enumerable:!0})}const Ae=dt;function je(t){return typeof t=="function"?i.computed(t):i.ref(t)}function Fe(...t){if(t.length===2){const[e,n]=t;e.value=n}if(t.length===3)if(i.isVue2)i.set(...t);else{const[e,n,r]=t;e[n]=r}}function Ie(t,e,n={}){var r,o;const{flush:l="sync",deep:u=!1,immediate:c=!0,direction:d="both",transform:_={}}=n;let s,f;const p=(r=_.ltr)!=null?r:v=>v,h=(o=_.rtl)!=null?o:v=>v;return(d==="both"||d==="ltr")&&(s=i.watch(t,v=>e.value=p(v),{flush:l,deep:u,immediate:c})),(d==="both"||d==="rtl")&&(f=i.watch(e,v=>t.value=h(v),{flush:l,deep:u,immediate:c})),()=>{s==null||s(),f==null||f()}}function Te(t,e,n={}){const{flush:r="sync",deep:o=!1,immediate:l=!0}=n;return Array.isArray(e)||(e=[e]),i.watch(t,u=>e.forEach(c=>c.value=u),{flush:r,deep:o,immediate:l})}var Ee=Object.defineProperty,Me=Object.defineProperties,Re=Object.getOwnPropertyDescriptors,pt=Object.getOwnPropertySymbols,Ce=Object.prototype.hasOwnProperty,Ne=Object.prototype.propertyIsEnumerable,yt=(t,e,n)=>e in t?Ee(t,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[e]=n,We=(t,e)=>{for(var n in e||(e={}))Ce.call(e,n)&&yt(t,n,e[n]);if(pt)for(var n of pt(e))Ne.call(e,n)&&yt(t,n,e[n]);return t},Ue=(t,e)=>Me(t,Re(e));function Le(t){if(!i.isRef(t))return i.toRefs(t);const e=Array.isArray(t.value)?new Array(t.value.length):{};for(const n in t.value)e[n]=i.customRef(()=>({get(){return t.value[n]},set(r){if(Array.isArray(t.value)){const o=[...t.value];o[n]=r,t.value=o}else{const o=Ue(We({},t.value),{[n]:r});Object.setPrototypeOf(o,t.value),t.value=o}}}));return e}function Be(t,e=!0){i.getCurrentInstance()?i.onBeforeMount(t):e?t():i.nextTick(t)}function He(t){i.getCurrentInstance()&&i.onBeforeUnmount(t)}function ke(t,e=!0){i.getCurrentInstance()?i.onMounted(t):e?t():i.nextTick(t)}function Ye(t){i.getCurrentInstance()&&i.onUnmounted(t)}function Q(t,e=!1){function n(f,{flush:p="sync",deep:h=!1,timeout:v,throwOnTimeout:w}={}){let g=null;const D=[new Promise(G=>{g=i.watch(t,T=>{f(T)!==e&&(g==null||g(),G(T))},{flush:p,deep:h,immediate:!0})})];return v!=null&&D.push(q(v,w).then(()=>y(t)).finally(()=>g==null?void 0:g())),Promise.race(D)}function r(f,p){if(!i.isRef(f))return n(T=>T===f,p);const{flush:h="sync",deep:v=!1,timeout:w,throwOnTimeout:g}=p??{};let I=null;const G=[new Promise(T=>{I=i.watch([t,f],([Gt,fr])=>{e!==(Gt===fr)&&(I==null||I(),T(Gt))},{flush:h,deep:v,immediate:!0})})];return w!=null&&G.push(q(w,g).then(()=>y(t)).finally(()=>(I==null||I(),y(t)))),Promise.race(G)}function o(f){return n(p=>Boolean(p),f)}function l(f){return r(null,f)}function u(f){return r(void 0,f)}function c(f){return n(Number.isNaN,f)}function d(f,p){return n(h=>{const v=Array.from(h);return v.includes(f)||v.includes(y(f))},p)}function _(f){return s(1,f)}function s(f=1,p){let h=-1;return n(()=>(h+=1,h>=f),p)}return Array.isArray(y(t))?{toMatch:n,toContains:d,changed:_,changedTimes:s,get not(){return Q(t,!e)}}:{toMatch:n,toBe:r,toBeTruthy:o,toBeNull:l,toBeNaN:c,toBeUndefined:u,changed:_,changedTimes:s,get not(){return Q(t,!e)}}}function Ge(t){return Q(t)}function ze(t,e){return i.computed(()=>y(t).every((n,r,o)=>e(y(n),r,o)))}function Ze(t,e){return i.computed(()=>y(t).map(n=>y(n)).filter(e))}function qe(t,e){return i.computed(()=>y(y(t).find((n,r,o)=>e(y(n),r,o))))}function Je(t,e){return i.computed(()=>y(t).findIndex((n,r,o)=>e(y(n),r,o)))}function Xe(t,e){let n=t.length;for(;n-- >0;)if(e(t[n],n,t))return t[n]}function Ke(t,e){return i.computed(()=>y(Array.prototype.findLast?y(t).findLast((n,r,o)=>e(y(n),r,o)):Xe(y(t),(n,r,o)=>e(y(n),r,o))))}function Qe(t,e){return i.computed(()=>y(t).map(n=>y(n)).join(y(e)))}function Ve(t,e){return i.computed(()=>y(t).map(n=>y(n)).map(e))}function De(t,e,...n){const r=(o,l,u)=>e(y(o),y(l),u);return i.computed(()=>{const o=y(t);return n.length?o.reduce(r,y(n[0])):o.reduce(r)})}function xe(t,e){return i.computed(()=>y(t).some((n,r,o)=>e(y(n),r,o)))}function tn(t){return i.computed(()=>[...new Set(y(t).map(e=>y(e)))])}function en(t=0,e={}){const n=i.ref(t),{max:r=1/0,min:o=-1/0}=e,l=(s=1)=>n.value=Math.min(r,n.value+s),u=(s=1)=>n.value=Math.max(o,n.value-s),c=()=>n.value,d=s=>n.value=Math.max(o,Math.min(r,s));return{count:n,inc:l,dec:u,get:c,set:d,reset:(s=t)=>(t=s,d(s))}}const nn=/^(\d{4})[-/]?(\d{1,2})?[-/]?(\d{0,2})[Tt\s]*(\d{1,2})?:?(\d{1,2})?:?(\d{1,2})?[.:]?(\d+)?$/,rn=/\[([^\]]+)]|Y{1,4}|M{1,4}|D{1,2}|d{1,4}|H{1,2}|h{1,2}|a{1,2}|A{1,2}|m{1,2}|s{1,2}|Z{1,2}|SSS/g,on=(t,e,n,r)=>{let o=t<12?"AM":"PM";return r&&(o=o.split("").reduce((l,u)=>l+=`${u}.`,"")),n?o.toLowerCase():o},_t=(t,e,n={})=>{var r;const o=t.getFullYear(),l=t.getMonth(),u=t.getDate(),c=t.getHours(),d=t.getMinutes(),_=t.getSeconds(),s=t.getMilliseconds(),f=t.getDay(),p=(r=n.customMeridiem)!=null?r:on,h={YY:()=>String(o).slice(-2),YYYY:()=>o,M:()=>l+1,MM:()=>`${l+1}`.padStart(2,"0"),MMM:()=>t.toLocaleDateString(n.locales,{month:"short"}),MMMM:()=>t.toLocaleDateString(n.locales,{month:"long"}),D:()=>String(u),DD:()=>`${u}`.padStart(2,"0"),H:()=>String(c),HH:()=>`${c}`.padStart(2,"0"),h:()=>`${c%12||12}`.padStart(1,"0"),hh:()=>`${c%12||12}`.padStart(2,"0"),m:()=>String(d),mm:()=>`${d}`.padStart(2,"0"),s:()=>String(_),ss:()=>`${_}`.padStart(2,"0"),SSS:()=>`${s}`.padStart(3,"0"),d:()=>f,dd:()=>t.toLocaleDateString(n.locales,{weekday:"narrow"}),ddd:()=>t.toLocaleDateString(n.locales,{weekday:"short"}),dddd:()=>t.toLocaleDateString(n.locales,{weekday:"long"}),A:()=>p(c,d),AA:()=>p(c,d,!1,!0),a:()=>p(c,d,!0),aa:()=>p(c,d,!0,!0)};return e.replace(rn,(v,w)=>w||h[v]())},vt=t=>{if(t===null)return new Date(NaN);if(t===void 0)return new Date;if(t instanceof Date)return new Date(t);if(typeof t=="string"&&!/Z$/i.test(t)){const e=t.match(nn);if(e){const n=e[2]-1||0,r=(e[7]||"0").substring(0,3);return new Date(e[1],n,e[3]||1,e[4]||0,e[5]||0,e[6]||0,r)}}return new Date(t)};function an(t,e="HH:mm:ss",n={}){return i.computed(()=>_t(vt(y(t)),y(e),n))}function ht(t,e=1e3,n={}){const{immediate:r=!0,immediateCallback:o=!1}=n;let l=null;const u=i.ref(!1);function c(){l&&(clearInterval(l),l=null)}function d(){u.value=!1,c()}function _(){const s=y(e);s<=0||(u.value=!0,o&&t(),c(),l=setInterval(t,s))}if(r&&M&&_(),i.isRef(e)||U(e)){const s=i.watch(e,()=>{u.value&&M&&_()});j(s)}return j(d),{isActive:u,pause:d,resume:_}}var ln=Object.defineProperty,Ot=Object.getOwnPropertySymbols,un=Object.prototype.hasOwnProperty,cn=Object.prototype.propertyIsEnumerable,wt=(t,e,n)=>e in t?ln(t,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[e]=n,sn=(t,e)=>{for(var n in e||(e={}))un.call(e,n)&&wt(t,n,e[n]);if(Ot)for(var n of Ot(e))cn.call(e,n)&&wt(t,n,e[n]);return t};function fn(t=1e3,e={}){const{controls:n=!1,immediate:r=!0,callback:o}=e,l=i.ref(0),u=()=>l.value+=1,c=()=>{l.value=0},d=ht(o?()=>{u(),o(l.value)}:u,t,{immediate:r});return n?sn({counter:l,reset:c},d):l}function dn(t,e={}){var n;const r=i.ref((n=e.initialValue)!=null?n:null);return i.watch(t,()=>r.value=tt(),e),r}function gt(t,e,n={}){const{immediate:r=!0}=n,o=i.ref(!1);let l=null;function u(){l&&(clearTimeout(l),l=null)}function c(){o.value=!1,u()}function d(..._){u(),o.value=!0,l=setTimeout(()=>{o.value=!1,l=null,t(..._)},y(e))}return r&&(o.value=!0,M&&d()),j(c),{isPending:i.readonly(o),start:d,stop:c}}var pn=Object.defineProperty,Pt=Object.getOwnPropertySymbols,yn=Object.prototype.hasOwnProperty,_n=Object.prototype.propertyIsEnumerable,mt=(t,e,n)=>e in t?pn(t,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[e]=n,vn=(t,e)=>{for(var n in e||(e={}))yn.call(e,n)&&mt(t,n,e[n]);if(Pt)for(var n of Pt(e))_n.call(e,n)&&mt(t,n,e[n]);return t};function hn(t=1e3,e={}){const{controls:n=!1,callback:r}=e,o=gt(r??F,t,e),l=i.computed(()=>!o.isPending.value);return n?vn({ready:l},o):l}function On(t,e={}){const{method:n="parseFloat",radix:r,nanToZero:o}=e;return i.computed(()=>{let l=y(t);return typeof l=="string"&&(l=Number[n](l,r)),o&&isNaN(l)&&(l=0),l})}function wn(t){return i.computed(()=>`${y(t)}`)}function gn(t=!1,e={}){const{truthyValue:n=!0,falsyValue:r=!1}=e,o=i.isRef(t),l=i.ref(t);function u(c){if(arguments.length)return l.value=c,l.value;{const d=y(n);return l.value=l.value===d?y(r):d,l.value}}return o?u:[l,u]}function Pn(t,e,n){let r=(n==null?void 0:n.immediate)?[]:[...t instanceof Function?t():Array.isArray(t)?t:i.unref(t)];return i.watch(t,(o,l,u)=>{const c=new Array(r.length),d=[];for(const s of o){let f=!1;for(let p=0;p<r.length;p++)if(!c[p]&&s===r[p]){c[p]=!0,f=!0;break}f||d.push(s)}const _=r.filter((s,f)=>!c[f]);e(o,r,d,_,u),r=[...o]},n)}var bt=Object.getOwnPropertySymbols,mn=Object.prototype.hasOwnProperty,bn=Object.prototype.propertyIsEnumerable,$n=(t,e)=>{var n={};for(var r in t)mn.call(t,r)&&e.indexOf(r)<0&&(n[r]=t[r]);if(t!=null&&bt)for(var r of bt(t))e.indexOf(r)<0&&bn.call(t,r)&&(n[r]=t[r]);return n};function C(t,e,n={}){const r=n,{eventFilter:o=L}=r,l=$n(r,["eventFilter"]);return i.watch(t,R(o,e),l)}var $t=Object.getOwnPropertySymbols,Sn=Object.prototype.hasOwnProperty,An=Object.prototype.propertyIsEnumerable,jn=(t,e)=>{var n={};for(var r in t)Sn.call(t,r)&&e.indexOf(r)<0&&(n[r]=t[r]);if(t!=null&&$t)for(var r of $t(t))e.indexOf(r)<0&&An.call(t,r)&&(n[r]=t[r]);return n};function Fn(t,e,n){const r=n,{count:o}=r,l=jn(r,["count"]),u=i.ref(0),c=C(t,(...d)=>{u.value+=1,u.value>=y(o)&&i.nextTick(()=>c()),e(...d)},l);return{count:u,stop:c}}var In=Object.defineProperty,Tn=Object.defineProperties,En=Object.getOwnPropertyDescriptors,B=Object.getOwnPropertySymbols,St=Object.prototype.hasOwnProperty,At=Object.prototype.propertyIsEnumerable,jt=(t,e,n)=>e in t?In(t,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[e]=n,Mn=(t,e)=>{for(var n in e||(e={}))St.call(e,n)&&jt(t,n,e[n]);if(B)for(var n of B(e))At.call(e,n)&&jt(t,n,e[n]);return t},Rn=(t,e)=>Tn(t,En(e)),Cn=(t,e)=>{var n={};for(var r in t)St.call(t,r)&&e.indexOf(r)<0&&(n[r]=t[r]);if(t!=null&&B)for(var r of B(t))e.indexOf(r)<0&&At.call(t,r)&&(n[r]=t[r]);return n};function Ft(t,e,n={}){const r=n,{debounce:o=0,maxWait:l=void 0}=r,u=Cn(r,["debounce","maxWait"]);return C(t,e,Rn(Mn({},u),{eventFilter:z(o,{maxWait:l})}))}var Nn=Object.defineProperty,Wn=Object.defineProperties,Un=Object.getOwnPropertyDescriptors,H=Object.getOwnPropertySymbols,It=Object.prototype.hasOwnProperty,Tt=Object.prototype.propertyIsEnumerable,Et=(t,e,n)=>e in t?Nn(t,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[e]=n,Ln=(t,e)=>{for(var n in e||(e={}))It.call(e,n)&&Et(t,n,e[n]);if(H)for(var n of H(e))Tt.call(e,n)&&Et(t,n,e[n]);return t},Bn=(t,e)=>Wn(t,Un(e)),Hn=(t,e)=>{var n={};for(var r in t)It.call(t,r)&&e.indexOf(r)<0&&(n[r]=t[r]);if(t!=null&&H)for(var r of H(t))e.indexOf(r)<0&&Tt.call(t,r)&&(n[r]=t[r]);return n};function V(t,e,n={}){const r=n,{eventFilter:o=L}=r,l=Hn(r,["eventFilter"]),u=R(o,e);let c,d,_;if(l.flush==="sync"){const s=i.ref(!1);d=()=>{},c=f=>{s.value=!0,f(),s.value=!1},_=i.watch(t,(...f)=>{s.value||u(...f)},l)}else{const s=[],f=i.ref(0),p=i.ref(0);d=()=>{f.value=p.value},s.push(i.watch(t,()=>{p.value++},Bn(Ln({},l),{flush:"sync"}))),c=h=>{const v=p.value;h(),f.value+=p.value-v},s.push(i.watch(t,(...h)=>{const v=f.value>0&&f.value===p.value;f.value=0,p.value=0,!v&&u(...h)},l)),_=()=>{s.forEach(h=>h())}}return{stop:_,ignoreUpdates:c,ignorePrevAsyncUpdates:d}}function kn(t,e,n){const r=i.watch(t,(...o)=>(i.nextTick(()=>r()),e(...o)),n)}var Yn=Object.defineProperty,Gn=Object.defineProperties,zn=Object.getOwnPropertyDescriptors,k=Object.getOwnPropertySymbols,Mt=Object.prototype.hasOwnProperty,Rt=Object.prototype.propertyIsEnumerable,Ct=(t,e,n)=>e in t?Yn(t,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[e]=n,Zn=(t,e)=>{for(var n in e||(e={}))Mt.call(e,n)&&Ct(t,n,e[n]);if(k)for(var n of k(e))Rt.call(e,n)&&Ct(t,n,e[n]);return t},qn=(t,e)=>Gn(t,zn(e)),Jn=(t,e)=>{var n={};for(var r in t)Mt.call(t,r)&&e.indexOf(r)<0&&(n[r]=t[r]);if(t!=null&&k)for(var r of k(t))e.indexOf(r)<0&&Rt.call(t,r)&&(n[r]=t[r]);return n};function Nt(t,e,n={}){const r=n,{eventFilter:o}=r,l=Jn(r,["eventFilter"]),{eventFilter:u,pause:c,resume:d,isActive:_}=et(o);return{stop:C(t,e,qn(Zn({},l),{eventFilter:u})),pause:c,resume:d,isActive:_}}var Xn=Object.defineProperty,Kn=Object.defineProperties,Qn=Object.getOwnPropertyDescriptors,Y=Object.getOwnPropertySymbols,Wt=Object.prototype.hasOwnProperty,Ut=Object.prototype.propertyIsEnumerable,Lt=(t,e,n)=>e in t?Xn(t,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[e]=n,Vn=(t,e)=>{for(var n in e||(e={}))Wt.call(e,n)&&Lt(t,n,e[n]);if(Y)for(var n of Y(e))Ut.call(e,n)&&Lt(t,n,e[n]);return t},Dn=(t,e)=>Kn(t,Qn(e)),xn=(t,e)=>{var n={};for(var r in t)Wt.call(t,r)&&e.indexOf(r)<0&&(n[r]=t[r]);if(t!=null&&Y)for(var r of Y(t))e.indexOf(r)<0&&Ut.call(t,r)&&(n[r]=t[r]);return n};function Bt(t,e,n={}){const r=n,{throttle:o=0,trailing:l=!0,leading:u=!0}=r,c=xn(r,["throttle","trailing","leading"]);return C(t,e,Dn(Vn({},c),{eventFilter:Z(o,l,u)}))}var tr=Object.defineProperty,er=Object.defineProperties,nr=Object.getOwnPropertyDescriptors,Ht=Object.getOwnPropertySymbols,rr=Object.prototype.hasOwnProperty,or=Object.prototype.propertyIsEnumerable,kt=(t,e,n)=>e in t?tr(t,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[e]=n,ar=(t,e)=>{for(var n in e||(e={}))rr.call(e,n)&&kt(t,n,e[n]);if(Ht)for(var n of Ht(e))or.call(e,n)&&kt(t,n,e[n]);return t},ir=(t,e)=>er(t,nr(e));function lr(t,e,n={}){let r;function o(){if(!r)return;const s=r;r=void 0,s()}function l(s){r=s}const u=(s,f)=>(o(),e(s,f,l)),c=V(t,u,n),{ignoreUpdates:d}=c,_=()=>{let s;return d(()=>{s=u(ur(t),cr(t))}),s};return ir(ar({},c),{trigger:_})}function ur(t){return i.isReactive(t)?t:Array.isArray(t)?t.map(e=>Yt(e)):Yt(t)}function Yt(t){return typeof t=="function"?t():i.unref(t)}function cr(t){return Array.isArray(t)?t.map(()=>{}):void 0}function sr(t,e,n){return i.watch(t,(r,o,l)=>{r&&e(r,o,l)},n)}a.__onlyVue27Plus=nt,a.__onlyVue3=re,a.assert=qt,a.autoResetRef=ct,a.bypassFilter=L,a.clamp=xt,a.computedEager=$,a.computedWithControl=rt,a.containsProp=ue,a.controlledComputed=rt,a.controlledRef=Ae,a.createEventHook=fe,a.createFilterWrapper=R,a.createGlobalState=de,a.createInjectionState=pe,a.createReactiveFn=J,a.createSharedComposable=ye,a.createSingletonPromise=ie,a.debounceFilter=z,a.debouncedRef=X,a.debouncedWatch=Ft,a.directiveHooks=oe,a.eagerComputed=$,a.extendRef=ot,a.formatDate=_t,a.get=_e,a.hasOwn=ne,a.identity=ae,a.ignorableWatch=V,a.increaseWithUnit=ce,a.invoke=le,a.isBoolean=Jt,a.isClient=M,a.isDef=Zt,a.isDefined=ve,a.isFunction=U,a.isIOS=ee,a.isNumber=Xt,a.isObject=Qt,a.isString=Kt,a.isWindow=Vt,a.makeDestructurable=Pe,a.noop=F,a.normalizeDate=vt,a.now=Dt,a.objectPick=se,a.pausableFilter=et,a.pausableWatch=Nt,a.promiseTimeout=q,a.rand=te,a.reactify=J,a.reactifyObject=me,a.reactiveComputed=ut,a.reactiveOmit=be,a.reactivePick=$e,a.refAutoReset=ct,a.refDebounced=X,a.refDefault=Se,a.refThrottled=K,a.refWithControl=dt,a.resolveRef=je,a.resolveUnref=y,a.set=Fe,a.syncRef=Ie,a.syncRefs=Te,a.throttleFilter=Z,a.throttledRef=K,a.throttledWatch=Bt,a.timestamp=tt,a.toReactive=lt,a.toRefs=Le,a.tryOnBeforeMount=Be,a.tryOnBeforeUnmount=He,a.tryOnMounted=ke,a.tryOnScopeDispose=j,a.tryOnUnmounted=Ye,a.until=Ge,a.useArrayEvery=ze,a.useArrayFilter=Ze,a.useArrayFind=qe,a.useArrayFindIndex=Je,a.useArrayFindLast=Ke,a.useArrayJoin=Qe,a.useArrayMap=Ve,a.useArrayReduce=De,a.useArraySome=xe,a.useArrayUnique=tn,a.useCounter=en,a.useDateFormat=an,a.useDebounce=X,a.useDebounceFn=st,a.useInterval=fn,a.useIntervalFn=ht,a.useLastChanged=dn,a.useThrottle=K,a.useThrottleFn=ft,a.useTimeout=hn,a.useTimeoutFn=gt,a.useToNumber=On,a.useToString=wn,a.useToggle=gn,a.watchArray=Pn,a.watchAtMost=Fn,a.watchDebounced=Ft,a.watchIgnorable=V,a.watchOnce=kn,a.watchPausable=Nt,a.watchThrottled=Bt,a.watchTriggerable=lr,a.watchWithFilter=C,a.whenever=sr})(this.VueUse=this.VueUse||{},VueDemi);

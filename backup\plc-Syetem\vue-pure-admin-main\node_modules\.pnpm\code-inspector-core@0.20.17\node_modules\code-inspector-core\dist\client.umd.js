(function($,b){typeof exports=="object"&&typeof module!="undefined"?b(exports):typeof define=="function"&&define.amd?define(["exports"],b):b(($=typeof globalThis!="undefined"?globalThis:$||self).vueInspectorClient={})})(this,function($){"use strict";var mt=Object.freeze,bt=Object.defineProperty,nn=Object.defineProperties;var on=Object.getOwnPropertyDescriptors;var vt=Object.getOwnPropertySymbols;var rn=Object.prototype.hasOwnProperty,sn=Object.prototype.propertyIsEnumerable;var A=Math.pow,yt=($,b,E)=>b in $?bt($,b,{enumerable:!0,configurable:!0,writable:!0,value:E}):$[b]=E,W=($,b)=>{for(var E in b||(b={}))rn.call(b,E)&&yt($,E,b[E]);if(vt)for(var E of vt(b))sn.call(b,E)&&yt($,E,b[E]);return $},ie=($,b)=>nn($,on(b));var Y=($,b)=>mt(bt($,"raw",{value:mt(b||$.slice())})),wt=($,b,E)=>new Promise((F,q)=>{var re=P=>{try{N(E.next(P))}catch(R){q(R)}},se=P=>{try{N(E.throw(P))}catch(R){q(R)}},N=P=>P.done?F(P.value):Promise.resolve(P.value).then(re,se);N((E=E.apply($,b)).next())});var $t,_t,Et,xt,At;const b=window,E=b.ShadowRoot&&(b.ShadyCSS===void 0||b.ShadyCSS.nativeShadow)&&"adoptedStyleSheets"in Document.prototype&&"replace"in CSSStyleSheet.prototype,F=Symbol(),q=new WeakMap;let re=class{constructor(i,e,n){if(this._$cssResult$=!0,n!==F)throw Error("CSSResult is not constructable. Use `unsafeCSS` or `css` instead.");this.cssText=i,this.t=e}get styleSheet(){let i=this.o;const e=this.t;if(E&&i===void 0){const n=e!==void 0&&e.length===1;n&&(i=q.get(e)),i===void 0&&((this.o=i=new CSSStyleSheet).replaceSync(this.cssText),n&&q.set(e,i))}return i}toString(){return this.cssText}};const se=E?i=>i:i=>i instanceof CSSStyleSheet?(e=>{let n="";for(const s of e.cssRules)n+=s.cssText;return(s=>new re(typeof s=="string"?s:s+"",void 0,F))(n)})(i):i;var N;const P=window,R=P.trustedTypes,kt=R?R.emptyScript:"",je=P.reactiveElementPolyfillSupport,ge={toAttribute(i,e){switch(e){case Boolean:i=i?kt:null;break;case Object:case Array:i=i==null?i:JSON.stringify(i)}return i},fromAttribute(i,e){let n=i;switch(e){case Boolean:n=i!==null;break;case Number:n=i===null?null:Number(i);break;case Object:case Array:try{n=JSON.parse(i)}catch(s){n=null}}return n}},De=(i,e)=>e!==i&&(e==e||i==i),fe={attribute:!0,type:String,converter:ge,reflect:!1,hasChanged:De};let U=class extends HTMLElement{constructor(){super(),this._$Ei=new Map,this.isUpdatePending=!1,this.hasUpdated=!1,this._$El=null,this.u()}static addInitializer(i){var e;this.finalize(),((e=this.h)!==null&&e!==void 0?e:this.h=[]).push(i)}static get observedAttributes(){this.finalize();const i=[];return this.elementProperties.forEach((e,n)=>{const s=this._$Ep(n,e);s!==void 0&&(this._$Ev.set(s,n),i.push(s))}),i}static createProperty(i,e=fe){if(e.state&&(e.attribute=!1),this.finalize(),this.elementProperties.set(i,e),!e.noAccessor&&!this.prototype.hasOwnProperty(i)){const n=typeof i=="symbol"?Symbol():"__"+i,s=this.getPropertyDescriptor(i,n,e);s!==void 0&&Object.defineProperty(this.prototype,i,s)}}static getPropertyDescriptor(i,e,n){return{get(){return this[e]},set(s){const t=this[i];this[e]=s,this.requestUpdate(i,t,n)},configurable:!0,enumerable:!0}}static getPropertyOptions(i){return this.elementProperties.get(i)||fe}static finalize(){if(this.hasOwnProperty("finalized"))return!1;this.finalized=!0;const i=Object.getPrototypeOf(this);if(i.finalize(),i.h!==void 0&&(this.h=[...i.h]),this.elementProperties=new Map(i.elementProperties),this._$Ev=new Map,this.hasOwnProperty("properties")){const e=this.properties,n=[...Object.getOwnPropertyNames(e),...Object.getOwnPropertySymbols(e)];for(const s of n)this.createProperty(s,e[s])}return this.elementStyles=this.finalizeStyles(this.styles),!0}static finalizeStyles(i){const e=[];if(Array.isArray(i)){const n=new Set(i.flat(1/0).reverse());for(const s of n)e.unshift(se(s))}else i!==void 0&&e.push(se(i));return e}static _$Ep(i,e){const n=e.attribute;return n===!1?void 0:typeof n=="string"?n:typeof i=="string"?i.toLowerCase():void 0}u(){var i;this._$E_=new Promise(e=>this.enableUpdating=e),this._$AL=new Map,this._$Eg(),this.requestUpdate(),(i=this.constructor.h)===null||i===void 0||i.forEach(e=>e(this))}addController(i){var e,n;((e=this._$ES)!==null&&e!==void 0?e:this._$ES=[]).push(i),this.renderRoot!==void 0&&this.isConnected&&((n=i.hostConnected)===null||n===void 0||n.call(i))}removeController(i){var e;(e=this._$ES)===null||e===void 0||e.splice(this._$ES.indexOf(i)>>>0,1)}_$Eg(){this.constructor.elementProperties.forEach((i,e)=>{this.hasOwnProperty(e)&&(this._$Ei.set(e,this[e]),delete this[e])})}createRenderRoot(){var i;const e=(i=this.shadowRoot)!==null&&i!==void 0?i:this.attachShadow(this.constructor.shadowRootOptions);return((n,s)=>{E?n.adoptedStyleSheets=s.map(t=>t instanceof CSSStyleSheet?t:t.styleSheet):s.forEach(t=>{const o=document.createElement("style"),r=b.litNonce;r!==void 0&&o.setAttribute("nonce",r),o.textContent=t.cssText,n.appendChild(o)})})(e,this.constructor.elementStyles),e}connectedCallback(){var i;this.renderRoot===void 0&&(this.renderRoot=this.createRenderRoot()),this.enableUpdating(!0),(i=this._$ES)===null||i===void 0||i.forEach(e=>{var n;return(n=e.hostConnected)===null||n===void 0?void 0:n.call(e)})}enableUpdating(i){}disconnectedCallback(){var i;(i=this._$ES)===null||i===void 0||i.forEach(e=>{var n;return(n=e.hostDisconnected)===null||n===void 0?void 0:n.call(e)})}attributeChangedCallback(i,e,n){this._$AK(i,n)}_$EO(i,e,n=fe){var s;const t=this.constructor._$Ep(i,n);if(t!==void 0&&n.reflect===!0){const o=(((s=n.converter)===null||s===void 0?void 0:s.toAttribute)!==void 0?n.converter:ge).toAttribute(e,n.type);this._$El=i,o==null?this.removeAttribute(t):this.setAttribute(t,o),this._$El=null}}_$AK(i,e){var n;const s=this.constructor,t=s._$Ev.get(i);if(t!==void 0&&this._$El!==t){const o=s.getPropertyOptions(t),r=typeof o.converter=="function"?{fromAttribute:o.converter}:((n=o.converter)===null||n===void 0?void 0:n.fromAttribute)!==void 0?o.converter:ge;this._$El=t,this[t]=r.fromAttribute(e,o.type),this._$El=null}}requestUpdate(i,e,n){let s=!0;i!==void 0&&(((n=n||this.constructor.getPropertyOptions(i)).hasChanged||De)(this[i],e)?(this._$AL.has(i)||this._$AL.set(i,e),n.reflect===!0&&this._$El!==i&&(this._$EC===void 0&&(this._$EC=new Map),this._$EC.set(i,n))):s=!1),!this.isUpdatePending&&s&&(this._$E_=this._$Ej())}_$Ej(){return wt(this,null,function*(){this.isUpdatePending=!0;try{yield this._$E_}catch(e){Promise.reject(e)}const i=this.scheduleUpdate();return i!=null&&(yield i),!this.isUpdatePending})}scheduleUpdate(){return this.performUpdate()}performUpdate(){var i;if(!this.isUpdatePending)return;this.hasUpdated,this._$Ei&&(this._$Ei.forEach((s,t)=>this[t]=s),this._$Ei=void 0);let e=!1;const n=this._$AL;try{e=this.shouldUpdate(n),e?(this.willUpdate(n),(i=this._$ES)===null||i===void 0||i.forEach(s=>{var t;return(t=s.hostUpdate)===null||t===void 0?void 0:t.call(s)}),this.update(n)):this._$Ek()}catch(s){throw e=!1,this._$Ek(),s}e&&this._$AE(n)}willUpdate(i){}_$AE(i){var e;(e=this._$ES)===null||e===void 0||e.forEach(n=>{var s;return(s=n.hostUpdated)===null||s===void 0?void 0:s.call(n)}),this.hasUpdated||(this.hasUpdated=!0,this.firstUpdated(i)),this.updated(i)}_$Ek(){this._$AL=new Map,this.isUpdatePending=!1}get updateComplete(){return this.getUpdateComplete()}getUpdateComplete(){return this._$E_}shouldUpdate(i){return!0}update(i){this._$EC!==void 0&&(this._$EC.forEach((e,n)=>this._$EO(n,this[n],e)),this._$EC=void 0),this._$Ek()}updated(i){}firstUpdated(i){}};var me;U.finalized=!0,U.elementProperties=new Map,U.elementStyles=[],U.shadowRootOptions={mode:"open"},je==null||je({ReactiveElement:U}),((N=P.reactiveElementVersions)!==null&&N!==void 0?N:P.reactiveElementVersions=[]).push("1.6.1");const le=window,V=le.trustedTypes,Le=V?V.createPolicy("lit-html",{createHTML:i=>i}):void 0,O="lit$".concat((Math.random()+"").slice(9),"$"),Re="?"+O,Ct="<".concat(Re,">"),I=document,G=(i="")=>I.createComment(i),X=i=>i===null||typeof i!="object"&&typeof i!="function",Ue=Array.isArray,Z=/<(?:(!--|\/[^a-zA-Z])|(\/?[a-zA-Z][^>\s]*)|(\/?$))/g,Ve=/-->/g,Ie=/>/g,j=RegExp(">|[ 	\n\f\r](?:([^\\s\"'>=/]+)([ 	\n\f\r]*=[ 	\n\f\r]*(?:[^ 	\n\f\r\"'`<>=]|(\"|')|))|$)","g"),Be=/'/g,ze=/"/g,He=/^(?:script|style|textarea|title)$/i,ae=(i=>(e,...n)=>({_$litType$:i,strings:e,values:n}))(1),D=Symbol.for("lit-noChange"),k=Symbol.for("lit-nothing"),Ke=new WeakMap,B=I.createTreeWalker(I,129,null,!1),St=(i,e)=>{const n=i.length-1,s=[];let t,o=e===2?"<svg>":"",r=Z;for(let a=0;a<n;a++){const c=i[a];let h,d,p=-1,u=0;for(;u<c.length&&(r.lastIndex=u,d=r.exec(c),d!==null);)u=r.lastIndex,r===Z?d[1]==="!--"?r=Ve:d[1]!==void 0?r=Ie:d[2]!==void 0?(He.test(d[2])&&(t=RegExp("</"+d[2],"g")),r=j):d[3]!==void 0&&(r=j):r===j?d[0]===">"?(r=t!=null?t:Z,p=-1):d[1]===void 0?p=-2:(p=r.lastIndex-d[2].length,h=d[1],r=d[3]===void 0?j:d[3]==='"'?ze:Be):r===ze||r===Be?r=j:r===Ve||r===Ie?r=Z:(r=j,t=void 0);const g=r===j&&i[a+1].startsWith("/>")?" ":"";o+=r===Z?c+Ct:p>=0?(s.push(h),c.slice(0,p)+"$lit$"+c.slice(p)+O+g):c+O+(p===-2?(s.push(void 0),a):g)}const l=o+(i[n]||"<?>")+(e===2?"</svg>":"");if(!Array.isArray(i)||!i.hasOwnProperty("raw"))throw Error("invalid template strings array");return[Le!==void 0?Le.createHTML(l):l,s]};class J{constructor({strings:e,_$litType$:n},s){let t;this.parts=[];let o=0,r=0;const l=e.length-1,a=this.parts,[c,h]=St(e,n);if(this.el=J.createElement(c,s),B.currentNode=this.el.content,n===2){const d=this.el.content,p=d.firstChild;p.remove(),d.append(...p.childNodes)}for(;(t=B.nextNode())!==null&&a.length<l;){if(t.nodeType===1){if(t.hasAttributes()){const d=[];for(const p of t.getAttributeNames())if(p.endsWith("$lit$")||p.startsWith(O)){const u=h[r++];if(d.push(p),u!==void 0){const g=t.getAttribute(u.toLowerCase()+"$lit$").split(O),f=/([.?@])?(.*)/.exec(u);a.push({type:1,index:o,name:f[2],strings:g,ctor:f[1]==="."?Mt:f[1]==="?"?Tt:f[1]==="@"?Nt:ce})}else a.push({type:6,index:o})}for(const p of d)t.removeAttribute(p)}if(He.test(t.tagName)){const d=t.textContent.split(O),p=d.length-1;if(p>0){t.textContent=V?V.emptyScript:"";for(let u=0;u<p;u++)t.append(d[u],G()),B.nextNode(),a.push({type:2,index:++o});t.append(d[p],G())}}}else if(t.nodeType===8)if(t.data===Re)a.push({type:2,index:o});else{let d=-1;for(;(d=t.data.indexOf(O,d+1))!==-1;)a.push({type:7,index:o}),d+=O.length-1}o++}}static createElement(e,n){const s=I.createElement("template");return s.innerHTML=e,s}}function z(i,e,n=i,s){var t,o,r,l;if(e===D)return e;let a=s!==void 0?(t=n._$Co)===null||t===void 0?void 0:t[s]:n._$Cl;const c=X(e)?void 0:e._$litDirective$;return(a==null?void 0:a.constructor)!==c&&((o=a==null?void 0:a._$AO)===null||o===void 0||o.call(a,!1),c===void 0?a=void 0:(a=new c(i),a._$AT(i,n,s)),s!==void 0?((r=(l=n)._$Co)!==null&&r!==void 0?r:l._$Co=[])[s]=a:n._$Cl=a),a!==void 0&&(e=z(i,a._$AS(i,e.values),a,s)),e}class Pt{constructor(e,n){this.u=[],this._$AN=void 0,this._$AD=e,this._$AM=n}get parentNode(){return this._$AM.parentNode}get _$AU(){return this._$AM._$AU}v(e){var n;const{el:{content:s},parts:t}=this._$AD,o=((n=e==null?void 0:e.creationScope)!==null&&n!==void 0?n:I).importNode(s,!0);B.currentNode=o;let r=B.nextNode(),l=0,a=0,c=t[0];for(;c!==void 0;){if(l===c.index){let h;c.type===2?h=new Q(r,r.nextSibling,this,e):c.type===1?h=new c.ctor(r,c.name,c.strings,this,e):c.type===6&&(h=new jt(r,this,e)),this.u.push(h),c=t[++a]}l!==(c==null?void 0:c.index)&&(r=B.nextNode(),l++)}return o}p(e){let n=0;for(const s of this.u)s!==void 0&&(s.strings!==void 0?(s._$AI(e,s,n),n+=s.strings.length-2):s._$AI(e[n])),n++}}class Q{constructor(e,n,s,t){var o;this.type=2,this._$AH=k,this._$AN=void 0,this._$AA=e,this._$AB=n,this._$AM=s,this.options=t,this._$Cm=(o=t==null?void 0:t.isConnected)===null||o===void 0||o}get _$AU(){var e,n;return(n=(e=this._$AM)===null||e===void 0?void 0:e._$AU)!==null&&n!==void 0?n:this._$Cm}get parentNode(){let e=this._$AA.parentNode;const n=this._$AM;return n!==void 0&&e.nodeType===11&&(e=n.parentNode),e}get startNode(){return this._$AA}get endNode(){return this._$AB}_$AI(e,n=this){e=z(this,e,n),X(e)?e===k||e==null||e===""?(this._$AH!==k&&this._$AR(),this._$AH=k):e!==this._$AH&&e!==D&&this.g(e):e._$litType$!==void 0?this.$(e):e.nodeType!==void 0?this.T(e):(s=>Ue(s)||typeof(s==null?void 0:s[Symbol.iterator])=="function")(e)?this.k(e):this.g(e)}O(e,n=this._$AB){return this._$AA.parentNode.insertBefore(e,n)}T(e){this._$AH!==e&&(this._$AR(),this._$AH=this.O(e))}g(e){this._$AH!==k&&X(this._$AH)?this._$AA.nextSibling.data=e:this.T(I.createTextNode(e)),this._$AH=e}$(e){var n;const{values:s,_$litType$:t}=e,o=typeof t=="number"?this._$AC(e):(t.el===void 0&&(t.el=J.createElement(t.h,this.options)),t);if(((n=this._$AH)===null||n===void 0?void 0:n._$AD)===o)this._$AH.p(s);else{const r=new Pt(o,this),l=r.v(this.options);r.p(s),this.T(l),this._$AH=r}}_$AC(e){let n=Ke.get(e.strings);return n===void 0&&Ke.set(e.strings,n=new J(e)),n}k(e){Ue(this._$AH)||(this._$AH=[],this._$AR());const n=this._$AH;let s,t=0;for(const o of e)t===n.length?n.push(s=new Q(this.O(G()),this.O(G()),this,this.options)):s=n[t],s._$AI(o),t++;t<n.length&&(this._$AR(s&&s._$AB.nextSibling,t),n.length=t)}_$AR(e=this._$AA.nextSibling,n){var s;for((s=this._$AP)===null||s===void 0||s.call(this,!1,!0,n);e&&e!==this._$AB;){const t=e.nextSibling;e.remove(),e=t}}setConnected(e){var n;this._$AM===void 0&&(this._$Cm=e,(n=this._$AP)===null||n===void 0||n.call(this,e))}}let ce=class{constructor(i,e,n,s,t){this.type=1,this._$AH=k,this._$AN=void 0,this.element=i,this.name=e,this._$AM=s,this.options=t,n.length>2||n[0]!==""||n[1]!==""?(this._$AH=Array(n.length-1).fill(new String),this.strings=n):this._$AH=k}get tagName(){return this.element.tagName}get _$AU(){return this._$AM._$AU}_$AI(i,e=this,n,s){const t=this.strings;let o=!1;if(t===void 0)i=z(this,i,e,0),o=!X(i)||i!==this._$AH&&i!==D,o&&(this._$AH=i);else{const r=i;let l,a;for(i=t[0],l=0;l<t.length-1;l++)a=z(this,r[n+l],e,l),a===D&&(a=this._$AH[l]),o||(o=!X(a)||a!==this._$AH[l]),a===k?i=k:i!==k&&(i+=(a!=null?a:"")+t[l+1]),this._$AH[l]=a}o&&!s&&this.j(i)}j(i){i===k?this.element.removeAttribute(this.name):this.element.setAttribute(this.name,i!=null?i:"")}};class Mt extends ce{constructor(){super(...arguments),this.type=3}j(e){this.element[this.name]=e===k?void 0:e}}const Ot=V?V.emptyScript:"";class Tt extends ce{constructor(){super(...arguments),this.type=4}j(e){e&&e!==k?this.element.setAttribute(this.name,Ot):this.element.removeAttribute(this.name)}}class Nt extends ce{constructor(e,n,s,t,o){super(e,n,s,t,o),this.type=5}_$AI(e,n=this){var s;if((e=(s=z(this,e,n,0))!==null&&s!==void 0?s:k)===D)return;const t=this._$AH,o=e===k&&t!==k||e.capture!==t.capture||e.once!==t.once||e.passive!==t.passive,r=e!==k&&(t===k||o);o&&this.element.removeEventListener(this.name,this,t),r&&this.element.addEventListener(this.name,this,e),this._$AH=e}handleEvent(e){var n,s;typeof this._$AH=="function"?this._$AH.call((s=(n=this.options)===null||n===void 0?void 0:n.host)!==null&&s!==void 0?s:this.element,e):this._$AH.handleEvent(e)}}class jt{constructor(e,n,s){this.element=e,this.type=6,this._$AN=void 0,this._$AM=n,this.options=s}get _$AU(){return this._$AM._$AU}_$AI(e){z(this,e)}}const We=le.litHtmlPolyfillSupport;We==null||We(J,Q),((me=le.litHtmlVersions)!==null&&me!==void 0?me:le.litHtmlVersions=[]).push("2.6.1");var ve,ye;class ee extends U{constructor(){super(...arguments),this.renderOptions={host:this},this._$Do=void 0}createRenderRoot(){var e,n;const s=super.createRenderRoot();return(e=(n=this.renderOptions).renderBefore)!==null&&e!==void 0||(n.renderBefore=s.firstChild),s}update(e){const n=this.render();this.hasUpdated||(this.renderOptions.isConnected=this.isConnected),super.update(e),this._$Do=((s,t,o)=>{var r,l;const a=(r=o==null?void 0:o.renderBefore)!==null&&r!==void 0?r:t;let c=a._$litPart$;if(c===void 0){const h=(l=o==null?void 0:o.renderBefore)!==null&&l!==void 0?l:null;a._$litPart$=c=new Q(t.insertBefore(G(),h),h,void 0,o!=null?o:{})}return c._$AI(s),c})(n,this.renderRoot,this.renderOptions)}connectedCallback(){var e;super.connectedCallback(),(e=this._$Do)===null||e===void 0||e.setConnected(!0)}disconnectedCallback(){var e;super.disconnectedCallback(),(e=this._$Do)===null||e===void 0||e.setConnected(!1)}render(){return D}}ee.finalized=!0,ee._$litElement$=!0,(ve=globalThis.litElementHydrateSupport)===null||ve===void 0||ve.call(globalThis,{LitElement:ee});const Ye=globalThis.litElementPolyfillSupport;Ye==null||Ye({LitElement:ee}),((ye=globalThis.litElementVersions)!==null&&ye!==void 0?ye:globalThis.litElementVersions=[]).push("3.2.2");const Dt=(i,e)=>e.kind==="method"&&e.descriptor&&!("value"in e.descriptor)?ie(W({},e),{finisher(n){n.createProperty(e.key,i)}}):{kind:"field",key:Symbol(),placement:"own",descriptor:{},originalKey:e.key,initializer(){typeof e.initializer=="function"&&(this[e.key]=e.initializer.call(this))},finisher(n){n.createProperty(e.key,i)}};function M(i){return(e,n)=>n!==void 0?((s,t,o)=>{t.constructor.createProperty(o,s)})(i,e,n):Dt(i,e)}function C(i){return M(ie(W({},i),{state:!0}))}function Fe(i,e){return(({finisher:n,descriptor:s})=>(t,o)=>{var r;if(o===void 0){const l=(r=t.originalKey)!==null&&r!==void 0?r:t.key,a=s!=null?{kind:"method",placement:"prototype",key:l,descriptor:s(t.key)}:ie(W({},t),{key:l});return n!=null&&(a.finisher=function(c){n(c,l)}),a}{const l=t.constructor;s!==void 0&&Object.defineProperty(t,o,s(o)),n==null||n(l,o)}})({descriptor:n=>{const s={get(){var t,o;return(o=(t=this.renderRoot)===null||t===void 0?void 0:t.querySelector(i))!==null&&o!==void 0?o:null},enumerable:!0,configurable:!0};if(e){const t=typeof n=="symbol"?Symbol():"__"+n;s.get=function(){var o,r;return this[t]===void 0&&(this[t]=(r=(o=this.renderRoot)===null||o===void 0?void 0:o.querySelector(i))!==null&&r!==void 0?r:null),this[t]}}return s}})}var be;(be=window.HTMLSlotElement)===null||be===void 0||be.prototype.assignedElements;const Lt=1;let Rt=class{constructor(i){}get _$AU(){return this._$AM._$AU}_$AT(i,e,n){this._$Ct=i,this._$AM=e,this._$Ci=n}_$AS(i,e){return this.update(i,e)}update(i,e){return this.render(...e)}};const L=(i=>(...e)=>({_$litDirective$:i,values:e}))(class extends Rt{constructor(i){var e;if(super(i),i.type!==Lt||i.name!=="style"||((e=i.strings)===null||e===void 0?void 0:e.length)>2)throw Error("The `styleMap` directive must be used in the `style` attribute and must be the only part in the attribute.")}render(i){return Object.keys(i).reduce((e,n)=>{const s=i[n];return s==null?e:e+"".concat(n=n.replace(/(?:^(webkit|moz|ms|o)|)(?=[A-Z])/g,"-$&").toLowerCase(),":").concat(s,";")},"")}update(i,[e]){const{style:n}=i.element;if(this.vt===void 0){this.vt=new Set;for(const s in e)this.vt.add(s);return this.render(e)}this.vt.forEach(s=>{e[s]==null&&(this.vt.delete(s),s.includes("-")?n.removeProperty(s):n[s]="")});for(const s in e){const t=e[s];t!=null&&(this.vt.add(s),s.includes("-")?n.setProperty(s,t):n[s]=t)}return D}}),H="data-insp-path",Ut=Object.freeze(Object.defineProperty({__proto__:null,default:{}},Symbol.toStringTag,{value:"Module"}));function Vt(i){if(i.__esModule)return i;var e=i.default;if(typeof e=="function"){var n=function s(){if(this instanceof s){var t=[null];return t.push.apply(t,arguments),new(Function.bind.apply(e,t))}return e.apply(this,arguments)};n.prototype=e.prototype}else n={};return Object.defineProperty(n,"__esModule",{value:!0}),Object.keys(i).forEach(function(s){var t=Object.getOwnPropertyDescriptor(i,s);Object.defineProperty(n,s,t.get?t:{enumerable:!0,get:function(){return i[s]}})}),n}var qe,Ge,we,Xe,$e,Ze,Je,Qe,et={exports:{}};function tt(){if(Xe)return we;Xe=1;const i=Ge?qe:(Ge=1,qe={aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,134,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,250,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],grey:[128,128,128],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,221],powderblue:[176,224,230],purple:[128,0,128],rebeccapurple:[102,51,153],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[112,128,144],slategrey:[112,128,144],snow:[255,250,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,50]}),e={};for(const t of Object.keys(i))e[i[t]]=t;const n={rgb:{channels:3,labels:"rgb"},hsl:{channels:3,labels:"hsl"},hsv:{channels:3,labels:"hsv"},hwb:{channels:3,labels:"hwb"},cmyk:{channels:4,labels:"cmyk"},xyz:{channels:3,labels:"xyz"},lab:{channels:3,labels:"lab"},lch:{channels:3,labels:"lch"},hex:{channels:1,labels:["hex"]},keyword:{channels:1,labels:["keyword"]},ansi16:{channels:1,labels:["ansi16"]},ansi256:{channels:1,labels:["ansi256"]},hcg:{channels:3,labels:["h","c","g"]},apple:{channels:3,labels:["r16","g16","b16"]},gray:{channels:1,labels:["gray"]}};we=n;for(const t of Object.keys(n)){if(!("channels"in n[t]))throw new Error("missing channels property: "+t);if(!("labels"in n[t]))throw new Error("missing channel labels property: "+t);if(n[t].labels.length!==n[t].channels)throw new Error("channel and label counts mismatch: "+t);const{channels:o,labels:r}=n[t];delete n[t].channels,delete n[t].labels,Object.defineProperty(n[t],"channels",{value:o}),Object.defineProperty(n[t],"labels",{value:r})}function s(t,o){return A(t[0]-o[0],2)+A(t[1]-o[1],2)+A(t[2]-o[2],2)}return n.rgb.hsl=function(t){const o=t[0]/255,r=t[1]/255,l=t[2]/255,a=Math.min(o,r,l),c=Math.max(o,r,l),h=c-a;let d,p;c===a?d=0:o===c?d=(r-l)/h:r===c?d=2+(l-o)/h:l===c&&(d=4+(o-r)/h),d=Math.min(60*d,360),d<0&&(d+=360);const u=(a+c)/2;return p=c===a?0:u<=.5?h/(c+a):h/(2-c-a),[d,100*p,100*u]},n.rgb.hsv=function(t){let o,r,l,a,c;const h=t[0]/255,d=t[1]/255,p=t[2]/255,u=Math.max(h,d,p),g=u-Math.min(h,d,p),f=function(m){return(u-m)/6/g+.5};return g===0?(a=0,c=0):(c=g/u,o=f(h),r=f(d),l=f(p),h===u?a=l-r:d===u?a=1/3+o-l:p===u&&(a=2/3+r-o),a<0?a+=1:a>1&&(a-=1)),[360*a,100*c,100*u]},n.rgb.hwb=function(t){const o=t[0],r=t[1];let l=t[2];const a=n.rgb.hsl(t)[0],c=1/255*Math.min(o,Math.min(r,l));return l=1-1/255*Math.max(o,Math.max(r,l)),[a,100*c,100*l]},n.rgb.cmyk=function(t){const o=t[0]/255,r=t[1]/255,l=t[2]/255,a=Math.min(1-o,1-r,1-l);return[100*((1-o-a)/(1-a)||0),100*((1-r-a)/(1-a)||0),100*((1-l-a)/(1-a)||0),100*a]},n.rgb.keyword=function(t){const o=e[t];if(o)return o;let r,l=1/0;for(const a of Object.keys(i)){const c=s(t,i[a]);c<l&&(l=c,r=a)}return r},n.keyword.rgb=function(t){return i[t]},n.rgb.xyz=function(t){let o=t[0]/255,r=t[1]/255,l=t[2]/255;return o=o>.04045?A((o+.055)/1.055,2.4):o/12.92,r=r>.04045?A((r+.055)/1.055,2.4):r/12.92,l=l>.04045?A((l+.055)/1.055,2.4):l/12.92,[100*(.4124*o+.3576*r+.1805*l),100*(.2126*o+.7152*r+.0722*l),100*(.0193*o+.1192*r+.9505*l)]},n.rgb.lab=function(t){const o=n.rgb.xyz(t);let r=o[0],l=o[1],a=o[2];return r/=95.047,l/=100,a/=108.883,r=r>.008856?A(r,1/3):7.787*r+16/116,l=l>.008856?A(l,1/3):7.787*l+16/116,a=a>.008856?A(a,1/3):7.787*a+16/116,[116*l-16,500*(r-l),200*(l-a)]},n.hsl.rgb=function(t){const o=t[0]/360,r=t[1]/100,l=t[2]/100;let a,c,h;if(r===0)return h=255*l,[h,h,h];a=l<.5?l*(1+r):l+r-l*r;const d=2*l-a,p=[0,0,0];for(let u=0;u<3;u++)c=o+1/3*-(u-1),c<0&&c++,c>1&&c--,h=6*c<1?d+6*(a-d)*c:2*c<1?a:3*c<2?d+(a-d)*(2/3-c)*6:d,p[u]=255*h;return p},n.hsl.hsv=function(t){const o=t[0];let r=t[1]/100,l=t[2]/100,a=r;const c=Math.max(l,.01);return l*=2,r*=l<=1?l:2-l,a*=c<=1?c:2-c,[o,100*(l===0?2*a/(c+a):2*r/(l+r)),100*((l+r)/2)]},n.hsv.rgb=function(t){const o=t[0]/60,r=t[1]/100;let l=t[2]/100;const a=Math.floor(o)%6,c=o-Math.floor(o),h=255*l*(1-r),d=255*l*(1-r*c),p=255*l*(1-r*(1-c));switch(l*=255,a){case 0:return[l,p,h];case 1:return[d,l,h];case 2:return[h,l,p];case 3:return[h,d,l];case 4:return[p,h,l];case 5:return[l,h,d]}},n.hsv.hsl=function(t){const o=t[0],r=t[1]/100,l=t[2]/100,a=Math.max(l,.01);let c,h;h=(2-r)*l;const d=(2-r)*a;return c=r*a,c/=d<=1?d:2-d,c=c||0,h/=2,[o,100*c,100*h]},n.hwb.rgb=function(t){const o=t[0]/360;let r=t[1]/100,l=t[2]/100;const a=r+l;let c;a>1&&(r/=a,l/=a);const h=Math.floor(6*o),d=1-l;c=6*o-h,1&h&&(c=1-c);const p=r+c*(d-r);let u,g,f;switch(h){default:case 6:case 0:u=d,g=p,f=r;break;case 1:u=p,g=d,f=r;break;case 2:u=r,g=d,f=p;break;case 3:u=r,g=p,f=d;break;case 4:u=p,g=r,f=d;break;case 5:u=d,g=r,f=p}return[255*u,255*g,255*f]},n.cmyk.rgb=function(t){const o=t[0]/100,r=t[1]/100,l=t[2]/100,a=t[3]/100;return[255*(1-Math.min(1,o*(1-a)+a)),255*(1-Math.min(1,r*(1-a)+a)),255*(1-Math.min(1,l*(1-a)+a))]},n.xyz.rgb=function(t){const o=t[0]/100,r=t[1]/100,l=t[2]/100;let a,c,h;return a=3.2406*o+-1.5372*r+-.4986*l,c=-.9689*o+1.8758*r+.0415*l,h=.0557*o+-.204*r+1.057*l,a=a>.0031308?1.055*A(a,1/2.4)-.055:12.92*a,c=c>.0031308?1.055*A(c,1/2.4)-.055:12.92*c,h=h>.0031308?1.055*A(h,1/2.4)-.055:12.92*h,a=Math.min(Math.max(0,a),1),c=Math.min(Math.max(0,c),1),h=Math.min(Math.max(0,h),1),[255*a,255*c,255*h]},n.xyz.lab=function(t){let o=t[0],r=t[1],l=t[2];return o/=95.047,r/=100,l/=108.883,o=o>.008856?A(o,1/3):7.787*o+16/116,r=r>.008856?A(r,1/3):7.787*r+16/116,l=l>.008856?A(l,1/3):7.787*l+16/116,[116*r-16,500*(o-r),200*(r-l)]},n.lab.xyz=function(t){let o,r,l;r=(t[0]+16)/116,o=t[1]/500+r,l=r-t[2]/200;const a=A(r,3),c=A(o,3),h=A(l,3);return r=a>.008856?a:(r-16/116)/7.787,o=c>.008856?c:(o-16/116)/7.787,l=h>.008856?h:(l-16/116)/7.787,o*=95.047,r*=100,l*=108.883,[o,r,l]},n.lab.lch=function(t){const o=t[0],r=t[1],l=t[2];let a;return a=360*Math.atan2(l,r)/2/Math.PI,a<0&&(a+=360),[o,Math.sqrt(r*r+l*l),a]},n.lch.lab=function(t){const o=t[0],r=t[1],l=t[2]/360*2*Math.PI;return[o,r*Math.cos(l),r*Math.sin(l)]},n.rgb.ansi16=function(t,o=null){const[r,l,a]=t;let c=o===null?n.rgb.hsv(t)[2]:o;if(c=Math.round(c/50),c===0)return 30;let h=30+(Math.round(a/255)<<2|Math.round(l/255)<<1|Math.round(r/255));return c===2&&(h+=60),h},n.hsv.ansi16=function(t){return n.rgb.ansi16(n.hsv.rgb(t),t[2])},n.rgb.ansi256=function(t){const o=t[0],r=t[1],l=t[2];return o===r&&r===l?o<8?16:o>248?231:Math.round((o-8)/247*24)+232:16+36*Math.round(o/255*5)+6*Math.round(r/255*5)+Math.round(l/255*5)},n.ansi16.rgb=function(t){let o=t%10;if(o===0||o===7)return t>50&&(o+=3.5),o=o/10.5*255,[o,o,o];const r=.5*(1+~~(t>50));return[(1&o)*r*255,(o>>1&1)*r*255,(o>>2&1)*r*255]},n.ansi256.rgb=function(t){if(t>=232){const r=10*(t-232)+8;return[r,r,r]}let o;return t-=16,[Math.floor(t/36)/5*255,Math.floor((o=t%36)/6)/5*255,o%6/5*255]},n.rgb.hex=function(t){const o=(((255&Math.round(t[0]))<<16)+((255&Math.round(t[1]))<<8)+(255&Math.round(t[2]))).toString(16).toUpperCase();return"000000".substring(o.length)+o},n.hex.rgb=function(t){const o=t.toString(16).match(/[a-f0-9]{6}|[a-f0-9]{3}/i);if(!o)return[0,0,0];let r=o[0];o[0].length===3&&(r=r.split("").map(a=>a+a).join(""));const l=parseInt(r,16);return[l>>16&255,l>>8&255,255&l]},n.rgb.hcg=function(t){const o=t[0]/255,r=t[1]/255,l=t[2]/255,a=Math.max(Math.max(o,r),l),c=Math.min(Math.min(o,r),l),h=a-c;let d,p;return d=h<1?c/(1-h):0,p=h<=0?0:a===o?(r-l)/h%6:a===r?2+(l-o)/h:4+(o-r)/h,p/=6,p%=1,[360*p,100*h,100*d]},n.hsl.hcg=function(t){const o=t[1]/100,r=t[2]/100,l=r<.5?2*o*r:2*o*(1-r);let a=0;return l<1&&(a=(r-.5*l)/(1-l)),[t[0],100*l,100*a]},n.hsv.hcg=function(t){const o=t[1]/100,r=t[2]/100,l=o*r;let a=0;return l<1&&(a=(r-l)/(1-l)),[t[0],100*l,100*a]},n.hcg.rgb=function(t){const o=t[0]/360,r=t[1]/100,l=t[2]/100;if(r===0)return[255*l,255*l,255*l];const a=[0,0,0],c=o%1*6,h=c%1,d=1-h;let p=0;switch(Math.floor(c)){case 0:a[0]=1,a[1]=h,a[2]=0;break;case 1:a[0]=d,a[1]=1,a[2]=0;break;case 2:a[0]=0,a[1]=1,a[2]=h;break;case 3:a[0]=0,a[1]=d,a[2]=1;break;case 4:a[0]=h,a[1]=0,a[2]=1;break;default:a[0]=1,a[1]=0,a[2]=d}return p=(1-r)*l,[255*(r*a[0]+p),255*(r*a[1]+p),255*(r*a[2]+p)]},n.hcg.hsv=function(t){const o=t[1]/100,r=o+t[2]/100*(1-o);let l=0;return r>0&&(l=o/r),[t[0],100*l,100*r]},n.hcg.hsl=function(t){const o=t[1]/100,r=t[2]/100*(1-o)+.5*o;let l=0;return r>0&&r<.5?l=o/(2*r):r>=.5&&r<1&&(l=o/(2*(1-r))),[t[0],100*l,100*r]},n.hcg.hwb=function(t){const o=t[1]/100,r=o+t[2]/100*(1-o);return[t[0],100*(r-o),100*(1-r)]},n.hwb.hcg=function(t){const o=t[1]/100,r=1-t[2]/100,l=r-o;let a=0;return l<1&&(a=(r-l)/(1-l)),[t[0],100*l,100*a]},n.apple.rgb=function(t){return[t[0]/65535*255,t[1]/65535*255,t[2]/65535*255]},n.rgb.apple=function(t){return[t[0]/255*65535,t[1]/255*65535,t[2]/255*65535]},n.gray.rgb=function(t){return[t[0]/100*255,t[0]/100*255,t[0]/100*255]},n.gray.hsl=function(t){return[0,0,t[0]]},n.gray.hsv=n.gray.hsl,n.gray.hwb=function(t){return[0,100,t[0]]},n.gray.cmyk=function(t){return[0,0,0,t[0]]},n.gray.lab=function(t){return[t[0],0,0]},n.gray.hex=function(t){const o=255&Math.round(t[0]/100*255),r=((o<<16)+(o<<8)+o).toString(16).toUpperCase();return"000000".substring(r.length)+r},n.rgb.gray=function(t){return[(t[0]+t[1]+t[2])/3/255*100]},we}function It(){if(Ze)return $e;Ze=1;const i=tt();function e(t){const o=function(){const l={},a=Object.keys(i);for(let c=a.length,h=0;h<c;h++)l[a[h]]={distance:-1,parent:null};return l}(),r=[t];for(o[t].distance=0;r.length;){const l=r.pop(),a=Object.keys(i[l]);for(let c=a.length,h=0;h<c;h++){const d=a[h],p=o[d];p.distance===-1&&(p.distance=o[l].distance+1,p.parent=l,r.unshift(d))}}return o}function n(t,o){return function(r){return o(t(r))}}function s(t,o){const r=[o[t].parent,t];let l=i[o[t].parent][t],a=o[t].parent;for(;o[a].parent;)r.unshift(o[a].parent),l=n(i[o[a].parent][a],l),a=o[a].parent;return l.conversion=r,l}return $e=function(t){const o=e(t),r={},l=Object.keys(o);for(let a=l.length,c=0;c<a;c++){const h=l[c];o[h].parent!==null&&(r[h]=s(h,o))}return r},$e}function Bt(){if(Qe)return Je;Qe=1;const i=tt(),e=It(),n={};return Object.keys(i).forEach(s=>{n[s]={},Object.defineProperty(n[s],"channels",{value:i[s].channels}),Object.defineProperty(n[s],"labels",{value:i[s].labels});const t=e(s);Object.keys(t).forEach(o=>{const r=t[o];n[s][o]=function(l){const a=function(...c){const h=c[0];if(h==null)return h;h.length>1&&(c=h);const d=l(c);if(typeof d=="object")for(let p=d.length,u=0;u<p;u++)d[u]=Math.round(d[u]);return d};return"conversion"in l&&(a.conversion=l.conversion),a}(r),n[s][o].raw=function(l){const a=function(...c){const h=c[0];return h==null?h:(h.length>1&&(c=h),l(c))};return"conversion"in l&&(a.conversion=l.conversion),a}(r)})}),Je=n}(function(i){const e=(c,h)=>(...d)=>"\x1B[".concat(c(...d)+h,"m"),n=(c,h)=>(...d)=>{const p=c(...d);return"\x1B[".concat(38+h,";5;").concat(p,"m")},s=(c,h)=>(...d)=>{const p=c(...d);return"\x1B[".concat(38+h,";2;").concat(p[0],";").concat(p[1],";").concat(p[2],"m")},t=c=>c,o=(c,h,d)=>[c,h,d],r=(c,h,d)=>{Object.defineProperty(c,h,{get:()=>{const p=d();return Object.defineProperty(c,h,{value:p,enumerable:!0,configurable:!0}),p},enumerable:!0,configurable:!0})};let l;const a=(c,h,d,p)=>{l===void 0&&(l=Bt());const u=p?10:0,g={};for(const[f,m]of Object.entries(l)){const w=f==="ansi16"?"ansi":f;f===h?g[w]=c(d,u):typeof m=="object"&&(g[w]=c(m[h],u))}return g};Object.defineProperty(i,"exports",{enumerable:!0,get:function(){const c=new Map,h={modifier:{reset:[0,0],bold:[1,22],dim:[2,22],italic:[3,23],underline:[4,24],inverse:[7,27],hidden:[8,28],strikethrough:[9,29]},color:{black:[30,39],red:[31,39],green:[32,39],yellow:[33,39],blue:[34,39],magenta:[35,39],cyan:[36,39],white:[37,39],blackBright:[90,39],redBright:[91,39],greenBright:[92,39],yellowBright:[93,39],blueBright:[94,39],magentaBright:[95,39],cyanBright:[96,39],whiteBright:[97,39]},bgColor:{bgBlack:[40,49],bgRed:[41,49],bgGreen:[42,49],bgYellow:[43,49],bgBlue:[44,49],bgMagenta:[45,49],bgCyan:[46,49],bgWhite:[47,49],bgBlackBright:[100,49],bgRedBright:[101,49],bgGreenBright:[102,49],bgYellowBright:[103,49],bgBlueBright:[104,49],bgMagentaBright:[105,49],bgCyanBright:[106,49],bgWhiteBright:[107,49]}};h.color.gray=h.color.blackBright,h.bgColor.bgGray=h.bgColor.bgBlackBright,h.color.grey=h.color.blackBright,h.bgColor.bgGrey=h.bgColor.bgBlackBright;for(const[d,p]of Object.entries(h)){for(const[u,g]of Object.entries(p))h[u]={open:"\x1B[".concat(g[0],"m"),close:"\x1B[".concat(g[1],"m")},p[u]=h[u],c.set(g[0],g[1]);Object.defineProperty(h,d,{value:p,enumerable:!1})}return Object.defineProperty(h,"codes",{value:c,enumerable:!1}),h.color.close="\x1B[39m",h.bgColor.close="\x1B[49m",r(h.color,"ansi",()=>a(e,"ansi16",t,!1)),r(h.color,"ansi256",()=>a(n,"ansi256",t,!1)),r(h.color,"ansi16m",()=>a(s,"rgb",o,!1)),r(h.bgColor,"ansi",()=>a(e,"ansi16",t,!0)),r(h.bgColor,"ansi256",()=>a(n,"ansi256",t,!0)),r(h.bgColor,"ansi16m",()=>a(s,"rgb",o,!0)),h}})})(et);var _e,nt,zt={stringReplaceAll:(i,e,n)=>{let s=i.indexOf(e);if(s===-1)return i;const t=e.length;let o=0,r="";do r+=i.substr(o,s-o)+e+n,o=s+t,s=i.indexOf(e,o);while(s!==-1);return r+=i.substr(o),r},stringEncaseCRLFWithFirstIndex:(i,e,n,s)=>{let t=0,o="";do{const r=i[s-1]==="\r";o+=i.substr(t,(r?s-1:s)-t)+e+(r?"\r\n":"\n")+n,t=s+1,s=i.indexOf("\n",t)}while(s!==-1);return o+=i.substr(t),o}};const te=et.exports,{stdout:Ee,stderr:xe}={stdout:!1,stderr:!1},{stringReplaceAll:Ht,stringEncaseCRLFWithFirstIndex:Kt}=zt,{isArray:he}=Array,ot=["ansi","ansi","ansi256","ansi16m"],K=Object.create(null);class Wt{constructor(e){return it(e)}}const it=i=>{const e={};return((n,s={})=>{if(s.level&&!(Number.isInteger(s.level)&&s.level>=0&&s.level<=3))throw new Error("The `level` option should be an integer from 0 to 3");const t=Ee?Ee.level:0;n.level=s.level===void 0?t:s.level})(e,i),e.template=(...n)=>lt(e.template,...n),Object.setPrototypeOf(e,de.prototype),Object.setPrototypeOf(e.template,e),e.template.constructor=()=>{throw new Error("`chalk.constructor()` is deprecated. Use `new chalk.Instance()` instead.")},e.template.Instance=Wt,e.template};function de(i){return it(i)}for(const[i,e]of Object.entries(te))K[i]={get(){const n=pe(this,Ae(e.open,e.close,this._styler),this._isEmpty);return Object.defineProperty(this,i,{value:n}),n}};K.visible={get(){const i=pe(this,this._styler,!0);return Object.defineProperty(this,"visible",{value:i}),i}};const rt=["rgb","hex","keyword","hsl","hsv","hwb","ansi","ansi256"];for(const i of rt)K[i]={get(){const{level:e}=this;return function(...n){const s=Ae(te.color[ot[e]][i](...n),te.color.close,this._styler);return pe(this,s,this._isEmpty)}}};for(const i of rt)K["bg"+i[0].toUpperCase()+i.slice(1)]={get(){const{level:e}=this;return function(...n){const s=Ae(te.bgColor[ot[e]][i](...n),te.bgColor.close,this._styler);return pe(this,s,this._isEmpty)}}};const Yt=Object.defineProperties(()=>{},ie(W({},K),{level:{enumerable:!0,get(){return this._generator.level},set(i){this._generator.level=i}}})),Ae=(i,e,n)=>{let s,t;return n===void 0?(s=i,t=e):(s=n.openAll+i,t=e+n.closeAll),{open:i,close:e,openAll:s,closeAll:t,parent:n}},pe=(i,e,n)=>{const s=(...t)=>he(t[0])&&he(t[0].raw)?st(s,lt(s,...t)):st(s,t.length===1?""+t[0]:t.join(" "));return Object.setPrototypeOf(s,Yt),s._generator=i,s._styler=e,s._isEmpty=n,s},st=(i,e)=>{if(i.level<=0||!e)return i._isEmpty?"":e;let n=i._styler;if(n===void 0)return e;const{openAll:s,closeAll:t}=n;if(e.indexOf("\x1B")!==-1)for(;n!==void 0;)e=Ht(e,n.close,n.open),n=n.parent;const o=e.indexOf("\n");return o!==-1&&(e=Kt(e,t,s,o)),s+e+t};let ke;const lt=(i,...e)=>{const[n]=e;if(!he(n)||!he(n.raw))return e.join(" ");const s=e.slice(1),t=[n.raw[0]];for(let o=1;o<n.length;o++)t.push(String(s[o-1]).replace(/[{}\\]/g,"\\$&"),String(n.raw[o]));return ke===void 0&&(ke=function(){if(nt)return _e;nt=1;const o=/(?:\\(u(?:[a-f\d]{4}|\{[a-f\d]{1,6}\})|x[a-f\d]{2}|.))|(?:\{(~)?(\w+(?:\([^)]*\))?(?:\.\w+(?:\([^)]*\))?)*)(?:[ \t]|(?=\r?\n)))|(\})|((?:.|[\r\n\f])+?)/gi,r=/(?:^|\.)(\w+)(?:\(([^)]*)\))?/g,l=/^(['"])((?:\\.|(?!\1)[^\\])*)\1$/,a=/\\(u(?:[a-f\d]{4}|{[a-f\d]{1,6}})|x[a-f\d]{2}|.)|([^\\])/gi,c=new Map([["n","\n"],["r","\r"],["t","	"],["b","\b"],["f","\f"],["v","\v"],["0","\0"],["\\","\\"],["e","\x1B"],["a","\x07"]]);function h(g){const f=g[0]==="u",m=g[1]==="{";return f&&!m&&g.length===5||g[0]==="x"&&g.length===3?String.fromCharCode(parseInt(g.slice(1),16)):f&&m?String.fromCodePoint(parseInt(g.slice(2,-1),16)):c.get(g)||g}function d(g,f){const m=[],w=f.trim().split(/\s*,\s*/g);let _;for(const S of w){const ne=Number(S);if(Number.isNaN(ne)){if(!(_=S.match(l)))throw new Error("Invalid Chalk template style argument: ".concat(S," (in style '").concat(g,"')"));m.push(_[2].replace(a,(gt,oe,Ne)=>oe?h(oe):Ne))}else m.push(ne)}return m}function p(g){r.lastIndex=0;const f=[];let m;for(;(m=r.exec(g))!==null;){const w=m[1];if(m[2]){const _=d(w,m[2]);f.push([w].concat(_))}else f.push([w])}return f}function u(g,f){const m={};for(const _ of f)for(const S of _.styles)m[S[0]]=_.inverse?null:S.slice(1);let w=g;for(const[_,S]of Object.entries(m))if(Array.isArray(S)){if(!(_ in w))throw new Error("Unknown Chalk style: ".concat(_));w=S.length>0?w[_](...S):w[_]}return w}return _e=(g,f)=>{const m=[],w=[];let _=[];if(f.replace(o,(S,ne,gt,oe,Ne,tn)=>{if(ne)_.push(h(ne));else if(oe){const ft=_.join("");_=[],w.push(m.length===0?ft:u(g,m)(ft)),m.push({inverse:gt,styles:p(oe)})}else if(Ne){if(m.length===0)throw new Error("Found extraneous } in Chalk template literal");w.push(u(g,m)(_.join(""))),_=[],m.pop()}else _.push(tn)}),w.push(_.join("")),m.length>0){const S="Chalk template literal is missing ".concat(m.length," closing bracket").concat(m.length===1?"":"s"," (`}`)");throw new Error(S)}return w.join("")},_e}()),ke(i,t.join(""))};Object.defineProperties(de.prototype,K);const Ce=de();Ce.supportsColor=Ee,Ce.stderr=de({level:xe?xe.level:0}),Ce.stderr.supportsColor=xe;var T={exports:{}};const ue=Vt(Ut),Se=ue,Pe=ue,Ft=ue,qt=ue,Me="16.4.5",Gt=/(?:^|^)\s*(?:export\s+)?([\w.-]+)(?:\s*=\s*?|:\s+?)(\s*'(?:\\'|[^'])*'|\s*"(?:\\"|[^"])*"|\s*`(?:\\`|[^`])*`|[^#\r\n]+)?\s*(?:#.*)?(?:$|$)/gm;function Oe(i){console.log("[dotenv@".concat(Me,"][DEBUG] ").concat(i))}function at(i){return i&&i.DOTENV_KEY&&i.DOTENV_KEY.length>0?i.DOTENV_KEY:process.env.DOTENV_KEY&&process.env.DOTENV_KEY.length>0?process.env.DOTENV_KEY:""}function Xt(i,e){let n;try{n=new URL(e)}catch(l){if(l.code==="ERR_INVALID_URL"){const a=new Error("INVALID_DOTENV_KEY: Wrong format. Must be in valid uri format like dotenv://:<EMAIL>/vault/.env.vault?environment=development");throw a.code="INVALID_DOTENV_KEY",a}throw l}const s=n.password;if(!s){const l=new Error("INVALID_DOTENV_KEY: Missing key part");throw l.code="INVALID_DOTENV_KEY",l}const t=n.searchParams.get("environment");if(!t){const l=new Error("INVALID_DOTENV_KEY: Missing environment part");throw l.code="INVALID_DOTENV_KEY",l}const o="DOTENV_VAULT_".concat(t.toUpperCase()),r=i.parsed[o];if(!r){const l=new Error("NOT_FOUND_DOTENV_ENVIRONMENT: Cannot locate environment ".concat(o," in your .env.vault file."));throw l.code="NOT_FOUND_DOTENV_ENVIRONMENT",l}return{ciphertext:r,key:s}}function ct(i){let e=null;if(i&&i.path&&i.path.length>0)if(Array.isArray(i.path))for(const n of i.path)Se.existsSync(n)&&(e=n.endsWith(".vault")?n:"".concat(n,".vault"));else e=i.path.endsWith(".vault")?i.path:"".concat(i.path,".vault");else e=Pe.resolve(process.cwd(),".env.vault");return Se.existsSync(e)?e:null}function ht(i){return i[0]==="~"?Pe.join(Ft.homedir(),i.slice(1)):i}const x={configDotenv:function(i){const e=Pe.resolve(process.cwd(),".env");let n="utf8";const s=!!(i&&i.debug);i&&i.encoding?n=i.encoding:s&&Oe("No encoding is specified. UTF-8 is used by default");let t,o=[e];if(i&&i.path)if(Array.isArray(i.path)){o=[];for(const a of i.path)o.push(ht(a))}else o=[ht(i.path)];const r={};for(const a of o)try{const c=x.parse(Se.readFileSync(a,{encoding:n}));x.populate(r,c,i)}catch(c){s&&Oe("Failed to load ".concat(a," ").concat(c.message)),t=c}let l=process.env;return i&&i.processEnv!=null&&(l=i.processEnv),x.populate(l,r,i),t?{parsed:r,error:t}:{parsed:r}},_configVault:function(i){var e;e="Loading env from encrypted .env.vault",console.log("[dotenv@".concat(Me,"][INFO] ").concat(e));const n=x._parseVault(i);let s=process.env;return i&&i.processEnv!=null&&(s=i.processEnv),x.populate(s,n,i),{parsed:n}},_parseVault:function(i){const e=ct(i),n=x.configDotenv({path:e});if(!n.parsed){const r=new Error("MISSING_DATA: Cannot parse ".concat(e," for an unknown reason"));throw r.code="MISSING_DATA",r}const s=at(i).split(","),t=s.length;let o;for(let r=0;r<t;r++)try{const l=Xt(n,s[r].trim());o=x.decrypt(l.ciphertext,l.key);break}catch(l){if(r+1>=t)throw l}return x.parse(o)},config:function(i){if(at(i).length===0)return x.configDotenv(i);const e=ct(i);return e?x._configVault(i):(n="You set DOTENV_KEY but you are missing a .env.vault file at ".concat(e,". Did you forget to build it?"),console.log("[dotenv@".concat(Me,"][WARN] ").concat(n)),x.configDotenv(i));var n},decrypt:function(i,e){const n=Buffer.from(e.slice(-64),"hex");let s=Buffer.from(i,"base64");const t=s.subarray(0,12),o=s.subarray(-16);s=s.subarray(12,-16);try{const r=qt.createDecipheriv("aes-256-gcm",n,t);return r.setAuthTag(o),"".concat(r.update(s)).concat(r.final())}catch(r){const l=r instanceof RangeError,a=r.message==="Invalid key length",c=r.message==="Unsupported state or unable to authenticate data";if(l||a){const h=new Error("INVALID_DOTENV_KEY: It must be 64 characters long (or more)");throw h.code="INVALID_DOTENV_KEY",h}if(c){const h=new Error("DECRYPTION_FAILED: Please check your DOTENV_KEY");throw h.code="DECRYPTION_FAILED",h}throw r}},parse:function(i){const e={};let n,s=i.toString();for(s=s.replace(/\r\n?/gm,"\n");(n=Gt.exec(s))!=null;){const t=n[1];let o=n[2]||"";o=o.trim();const r=o[0];o=o.replace(/^(['"`])([\s\S]*)\1$/gm,"$2"),r==='"'&&(o=o.replace(/\\n/g,"\n"),o=o.replace(/\\r/g,"\r")),e[t]=o}return e},populate:function(i,e,n={}){const s=!!(n&&n.debug),t=!!(n&&n.override);if(typeof e!="object"){const o=new Error("OBJECT_REQUIRED: Please check the processEnv argument being passed to populate");throw o.code="OBJECT_REQUIRED",o}for(const o of Object.keys(e))Object.prototype.hasOwnProperty.call(i,o)?(t===!0&&(i[o]=e[o]),s&&Oe(t===!0?'"'.concat(o,'" is already defined and WAS overwritten'):'"'.concat(o,'" is already defined and was NOT overwritten'))):i[o]=e[o]}};T.exports.configDotenv=x.configDotenv,T.exports._configVault=x._configVault,T.exports._parseVault=x._parseVault,T.exports.config=x.config,T.exports.decrypt=x.decrypt,T.exports.parse=x.parse,T.exports.populate=x.populate,T.exports=x;const dt="{file}",pt="{line}",ut="{column}";var Zt=Object.defineProperty,Jt=Object.getOwnPropertyDescriptor,y=(i,e,n,s)=>{for(var t,o=s>1?void 0:s?Jt(e,n):e,r=i.length-1;r>=0;r--)(t=i[r])&&(o=(s?t(e,n,o):t(o))||o);return s&&o&&Zt(e,n,o),o};const Te="__code-inspector-unique-id",Qt={ctrlKey:"^control",altKey:"⌥option",metaKey:"⌘command",shiftKey:"shift"},en={ctrlKey:"Ctrl",altKey:"Alt",metaKey:"⊞Windows",shiftKey:"Shift"};class v extends ee{constructor(){super(...arguments),this.hotKeys="shiftKey,altKey",this.port=5678,this.showSwitch=!1,this.autoToggle=!1,this.hideConsole=!1,this.locate=!0,this.copy=!1,this.target="",this.ip="localhost",this.position={top:0,right:0,bottom:0,left:0,padding:{top:0,right:0,bottom:0,left:0},border:{top:0,right:0,bottom:0,left:0},margin:{top:0,right:0,bottom:0,left:0}},this.element={name:"",line:0,column:0,path:""},this.infoClassName={vertical:"",horizon:""},this.infoWidth="300px",this.show=!1,this.showLayerPanel=!1,this.layerPanelPosition={},this.elementTree=[],this.dragging=!1,this.mousePosition={baseX:0,baseY:0,moveX:0,moveY:0},this.open=!1,this.moved=!1,this.hoverSwitch=!1,this.preUserSelect="",this.sendType="xhr",this.isTracking=e=>this.hotKeys&&this.hotKeys.split(",").every(n=>e[n.trim()]),this.getDomPropertyValue=(e,n)=>{const s=window.getComputedStyle(e);return Number(s.getPropertyValue(n).replace("px",""))},this.renderCover=e=>{const{top:n,right:s,bottom:t,left:o}=e.getBoundingClientRect();this.position={top:n,right:s,bottom:t,left:o,border:{top:this.getDomPropertyValue(e,"border-top-width"),right:this.getDomPropertyValue(e,"border-right-width"),bottom:this.getDomPropertyValue(e,"border-bottom-width"),left:this.getDomPropertyValue(e,"border-left-width")},padding:{top:this.getDomPropertyValue(e,"padding-top"),right:this.getDomPropertyValue(e,"padding-right"),bottom:this.getDomPropertyValue(e,"padding-bottom"),left:this.getDomPropertyValue(e,"padding-left")},margin:{top:this.getDomPropertyValue(e,"margin-top"),right:this.getDomPropertyValue(e,"margin-right"),bottom:this.getDomPropertyValue(e,"margin-bottom"),left:this.getDomPropertyValue(e,"margin-left")}};const r=document.documentElement.clientHeight,l=document.documentElement.clientWidth,a=r-t-this.getDomPropertyValue(e,"margin-bottom"),c=l-s-this.getDomPropertyValue(e,"margin-right"),h=n-this.getDomPropertyValue(e,"margin-top"),d=o-this.getDomPropertyValue(e,"margin-left");this.infoClassName={vertical:h>a?h<100?"element-info-top-inner":"element-info-top":a<100?"element-info-bottom-inner":"element-info-bottom",horizon:d>=c?"element-info-right":"element-info-left"},this.infoWidth=Math.max(s-o+this.getDomPropertyValue(e,"margin-right")+this.getDomPropertyValue(e,"margin-left"),Math.min(300,Math.max(d,c)))+"px",this.addGlobalCursorStyle(),this.preUserSelect||(this.preUserSelect=getComputedStyle(document.body).userSelect),document.body.style.userSelect="none";let p=e.getAttribute(H)||e[H]||"";!p&&e.getAttribute("data-astro-source-file")&&(p="".concat(e.getAttribute("data-astro-source-file"),":").concat(e.getAttribute("data-astro-source-loc"),":").concat(e.tagName.toLowerCase()));const u=p.split(":"),g=u[u.length-1],f=Number(u[u.length-2]),m=Number(u[u.length-3]),w=u.slice(0,u.length-3).join(":");this.element={name:g,path:w,line:m,column:f},this.show=!0},this.removeCover=()=>{this.show=!1,this.removeGlobalCursorStyle(),document.body.style.userSelect=this.preUserSelect,this.preUserSelect=""},this.renderLayerPanel=(e,{x:n,y:s})=>{const t=document.documentElement.clientWidth-n,o=document.documentElement.clientHeight-s;let r={};t<300?r.right=t+"px":r.left=n+"px",o<400?r.bottom=o+"px":r.top=s+"px",this.layerPanelPosition=r,this.elementTree=e,this.showLayerPanel=!0},this.removeLayerPanel=()=>{this.showLayerPanel=!1,this.elementTree=[]},this.addGlobalCursorStyle=()=>{if(!document.getElementById(Te)){const e=document.createElement("style");e.setAttribute("id",Te),e.innerText="body * {\n        cursor: pointer !important;\n      }",document.body.appendChild(e)}},this.removeGlobalCursorStyle=()=>{const e=document.getElementById(Te);e&&e.remove()},this.sendXHR=()=>{const e=encodeURIComponent(this.element.path),n="http://".concat(this.ip,":").concat(this.port,"/?file=").concat(e,"&line=").concat(this.element.line,"&column=").concat(this.element.column),s=new XMLHttpRequest;s.open("GET",n,!0),s.send(),s.addEventListener("error",()=>{this.sendType="img",this.sendImg()})},this.sendImg=()=>{const e=encodeURIComponent(this.element.path),n="http://".concat(this.ip,":").concat(this.port,"/?file=").concat(e,"&line=").concat(this.element.line,"&column=").concat(this.element.column);document.createElement("img").src=n},this.buildTargetUrl=()=>{let e=this.target;const{path:n,line:s,column:t}=this.element,o={"{file}":n,"{line}":s,"{column}":t};for(let r in o)e=e.replace(new RegExp(r,"g"),String(o[r]));return e},this.trackCode=()=>{if(this.locate&&(this.sendType==="xhr"?this.sendXHR():this.sendImg()),this.copy){const e=function(n,s,t,o){let r="".concat(n,":").concat(s,":").concat(t);if(typeof o=="string")r=o.replace(dt,n).replace(pt,s.toString()).replace(ut,t.toString());else if(o instanceof Array)return o.map(l=>l.replace(dt,n).replace(pt,s.toString()).replace(ut,t.toString()));return[r]}(this.element.path,String(this.element.line),String(this.element.column),this.copy);this.copyToClipboard(e[0])}this.target&&window.open(this.buildTargetUrl(),"_blank")},this.moveSwitch=e=>{if(e.composedPath().includes(this)?this.hoverSwitch=!0:this.hoverSwitch=!1,this.dragging)return this.moved=!0,this.inspectorSwitchRef.style.left=this.mousePosition.baseX+(this.getMousePosition(e).x-this.mousePosition.moveX)+"px",void(this.inspectorSwitchRef.style.top=this.mousePosition.baseY+(this.getMousePosition(e).y-this.mousePosition.moveY)+"px")},this.isSamePositionNode=(e,n)=>{const s=e.getBoundingClientRect(),t=n.getBoundingClientRect();return s.top===t.top&&s.left===t.left&&s.right===t.right&&s.bottom===t.bottom},this.handleMouseMove=e=>{if((this.isTracking(e)&&!this.dragging||this.open)&&!this.hoverSwitch){const n=e.composedPath();let s;for(let t=0;t<n.length;t++){const o=n[t];if((o.hasAttribute&&o.hasAttribute(H)||o[H])&&(s?this.isSamePositionNode(s,o)&&(s=o):s=o),o.hasAttribute&&o.hasAttribute("data-astro-source-file")){s=o;break}}s?this.renderCover(s):this.removeCover()}else this.removeCover()},this.handleMouseClick=e=>{(this.isTracking(e)||this.open)&&this.show&&(e.stopPropagation(),e.preventDefault(),this.trackCode(),this.removeCover(),this.autoToggle&&(this.open=!1)),setTimeout(()=>{this.removeLayerPanel()})},this.handleContextMenu=e=>{if((this.isTracking(e)&&!this.dragging||this.open)&&!this.hoverSwitch){e.preventDefault();const n=e.composedPath(),s=this.generateNodeTree(n);this.renderLayerPanel(s,{x:e.pageX,y:e.pageY})}},this.generateNodeTree=e=>{let n=null,s=[];for(let t=0;t<e.length;t++){const o=this.parseNode(e[t]);o.isTrackNode&&(n&&n.rect!==o.rect?n.path!==o.path&&(s.push(n),n=o):n=o)}return n&&s.push(n),s},this.parseNode=e=>{var p,u,g,f;let n=((p=e.getAttribute)==null?void 0:p.call(e,H))||e[H]||"";if(!n&&((u=e.getAttribute)!=null&&u.call(e,"data-astro-source-file"))&&(n="".concat((g=e.getAttribute)==null?void 0:g.call(e,"data-astro-source-file"),":").concat((f=e.getAttribute)==null?void 0:f.call(e,"data-astro-source-loc"),":").concat(e.tagName.toLowerCase())),!n)return{isTrackNode:!1};const s=n.split(":"),t=s[s.length-1],o=Number(s[s.length-2]),r=Number(s[s.length-3]),l=s.slice(0,s.length-3).join(":"),{top:a,right:c,bottom:h,left:d}=e.getBoundingClientRect();return{isTrackNode:!0,isAstroNode:!!e.getAttribute("data-astro-source-file"),originNode:e,rect:"".concat(a,",").concat(c,",").concat(h,",").concat(d),name:t,path:l,line:r,column:o}},this.handlePointerDown=e=>{let n=!1,s=e.target;for(;s;){if(s.disabled){n=!0;break}s=s.parentElement}n&&(this.isTracking(e)||this.open)&&this.show&&(e.stopPropagation(),e.preventDefault(),this.trackCode(),this.removeCover(),this.autoToggle&&(this.open=!1))},this.handleKeyUp=e=>{this.isTracking(e)||this.open||this.removeCover()},this.printTip=()=>{const e=navigator.userAgent.toLowerCase(),n=["windows","win32","wow32","win64","wow64"].some(l=>e.toUpperCase().match(l.toUpperCase()))?en:Qt,s=this.hotKeys.split(",").map(l=>"%c"+n[l.trim()]),t=2*s.length+1,o=Array(t).fill("").map((l,a)=>a%2==0?"color: #00B42A; font-family: PingFang SC; font-size: 12px;":"color: #006aff; font-weight: bold; font-family: PingFang SC; font-size: 12px;"),r="%c";console.log("".concat(r,"[code-inspector-plugin]").concat(r,"Press and hold ").concat(s.join(" ".concat(r,"+ "))).concat(r," to enable the feature. (click on page elements to locate the source code in the editor)"),"color: #006aff; font-weight: bolder; font-size: 12px;",...o)},this.getMousePosition=e=>{var n,s;return{x:e instanceof MouseEvent?e.pageX:(n=e.touches[0])==null?void 0:n.pageX,y:e instanceof MouseEvent?e.pageY:(s=e.touches[0])==null?void 0:s.pageY}},this.recordMousePosition=e=>{this.mousePosition={baseX:this.inspectorSwitchRef.offsetLeft,baseY:this.inspectorSwitchRef.offsetTop,moveX:this.getMousePosition(e).x,moveY:this.getMousePosition(e).y},this.dragging=!0,e.preventDefault()},this.handleMouseUp=e=>{this.hoverSwitch=!1,this.dragging&&(this.dragging=!1,e instanceof TouchEvent&&this.switch(e))},this.switch=e=>{this.moved||(this.open=!this.open,e.preventDefault(),e.stopPropagation()),this.moved=!1},this.handleLayerPanelClick=e=>{var o,r,l;const n=e.target;if(!((o=n==null?void 0:n.classList)!=null&&o.contains("inspector-layer"))||!((r=n==null?void 0:n.dataset)!=null&&r.index))return;e.stopPropagation();const s=+n.dataset.index,t=(l=this.elementTree)==null?void 0:l[s];t&&(this.element={name:t.name,column:t.column,line:t.line,path:t.path},this.trackCode(),this.removeLayerPanel())}}copyToClipboard(e){var n;if(typeof((n=navigator==null?void 0:navigator.clipboard)==null?void 0:n.writeText)=="function")navigator.clipboard.writeText(e);else{const s=document.createElement("textarea");s.value=e,document.body.appendChild(s),s.select(),document.execCommand("copy"),document.body.removeChild(s)}}firstUpdated(){this.hideConsole||this.printTip(),window.addEventListener("mousemove",this.handleMouseMove,!0),window.addEventListener("touchmove",this.handleMouseMove,!0),window.addEventListener("mousemove",this.moveSwitch,!0),window.addEventListener("touchmove",this.moveSwitch,!0),window.addEventListener("click",this.handleMouseClick,!0),window.addEventListener("pointerdown",this.handlePointerDown,!0),window.addEventListener("keyup",this.handleKeyUp,!0),window.addEventListener("mouseleave",this.removeCover,!0),window.addEventListener("mouseup",this.handleMouseUp,!0),window.addEventListener("touchend",this.handleMouseUp,!0),window.addEventListener("contextmenu",this.handleContextMenu,!0),this.inspectorSwitchRef.addEventListener("mousedown",this.recordMousePosition),this.inspectorSwitchRef.addEventListener("touchstart",this.recordMousePosition),this.inspectorSwitchRef.addEventListener("click",this.switch),this.inspectorLayersRef.addEventListener("click",this.handleLayerPanelClick)}disconnectedCallback(){window.removeEventListener("mousemove",this.handleMouseMove,!0),window.removeEventListener("touchmove",this.handleMouseMove,!0),window.removeEventListener("mousemove",this.moveSwitch,!0),window.removeEventListener("touchmove",this.moveSwitch,!0),window.removeEventListener("click",this.handleMouseClick,!0),window.removeEventListener("pointerdown",this.handlePointerDown,!0),window.removeEventListener("keyup",this.handleKeyUp,!0),window.removeEventListener("mouseleave",this.removeCover,!0),window.removeEventListener("mouseup",this.handleMouseUp,!0),window.removeEventListener("touchend",this.handleMouseUp,!0),window.removeEventListener("contextmenu",this.handleContextMenu,!0),this.inspectorSwitchRef&&(this.inspectorSwitchRef.removeEventListener("mousedown",this.recordMousePosition),this.inspectorSwitchRef.removeEventListener("touchstart",this.recordMousePosition),this.inspectorSwitchRef.removeEventListener("click",this.switch)),this.inspectorLayersRef&&this.inspectorLayersRef.removeEventListener("click",this.handleLayerPanelClick)}render(){const e={display:this.show?"block":"none",top:this.position.top-this.position.margin.top+"px",left:this.position.left-this.position.margin.left+"px",height:"".concat(this.position.bottom-this.position.top+this.position.margin.bottom+this.position.margin.top,"px"),width:"".concat(this.position.right-this.position.left+this.position.margin.right+this.position.margin.left,"px")},n={borderTopWidth:"".concat(this.position.margin.top,"px"),borderRightWidth:"".concat(this.position.margin.right,"px"),borderBottomWidth:"".concat(this.position.margin.bottom,"px"),borderLeftWidth:"".concat(this.position.margin.left,"px")},s={borderTopWidth:"".concat(this.position.border.top,"px"),borderRightWidth:"".concat(this.position.border.right,"px"),borderBottomWidth:"".concat(this.position.border.bottom,"px"),borderLeftWidth:"".concat(this.position.border.left,"px")},t={borderTopWidth:"".concat(this.position.padding.top,"px"),borderRightWidth:"".concat(this.position.padding.right,"px"),borderBottomWidth:"".concat(this.position.padding.bottom,"px"),borderLeftWidth:"".concat(this.position.padding.left,"px")},o=W({display:this.showLayerPanel?"block":"none"},this.layerPanelPosition);return ae(xt||(xt=Y([' <div\n        class="code-inspector-container"\n        id="code-inspector-container"\n        style=','\n      >\n        <div class="margin-overlay" style=','>\n          <div class="border-overlay" style=','>\n            <div class="padding-overlay" style=','>\n              <div class="content-overlay"></div>\n            </div>\n          </div>\n        </div>\n        <div\n          id="element-info"\n          class="element-info '," ",'"\n          style=','\n        >\n          <div class="element-info-content">\n            <div class="name-line">\n              <div class="element-name">\n                <span class="element-title">&lt;','&gt;</span>\n                <span class="element-tip">click to open IDE</span>\n              </div>\n            </div>\n            <div class="path-line">','</div>\n          </div>\n        </div>\n      </div>\n      <div\n        id="inspector-switch"\n        class="inspector-switch '," ",'"\n        style=',"\n      >\n        ",'\n      </div>\n      <div id="inspector-layers" style=',">\n        ","\n      </div>"])),L(e),L(n),L(s),L(t),this.infoClassName.vertical,this.infoClassName.horizon,L({width:this.infoWidth}),this.element.name,this.element.path,this.open?"active-inspector-switch":"",this.moved?"move-inspector-switch":"",L({display:this.showSwitch?"flex":"none"}),this.open?ae($t||($t=Y(['\n              <svg\n                t="1677801709811"\n                class="icon"\n                viewBox="0 0 1024 1024"\n                version="1.1"\n                xmlns="http://www.w3.org/2000/svg"\n                p-id="1110"\n                xmlns:xlink="http://www.w3.org/1999/xlink"\n                width="1em"\n                height="1em"\n              >\n                <path\n                  d="M546.56 704H128c-19.2 0-32-12.8-32-32V256h704v194.56c10.928 1.552 21.648 3.76 32 6.832V128c0-35.2-28.8-64-64-64H128C92.8 64 64 92.8 64 128v544c0 35.2 28.8 64 64 64h425.392a221.936 221.936 0 0 1-6.848-32zM96 128c0-19.2 12.8-32 32-32h640c19.2 0 32 12.8 32 32v96H96V128z"\n                  fill="#34495E"\n                  p-id="1111"\n                ></path>\n                <path\n                  d="M416 160m-32 0a32 32 0 1 0 64 0 32 32 0 1 0-64 0Z"\n                  fill="#00B42A"\n                  p-id="1112"\n                ></path>\n                <path\n                  d="M288 160m-32 0a32 32 0 1 0 64 0 32 32 0 1 0-64 0Z"\n                  fill="#F7BA1E"\n                  p-id="1113"\n                ></path>\n                <path\n                  d="M160 160m-32 0a32 32 0 1 0 64 0 32 32 0 1 0-64 0Z"\n                  fill="#F53F3F"\n                  p-id="1114"\n                ></path>\n                <path\n                  d="M382.848 658.928l99.376-370.88 30.912 8.272-99.36 370.88zM318.368 319.2L160 477.6l158.4 158.4 22.64-22.624-135.792-135.776 135.776-135.776zM768 480c-13.088 0-25.888 1.344-38.24 3.84l6.24-6.24-158.4-158.4-22.64 22.624 135.792 135.776-135.776 135.776 22.656 22.624 2.208-2.224a190.768 190.768 0 0 0 30.928 148.08l-116.672 116.656c-10.24 10.24-10.24 26.896 0 37.136l27.76 27.76c5.12 5.12 11.84 7.68 18.56 7.68s13.456-2.56 18.56-7.68l120.992-120.96A190.56 190.56 0 0 0 768 864c105.872 0 192-86.128 192-192s-86.128-192-192-192z m-159.12 193.136c0-88.224 71.776-160 160-160 10.656 0 21.04 1.152 31.12 3.152V672c0 19.2-12.8 32-32 32h-156a160.144 160.144 0 0 1-3.12-30.864z m-68.464 263.584l-19.632-19.632 110.336-110.336c6.464 6.656 13.392 12.848 20.752 18.528l-111.456 111.44z m228.464-103.584c-65.92 0-122.576-40.096-147.056-97.136H768c35.2 0 64-28.8 64-64v-145.776c56.896 24.544 96.88 81.12 96.88 146.912 0 88.224-71.776 160-160 160z"\n                  fill="#006AFF"\n                  p-id="1115"\n                ></path>\n                <path\n                  d="M864.576 672c0 52.928-43.072 96-96 96v32a128 128 0 0 0 128-128h-32z"\n                  fill="#34495E"\n                  p-id="1116"\n                ></path>\n              </svg>\n            ']))):ae(_t||(_t=Y(['<svg\n              t="1677801709811"\n              class="icon"\n              viewBox="0 0 1024 1024"\n              version="1.1"\n              xmlns="http://www.w3.org/2000/svg"\n              p-id="1110"\n              xmlns:xlink="http://www.w3.org/1999/xlink"\n              width="1em"\n              height="1em"\n            >\n              <path\n                d="M546.56 704H128c-19.2 0-32-12.8-32-32V256h704v194.56c10.928 1.552 21.648 3.76 32 6.832V128c0-35.2-28.8-64-64-64H128C92.8 64 64 92.8 64 128v544c0 35.2 28.8 64 64 64h425.392a221.936 221.936 0 0 1-6.848-32zM96 128c0-19.2 12.8-32 32-32h640c19.2 0 32 12.8 32 32v96H96V128z"\n                fill="currentColor"\n                p-id="1111"\n              ></path>\n              <path\n                d="M416 160m-32 0a32 32 0 1 0 64 0 32 32 0 1 0-64 0Z"\n                fill="currentColor"\n                p-id="1112"\n              ></path>\n              <path\n                d="M288 160m-32 0a32 32 0 1 0 64 0 32 32 0 1 0-64 0Z"\n                fill="currentColor"\n                p-id="1113"\n              ></path>\n              <path\n                d="M160 160m-32 0a32 32 0 1 0 64 0 32 32 0 1 0-64 0Z"\n                fill="currentColor"\n                p-id="1114"\n              ></path>\n              <path\n                d="M382.848 658.928l99.376-370.88 30.912 8.272-99.36 370.88zM318.368 319.2L160 477.6l158.4 158.4 22.64-22.624-135.792-135.776 135.776-135.776zM768 480c-13.088 0-25.888 1.344-38.24 3.84l6.24-6.24-158.4-158.4-22.64 22.624 135.792 135.776-135.776 135.776 22.656 22.624 2.208-2.224a190.768 190.768 0 0 0 30.928 148.08l-116.672 116.656c-10.24 10.24-10.24 26.896 0 37.136l27.76 27.76c5.12 5.12 11.84 7.68 18.56 7.68s13.456-2.56 18.56-7.68l120.992-120.96A190.56 190.56 0 0 0 768 864c105.872 0 192-86.128 192-192s-86.128-192-192-192z m-159.12 193.136c0-88.224 71.776-160 160-160 10.656 0 21.04 1.152 31.12 3.152V672c0 19.2-12.8 32-32 32h-156a160.144 160.144 0 0 1-3.12-30.864z m-68.464 263.584l-19.632-19.632 110.336-110.336c6.464 6.656 13.392 12.848 20.752 18.528l-111.456 111.44z m228.464-103.584c-65.92 0-122.576-40.096-147.056-97.136H768c35.2 0 64-28.8 64-64v-145.776c56.896 24.544 96.88 81.12 96.88 146.912 0 88.224-71.776 160-160 160z"\n                fill="currentColor"\n                p-id="1115"\n              ></path>\n              <path\n                d="M864.576 672c0 52.928-43.072 96-96 96v32a128 128 0 0 0 128-128h-32z"\n                fill="currentColor"\n                p-id="1116"\n              ></path>\n            </svg>']))),L(o),this.elementTree.map((r,l)=>ae(Et||(Et=Y(['<div class="inspector-layer" data-index=','>\n            <div class="name-line">\n              <div class="element-name">\n                <span class="element-title">&lt;','&gt;</span>\n                <span class="element-tip">click to open IDE</span>\n              </div>\n            </div>\n            <div class="path-line">',"</div>\n          </div> "])),l,r.name,r.path)))}}v.styles=((i,...e)=>{const n=i.length===1?i[0]:e.reduce((s,t,o)=>s+(r=>{if(r._$cssResult$===!0)return r.cssText;if(typeof r=="number")return r;throw Error("Value passed to 'css' function must be a 'css' function result: "+r+". Use 'unsafeCSS' to pass non-literal values, but take care to ensure page security.")})(t)+i[o+1],i[0]);return new re(n,i,F)})(At||(At=Y(["\n    .code-inspector-container {\n      position: fixed;\n      pointer-events: none;\n      z-index: 9999999999999;\n      font-family: 'PingFang SC';\n      .margin-overlay {\n        position: absolute;\n        inset: 0;\n        border-style: solid;\n        border-color: rgba(255, 155, 0, 0.3);\n        .border-overlay {\n          position: absolute;\n          inset: 0;\n          border-style: solid;\n          border-color: rgba(255, 200, 50, 0.3);\n          .padding-overlay {\n            position: absolute;\n            inset: 0;\n            border-style: solid;\n            border-color: rgba(77, 200, 0, 0.3);\n            .content-overlay {\n              position: absolute;\n              inset: 0;\n              background: rgba(120, 170, 210, 0.7);\n            }\n          }\n        }\n      }\n    }\n    .element-info {\n      position: absolute;\n    }\n    .element-info-content,\n    #inspector-layers {\n      max-width: 100%;\n      font-size: 12px;\n      color: #000;\n      background-color: #fff;\n      word-break: break-all;\n      box-shadow: 0 0 10px rgba(0, 0, 0, 0.25);\n      box-sizing: border-box;\n      padding: 4px 8px;\n      border-radius: 4px;\n    }\n    .element-info-top {\n      top: -4px;\n      transform: translateY(-100%);\n    }\n    .element-info-bottom {\n      top: calc(100% + 4px);\n    }\n    .element-info-top-inner {\n      top: 4px;\n    }\n    .element-info-bottom-inner {\n      bottom: 4px;\n    }\n    .element-info-left {\n      left: 0;\n      display: flex;\n      justify-content: flex-start;\n    }\n    .element-info-right {\n      right: 0;\n      display: flex;\n      justify-content: flex-end;\n    }\n    .element-name .element-title {\n      color: coral;\n      font-weight: bold;\n    }\n    .element-name .element-tip {\n      color: #006aff;\n    }\n    .path-line {\n      color: #333;\n      line-height: 12px;\n      margin-top: 4px;\n    }\n    .inspector-switch {\n      position: fixed;\n      z-index: 9999999999999;\n      top: 50%;\n      right: 24px;\n      font-size: 22px;\n      transform: translateY(-100%);\n      display: flex;\n      align-items: center;\n      justify-content: center;\n      background-color: rgba(255, 255, 255, 0.8);\n      color: #555;\n      height: 32px;\n      width: 32px;\n      border-radius: 50%;\n      box-shadow: 0px 1px 2px -2px rgba(0, 0, 0, 0.2),\n        0px 3px 6px 0px rgba(0, 0, 0, 0.16),\n        0px 5px 12px 4px rgba(0, 0, 0, 0.12);\n      cursor: pointer;\n    }\n    .active-inspector-switch {\n      color: #006aff;\n    }\n    .move-inspector-switch {\n      cursor: move;\n    }\n    #inspector-layers {\n      padding: 8px;\n      position: fixed;\n      user-select: none;\n      background-color: #fff;\n      z-index: 9999999999999;\n      border-radius: 8px;\n\n      .inspector-layer {\n        cursor: pointer;\n        pointer-events: auto;\n        border-radius: 4px;\n        padding: 8px;\n      }\n      .inspector-layer * {\n        pointer-events: none;\n      }\n      .inspector-layer:hover {\n        background-color: #f0f0f0;\n      }\n      .inspector-layer:first-child {\n        background-color: #6cf;\n      }\n    }\n  "]))),y([M()],v.prototype,"hotKeys",2),y([M()],v.prototype,"port",2),y([M()],v.prototype,"showSwitch",2),y([M()],v.prototype,"autoToggle",2),y([M()],v.prototype,"hideConsole",2),y([M()],v.prototype,"locate",2),y([M()],v.prototype,"copy",2),y([M()],v.prototype,"target",2),y([M()],v.prototype,"ip",2),y([C()],v.prototype,"position",2),y([C()],v.prototype,"element",2),y([C()],v.prototype,"infoClassName",2),y([C()],v.prototype,"infoWidth",2),y([C()],v.prototype,"show",2),y([C()],v.prototype,"showLayerPanel",2),y([C()],v.prototype,"layerPanelPosition",2),y([C()],v.prototype,"elementTree",2),y([C()],v.prototype,"dragging",2),y([C()],v.prototype,"mousePosition",2),y([C()],v.prototype,"open",2),y([C()],v.prototype,"moved",2),y([C()],v.prototype,"hoverSwitch",2),y([C()],v.prototype,"preUserSelect",2),y([C()],v.prototype,"sendType",2),y([Fe("#inspector-switch")],v.prototype,"inspectorSwitchRef",2),y([Fe("#inspector-layers")],v.prototype,"inspectorLayersRef",2),customElements.get("code-inspector-component")||customElements.define("code-inspector-component",v),$.CodeInspectorComponent=v,Object.defineProperty($,Symbol.toStringTag,{value:"Module"})});

"use strict";(()=>{var a=Object.defineProperty;var u=(i,t,e)=>t in i?a(i,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):i[t]=e;var o=(i,t,e)=>u(i,typeof t!="symbol"?t+"":t,e);var l=class{constructor(t){o(this,"_eventListeners");o(this,"_maxListeners");o(this,"_logger");this._eventListeners=new Map,this._maxListeners=100,this._logger=t?.logger}once(t,e){let s=(...r)=>{this.off(t,s),e(...r)};return this.on(t,s),this}listenerCount(t){if(!t)return this.getAllListeners().length;let e=this._eventListeners.get(t);return e?e.length:0}eventNames(){return[...this._eventListeners.keys()]}rawListeners(t){return t?this._eventListeners.get(t)??[]:this.getAllListeners()}prependListener(t,e){let s=this._eventListeners.get(t)??[];return s.unshift(e),this._eventListeners.set(t,s),this}prependOnceListener(t,e){let s=(...r)=>{this.off(t,s),e(...r)};return this.prependListener(t,s),this}maxListeners(){return this._maxListeners}addListener(t,e){return this.on(t,e),this}on(t,e){this._eventListeners.has(t)||this._eventListeners.set(t,[]);let s=this._eventListeners.get(t);return s&&(s.length>=this._maxListeners&&console.warn(`MaxListenersExceededWarning: Possible event memory leak detected. ${s.length+1} ${t} listeners added. Use setMaxListeners() to increase limit.`),s.push(e)),this}removeListener(t,e){return this.off(t,e),this}off(t,e){let s=this._eventListeners.get(t)??[],r=s.indexOf(e);return r!==-1&&s.splice(r,1),s.length===0&&this._eventListeners.delete(t),this}emit(t,...e){let s=!1,r=this._eventListeners.get(t);if(r&&r.length>0)for(let n of r)n(...e),s=!0;return s}listeners(t){return this._eventListeners.get(t)??[]}removeAllListeners(t){return t?this._eventListeners.delete(t):this._eventListeners.clear(),this}setMaxListeners(t){this._maxListeners=t;for(let e of this._eventListeners.values())e.length>t&&e.splice(t)}getAllListeners(){let t=new Array;for(let e of this._eventListeners.values())t=[...t,...e];return t}};var h=class extends l{constructor(e){super({logger:e?.logger});o(this,"_hooks");o(this,"_throwHookErrors",!1);this._hooks=new Map,e?.throwHookErrors!==void 0&&(this._throwHookErrors=e.throwHookErrors)}get hooks(){return this._hooks}get throwHookErrors(){return this._throwHookErrors}set throwHookErrors(e){this._throwHookErrors=e}get logger(){return this._logger}set logger(e){this._logger=e}onHook(e,s){let r=this._hooks.get(e);r?r.push(s):this._hooks.set(e,[s])}addHook(e,s){this.onHook(e,s)}onHooks(e){for(let s of e)this.onHook(s.event,s.handler)}prependHook(e,s){let r=this._hooks.get(e);r?r.unshift(s):this._hooks.set(e,[s])}prependOnceHook(e,s){let r=async(...n)=>(this.removeHook(e,r),s(...n));this.prependHook(e,r)}onceHook(e,s){let r=async(...n)=>(this.removeHook(e,r),s(...n));this.onHook(e,r)}removeHook(e,s){let r=this._hooks.get(e);if(r){let n=r.indexOf(s);n!==-1&&r.splice(n,1)}}removeHooks(e){for(let s of e)this.removeHook(s.event,s.handler)}async hook(e,...s){let r=this._hooks.get(e);if(r)for(let n of r)try{await n(...s)}catch(m){let g=`${e}: ${m.message}`;if(this.emit("error",new Error(g)),this._logger&&this._logger.error(g),this._throwHookErrors)throw new Error(g)}}async callHook(e,...s){await this.hook(e,...s)}getHooks(e){return this._hooks.get(e)}clearHooks(){this._hooks.clear()}};})();
//# sourceMappingURL=index.global.js.map
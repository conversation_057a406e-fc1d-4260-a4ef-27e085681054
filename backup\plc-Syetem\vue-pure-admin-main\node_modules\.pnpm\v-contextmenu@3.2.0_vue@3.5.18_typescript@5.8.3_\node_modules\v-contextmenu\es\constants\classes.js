var CLASSES = {
    contextmenu: 'v-contextmenu', // 根元素
    contextmenuIcon: 'v-contextmenu-icon', // icon
    contextmenuInner: 'v-contextmenu-inner', // 菜单根元素
    contextmenuDivider: 'v-contextmenu-divider', // 分割线
    contextmenuItem: 'v-contextmenu-item', // 单个菜单
    contextmenuItemHover: 'v-contextmenu-item--hover', // 单个菜单激活状态
    contextmenuItemDisabled: 'v-contextmenu-item--disabled', // 单个菜单禁用状态
    contextmenuGroup: 'v-contextmenu-group', // 按钮组根元素
    contextmenuGroupTitle: 'v-contextmenu-group__title', // 按钮组标题
    contextmenuGroupMenus: 'v-contextmenu-group__menus', // 按钮组按钮容器
    contextmenuSubmenu: 'v-contextmenu-submenu', // 子菜单容器
    contextmenuSubmenuTitle: 'v-contextmenu-submenu__title', // 子菜单标题
    contextmenuSubmenuMenus: 'v-contextmenu-submenu__menus', // 子菜单
    contextmenuSubmenuMenusTop: 'v-contextmenu-submenu__menus--top', // 子菜单 Top
    contextmenuSubmenuMenusRight: 'v-contextmenu-submenu__menus--right', // 子菜单 Right
    contextmenuSubmenuMenusBottom: 'v-contextmenu-submenu__menus--bottom', // 子菜 Bottom单
    contextmenuSubmenuMenusLeft: 'v-contextmenu-submenu__menus--left', // 子菜单 Left
    contextmenuSubmenuArrow: 'v-contextmenu-submenu__arrow', // 子菜单标题 icon
};
export default CLASSES;
//# sourceMappingURL=classes.js.map
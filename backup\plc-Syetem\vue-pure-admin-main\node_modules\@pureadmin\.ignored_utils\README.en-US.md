<br>
<p align="center">
  <a href="https://pure-admin-utils.netlify.app" target="_blank">
    <img src="https://xiaoxian521.github.io/hyperlink/img/pureadmin-utils.png" alt="pure-admin-utils" width="220" />
  </a>
</p>

<p align="center">
@pureadmin/utils
<br />
Commonly used utility functions (utils、hooks)
</p>

<p align="center">
<a href="https://www.npmjs.com/package/@pureadmin/utils" target="__blank"><img src="https://img.shields.io/npm/v/@pureadmin/utils?color=a1b858&label=" alt="NPM version"></a>
<a href="https://www.npmjs.com/package/@pureadmin/utils" target="__blank"><img alt="NPM Downloads" src="https://img.shields.io/npm/dm/@pureadmin/utils?color=50a36f&label="></a>
</p>

**English** | [中文](./README.md)

## 👀 Documentation

- [Document Address](https://pure-admin-utils.netlify.app/)

## 🚀 Features

- 🌎 **Run in any `JavaScript` environment**: Supports `Node.js`, browsers and any `JavaScript` framework
- 📡 **Can be referenced through `CDN`**: Supports both `jsdelivr` and `unpkg`
- ⚡️ **Completely tree-shaking**: Comes with `Tree-shaking`, only packages the imported code
- 💫 **Zero Dependencies**: Zero `Dependencies` dependencies, only the project itself will be installed
- 🦾 **Strong Type**: Written in `TypeScript`, with powerful type derivation hints
- 💯 **`100%` test**: `100%` test coverage, `100%` test pass rate

## 📦 Install

```bash
# npm
npm install @pureadmin/utils

# or yarn
yarn add @pureadmin/utils

# or pnpm
pnpm add @pureadmin/utils
```

## 📡 `CDN`

```html
<!-- provide a global variable `PureUtils`, which contains all methods
 -->

<!-- jsdelivr -->
<script src="//cdn.jsdelivr.net/npm/@pureadmin/utils"></script>

<!-- unpkg -->
<script src="//unpkg.com/@pureadmin/utils"></script>
```

## 📚 Demos

Integrate `vue-vite`, `vue-cli`, `preact`, `react`, `solid`, `svelte`, `nuxt3`, `next`, `node`, `html` usage demos

[View sample code](https://github.com/pure-admin/pure-admin-utils-docs/tree/master/playgrounds)

## LICENSE

[MIT © 2022-present, pure-admin](./LICENSE)

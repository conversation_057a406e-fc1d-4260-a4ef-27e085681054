'use strict';

var Ht = require('fs');
var path = require('path');

function _interopNamespace(e) {
	if (e && e.__esModule) return e;
	var n = Object.create(null);
	if (e) {
		Object.keys(e).forEach(function (k) {
			if (k !== 'default') {
				var d = Object.getOwnPropertyDescriptor(e, k);
				Object.defineProperty(n, k, d.get ? d : {
					enumerable: true,
					get: function () { return e[k]; }
				});
			}
		});
	}
	n.default = e;
	return Object.freeze(n);
}

var Ht__namespace = /*#__PURE__*/_interopNamespace(Ht);

var z=Object.defineProperty;var yt=Object.getOwnPropertyDescriptor;var ht=Object.getOwnPropertyNames;var bt=Object.prototype.hasOwnProperty;var xt=(t,e)=>{for(var n in e)z(t,n,{get:e[n],enumerable:!0});},Y=(t,e,n,r)=>{if(e&&typeof e=="object"||typeof e=="function")for(let o of ht(e))!bt.call(t,o)&&o!==n&&z(t,o,{get:()=>e[o],enumerable:!(r=yt(e,o))||r.enumerable});return t},F=(t,e,n)=>(Y(t,e,"default"),n&&Y(n,e,"default"));var Qt=t=>t.replace(/^\s*/,""),Xt=t=>t.replace(/(\s*$)/g,""),vt=t=>t.replace(/^\s*|\s*$/g,""),K=t=>t.replace(/\s*/g,"");var wt=Object.prototype.toString;function O(t,e){return wt.call(t)===`[object ${e}]`}function $(t){return t!==null&&O(t,"Object")}function ne(t){let e;return Object.prototype.toString.call(t)==="[object Object]"&&(e=Object.getPrototypeOf(t),e===null||e==Object.getPrototypeOf({}))}function At(t){return typeof t<"u"}function W(t){return !At(t)}function G(t){return t===null}function re(t){return G(t)&&W(t)}function Mt(t){return G(t)||W(t)}function St(t){return h(t)||S(t)?t.length===0:t instanceof Map||t instanceof Set?t.size===0:$(t)?Object.keys(t).length===0:!1}function Z(t){return !!(St(t)||Mt(t))}function oe(t){return O(t,"Date")}function se(t){return t%4===0&&(t%100!==0||t%400===0)}function L(t){return O(t,"Number")}function ie(t){if(!t||!(typeof t=="object"||typeof t=="function"))return !1;let e=t;return e instanceof Promise||V(e.then)&&V(e.catch)&&(Object.prototype.toString.call(e)==="[object Promise]"||e.constructor?.name==="Promise")}function S(t){return O(t,"String")}function V(t){return typeof t=="function"}function ae(t){return O(t,"Boolean")}function ue(t){return O(t,"RegExp")}function h(t){return t&&Array.isArray(t)}function ce(t){if(S(t))try{let e=JSON.parse(t);return !!($(e)&&e)}catch{return !1}return !1}function fe(t){return typeof window<"u"&&O(t,"Window")}function le(t){return $(t)&&!!t.tagName}var pe=t=>{if(t===""||t.trim()==="")return !1;try{return btoa(atob(t))==t}catch{return !1}},me=t=>/^#[a-fA-F0-9]{3}$|#[a-fA-F0-9]{6}$/.test(t),ge=t=>/^rgb\((\s*\d+\s*,?){3}\)$/.test(t),de=t=>/^rgba\((\s*\d+\s*,\s*){3}\s*\d(\.\d+)?\s*\)$/.test(t),Ot=typeof window>"u",ye=!Ot,x=typeof document<"u";function he(t){let e="^(?:(https?|ftp|rtsp|mms|ws|wss):\\/\\/)?(?:\\S+(?::\\S*)?@)?(?:(?:localhost)|(?:[1-9]\\d{0,2}(?:\\.\\d{1,3}){3})|(?:$[0-9a-fA-F:]+$)|(?:(?:[a-zA-Z0-9-_]+\\.)+[a-zA-Z]{2,63}))(?::\\d{1,5})?(?:[/?#]\\S*)?$";return new RegExp(e,"i").test(t)}function be(t){return /^[1](([3][0-9])|([4][0,1,4-9])|([5][0-3,5-9])|([6][2,5,6,7])|([7][0-8])|([8][0-9])|([9][0-3,5-9]))[0-9]{8}$/.test(t)}function xe(t){return /^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/.test(t)}function we(t){return /^[1-9][0-9]{4,12}$/.test(t.toString())}function Ae(t){return /^[1-9][0-9]{5}$/.test(t.toString())}function Me(t,e){let n="[\u4E00-\u9FFF",r="\u3002\uFF1B\uFF0C\uFF1A\u201C\u201D\uFF08\uFF09\u3001\uFF1F\u300A\u300B\uFF01\u3010\u3011\uFFE5";if(e?.pure&&(t=K(t)),e?.all){let o;return e?.unicode?o=new RegExp(`(^${n}${r}${e?.unicode}]+$)`,"g"):e?.replaceUnicode?o=new RegExp(`(^${n}${e?.replaceUnicode}]+$)`,"g"):o=new RegExp(`(^${n}${r}]+$)`,"g"),o.test(t)}else {let o;return e?.unicode?o=new RegExp(`(${n}${r}${e?.unicode}]+)`,"g"):e?.replaceUnicode?o=new RegExp(`(${n}${e?.replaceUnicode}]+)`,"g"):o=new RegExp(`(${n}${r}]+)`,"g"),o.test(t)}}function Se(t){return /^[a-z]+$/.test(t)}function Oe(t){return /^[A-Z]+$/.test(t)}function Te(t){return /^[A-Za-z]+$/.test(t)}function Ee(t){return !!new RegExp(/\s+/g).test(t)}function ke(t){return /<("[^"]*"|'[^']*'|[^'">])*>/.test(t)}var Tt=t=>{let e=parseFloat(t);if(isNaN(e))return !1;e=Math.round(t*100)/100;let n=e.toString(),r=n.indexOf(".");for(r<0&&(r=n.length,n+=".");n.length<=r+2;)n+="0";return n},je=(t,e=!0)=>{let n=t;n=t*.01,n+="";let r=n.indexOf(".")>-1?/(\d{1,3})(?=(?:\d{3})+\.)/g:/(\d{1,3})(?=(?:\d{3})+$)/g;return n=n.replace(r,"$1"),e?Tt(n):n},Pe=(t,e=100)=>{let n=0,r=t.toString(),o=e.toString();try{n+=r.split(".")[1].length;}catch{}try{n+=o.split(".")[1].length;}catch{}return Number(r.replace(".",""))*Number(o.replace(".",""))/Math.pow(10,n)},Re=t=>(t=t.toString(),t.includes(".")?t.toString().split(".")[1].length:0),Ce=(t,e="\u6574")=>{let n=["\u96F6","\u58F9","\u8D30","\u53C1","\u8086","\u4F0D","\u9646","\u67D2","\u634C","\u7396"],r=["","\u62FE","\u4F70","\u4EDF"],o=["","\u4E07","\u4EBF","\u5146"],s=["\u89D2","\u5206","\u6BEB","\u5398"],i="\u5143",u,c,f="",p;if(t==""||(t=parseFloat(t),t>=1e15))return "";if(t==0)return f=n[0]+i,f;t=t.toString(),t.indexOf(".")==-1?(u=t,c="",i=`\u5143${e}`):(p=t.split("."),u=p[0],c=p[1].substr(0,4));let l=0,m=0,g,d,b,w,k=0;if(parseInt(u,10)>0){l=0,m=u.length;for(let A=0;A<m;A++)g=u.substr(A,1),d=m-A-1,w=d/4,b=d%4,g=="0"?l++:(l>0&&(f+=n[0]),l=0,f+=n[parseInt(g)]+r[b]),b==0&&l<4&&(f+=o[w]);f+=i;}if(c!=""){k=c.length;for(let A=0;A<k;A++)g=c.substr(A,1),g!="0"&&(f+=n[Number(g)]+s[A]);}return f==""&&(f+=n[0]+i),f},Ie=(t,e)=>{if(Z(t))return "";let n=e?.digit??0;if(e?.round??!1)return new Intl.NumberFormat("en-US",{minimumFractionDigits:n,maximumFractionDigits:n}).format(t);{let o=t.toString(),[s,i]=o.split("."),a="";return i?a=i.slice(0,n).padEnd(n,"0"):n>0&&(a="0".repeat(n)),s.replace(/\B(?=(\d{3})+(?!\d))/g,",")+(a?"."+a:"")}};function J(t){if(!x)return;let e=t.split(","),r=e[0].match(/:(.*?);/)[1],o=window.atob(e[1]),s=o.length,i=new Uint8Array(s);for(;s--;)i[s]=o.charCodeAt(s);return new Blob([i],{type:r})}function Q(t,e,n){return new Promise((r,o)=>{x||o();let s=document.createElement("CANVAS"),i=s.getContext("2d"),a=new Image;a.crossOrigin="",a.onload=function(){if(!s||!i)return o();s.height=a.height,s.width=a.width,i.drawImage(a,0,0);let u=s.toDataURL(e||"image/png",n);s=null,r(u);},a.src=t;})}function Le(t,e={}){return new Promise((n,r)=>{x||r();let{red:o=.3,green:s=.59,blue:i=.11,scale:a=1}=e,u=new Image;new URL(t,window.location.href).origin!==window.location.origin&&(u.crossOrigin="anonymous",u.referrerPolicy="no-referrer"),u.onload=()=>{let c=document.createElement("canvas"),f=c.getContext("2d");if(!f){r("\u65E0\u6CD5\u83B7\u53D6\u753B\u5E03\u4E0A\u4E0B\u6587");return}let p=u.width*a,l=u.height*a;c.width=p,c.height=l,f.drawImage(u,0,0,p,l);let m;try{m=f.getImageData(0,0,c.width,c.height);}catch(d){r(d);return}let g=m.data;for(let d=0;d<g.length;d+=4){let b=g[d]*o+g[d+1]*s+g[d+2]*i;g[d]=g[d+1]=g[d+2]=b;}f.putImageData(m,0,0),n(c.toDataURL());},u.onerror=()=>{r("\u56FE\u7247\u52A0\u8F7D\u5931\u8D25");},u.src=t;})}var Et=Object.prototype.toString;function kt(t,e){return t&&t.hasOwnProperty?t.hasOwnProperty(e):!1}function $t(t,e,n){if(t)if(t.forEach)t.forEach(e,n);else for(let r=0,o=t.length;r<o;r++)e.call(n,t[r],r,t);}function Dt(t,e,n){if(t)for(let r in t)kt(t,r)&&e.call(n,t[r],r,t);}function U(t,e){let n=t.__proto__.constructor;return e?new n(e):new n}function j(t,e){return e?N(t,e):t}function N(t,e){if(t)switch(Et.call(t)){case"[object Object]":{let n=Object.create(t.__proto__);return Dt(t,function(r,o){n[o]=j(r,e);}),n}case"[object Date]":case"[object RegExp]":return U(t,t.valueOf());case"[object Array]":case"[object Arguments]":{let n=[];return $t(t,function(r){n.push(j(r,e));}),n}case"[object Set]":{let n=U(t);return n.forEach(function(r){n.add(j(r,e));}),n}case"[object Map]":{let n=U(t);return n.forEach(function(r){n.set(j(r,e));}),n}}return t}function Ne(t,e){return t&&N(t,e)}function He(t){return t&&N(t,!0)}var Ye=t=>{let e=t?.type??"rgb",n=t?.num??0;if(n===0)switch(e){case"rgb":return x?window.crypto.getRandomValues(new Uint8Array(3)).toString():void 0;case"hex":return `#${Math.floor(Math.random()*16777215).toString(16).padStart(6,`${Math.random()*10}`)}`;case"hsl":return [360*Math.random(),`${100*Math.random()}%`,`${100*Math.random()}%`].toString()}else switch(e){case"rgb":let r=[];if(!x)return;for(let i=0;i<n;i++)r.push(window.crypto.getRandomValues(new Uint8Array(3)).toString());return r;case"hex":let o=[];for(let i=0;i<n;i++)o.push(`#${Math.floor(Math.random()*16777215).toString(16).padStart(6,`${Math.random()*10}`)}`);return o;case"hsl":let s=[];for(let i=0;i<n;i++)s.push([360*Math.random(),`${100*Math.random()}%`,`${100*Math.random()}%`].toString());return s}};function D(t,e){return Math.floor(Math.random()*(e-t+1))+t}function H(t,e,n){return `hsl(${t}, ${e}%, ${n}%)`}var ze=(t={})=>{let{baseHue:e=D(0,360),hueOffset:n=30,saturation:r=70,lightness:o=60,angle:s=135,randomizeHue:i=!1,randomizeSaturation:a=!1,randomizeLightness:u=!1,randomizeAngle:c=!1}=t,f=i?D(0,360):e,p=a?D(50,100):r,l=u?D(40,70):o,m=c?D(0,360):s,g=H(f,p,l),d=H((f+n)%360,p,l),b=H((f+180)%360,p,l);return `linear-gradient(${m}deg, ${g}, ${d}, ${b})`},X=t=>{let e=t.replace("#","").match(/../g);for(let n=0;n<3;n++)e[n]=parseInt(e[n],16);return e},v=(t,e,n)=>{let r=[t.toString(16),e.toString(16),n.toString(16)];for(let o=0;o<3;o++)r[o].length==1&&(r[o]=`0${r[o]}`);return `#${r.join("")}`},Ke=(t,e)=>{let n=X(t);for(let r=0;r<3;r++)n[r]=Math.floor(n[r]*(1-e));return v(n[0],n[1],n[2])},Ve=(t,e)=>{let n=X(t);for(let r=0;r<3;r++)n[r]=Math.floor((255-n[r])*e+n[r]);return v(n[0],n[1],n[2])};function tt(t){let e=/^\\\\\?\\/.test(t),n=/[^\u0000-\u0080]+/.test(t);return e||n?t:t.replace(/\\/g,"/")}var P=52.35987755982988,y=3.141592653589793,R=6378245,C=.006693421622965943;function et(t,e){let n=+t,r=+e,o=-100+2*n+3*r+.2*r*r+.1*n*r+.2*Math.sqrt(Math.abs(n));return o+=(20*Math.sin(6*n*y)+20*Math.sin(2*n*y))*2/3,o+=(20*Math.sin(r*y)+40*Math.sin(r/3*y))*2/3,o+=(160*Math.sin(r/12*y)+320*Math.sin(r*y/30))*2/3,o}function nt(t,e){let n=+t,r=+e,o=300+t+2*r+.1*n*n+.1*n*r+.1*Math.sqrt(Math.abs(n));return o+=(20*Math.sin(6*n*y)+20*Math.sin(2*n*y))*2/3,o+=(20*Math.sin(n*y)+40*Math.sin(n/3*y))*2/3,o+=(150*Math.sin(n/12*y)+300*Math.sin(n/30*y))*2/3,o}function Ze(t,e){let n=+t,r=+e,o=n-.0065,s=r-.006,i=Math.sqrt(o*o+s*s)-2e-5*Math.sin(s*P),a=Math.atan2(s,o)-3e-6*Math.cos(o*P),u=i*Math.cos(a),c=i*Math.sin(a);return [u,c]}function Je(t,e){let n=+t,r=+e,o=Math.sqrt(n*n+r*r)+2e-5*Math.sin(r*P),s=Math.atan2(r,n)+3e-6*Math.cos(n*P),i=o*Math.cos(s)+.0065,a=o*Math.sin(s)+.006;return [i,a]}function Qe(t,e){let n=+t,r=+e;if(rt(n,r))return [n,r];{let o=et(n-105,r-35),s=nt(n-105,r-35),i=r/180*y,a=Math.sin(i);a=1-C*a*a;let u=Math.sqrt(a);o=o*180/(R*(1-C)/(a*u)*y),s=s*180/(R/u*Math.cos(i)*y);let c=r+o;return [n+s,c]}}function Xe(t,e){let n=+t,r=+e;if(rt(n,r))return [n,r];{let o=et(n-105,r-35),s=nt(n-105,r-35),i=r/180*y,a=Math.sin(i);a=1-C*a*a;let u=Math.sqrt(a);o=o*180/(R*(1-C)/(a*u)*y),s=s*180/(R/u*Math.cos(i)*y);let c=r+o,f=n+s;return [n*2-f,r*2-c]}}function rt(t,e){let n=+t,r=+e;return !(n>73.66&&n<135.05&&r>3.86&&r<53.55)}var en=t=>h(t)&&t.length>0?Math.max.apply(null,t):0,nn=t=>h(t)&&t.length>0?Math.min.apply(null,t):0,I=t=>h(t)&&t.length>0?t.reduce((e,n)=>e+n):0,rn=t=>h(t)&&t.length>0?I(t)/t.length:0,ot=t=>{if(!t&&typeof t>"u")return "";if(Number(t)===0)return "\u96F6";let e=["\u96F6","\u4E00","\u4E8C","\u4E09","\u56DB","\u4E94","\u516D","\u4E03","\u516B","\u4E5D","\u5341"],n=["","\u5341","\u767E","\u5343","\u4E07","\u4EBF","\u70B9",""],r=(""+t).replace(/(^0*)/g,"").split("."),o=0,s="";for(let i=r[0].length-1;i>=0;i--){switch(o){case 0:s=n[7]+s;break;case 4:new RegExp("0{4}//d{"+(r[0].length-i-1)+"}$").test(r[0])||(s=n[4]+s);break;case 8:s=n[5]+s,n[7]=n[5],o=0;break}o%4==2&&r[0].charAt(i+2)!=0&&r[0].charAt(i+1)==0&&(s=e[0]+s),r[0].charAt(i)!=0&&(s=e[r[0].charAt(i)]+n[o%4]+s),o++;}if(r.length>1){s+=n[6];for(let i=0;i<r[1].length;i++)s+=e[r[1].charAt(i)];}return s=="\u4E00\u5341"&&(s="\u5341"),s.match(/^一/)&&s.length==3&&(s=s.replace("\u4E00","")),s};function B(t){let e=t>Number.MAX_SAFE_INTEGER;return e&&console.warn("The calculation length has exceeded the JS maximum security integer"),e}function q(t,e){let n=t.toString().split(".").length>1?t.toString().split(".")[1].length:0,r=e.toString().split(".").length>1?e.toString().split(".")[1].length:0;return Math.pow(10,Math.max(n,r))}function on(t,e,n){let r=q(t,e),o=t*r+e*r;B(o);let s=o/r;return s=n||n?s.toFixed(n):s,Number(s)}function sn(t,e,n){let r=q(t,e),o=t*r-e*r;B(o);let s=o/r;return s=n||n?s.toFixed(n):s,Number(s)}function an(t,e,n){let r=t*e;B(r);let o=r;return o=n?o.toFixed(n):o,Number(o)}function un(t,e,n){let r=q(t,e),o=t*r/(e*r);return B(o),o=n||n?o.toFixed(n):o,Number(o)}var st=(t,e)=>{if(t==0)return "0 Bytes";let n=1024,r=e||2,o=["Bytes","KB","MB","GB","TB","PB","EB","ZB","YB"],s=Math.floor(Math.log(t)/Math.log(n));return parseFloat((t/Math.pow(n,s)).toFixed(r))+" "+o[s]};function T(t){let e=new Date,n={"M+":e.getMonth()+1,"D+":e.getDate(),"H+":e.getHours(),"m+":e.getMinutes(),"s+":e.getSeconds()};/(Y+)/.test(t)&&(t=t.replace(RegExp.$1,(e.getFullYear()+"").substr(4-RegExp.$1.length)));for(let r in n)new RegExp("("+r+")").test(t)&&(t=t.replace(RegExp.$1,RegExp.$1.length==1?n[r]:("00"+n[r]).substr((""+n[r]).length)));return t}function jt(t="\u661F\u671F"){let e=new Date().getDay();return `${t}${e===0?"\u65E5":ot(e)}`}function ln(t){t=new Date(t);let e=t.getFullYear(),n=t.getMonth()+1;return new Date(e,n,0).getDate()}function pn(t){let e=[];for(let n=0;n<=new Date().getFullYear()-t;n++)e.push(t+n);return e.reverse()}function mn(t){let e=t?.type??1,n=jt(t?.prefix??"\u661F\u671F"),r={ymd:T("YYYY\u5E74MM\u6708DD\u65E5"),hms:T("HH\u65F6mm\u5206ss\u79D2"),week:n},o={ymd:T("YYYY-MM-DD"),hms:T("HH-mm-ss"),week:n},s={ymd:T("YYYY/MM/DD"),hms:T("HH/mm/ss"),week:n};switch(e){case 1:return r;case 2:return o;case 3:return s;default:return r}}function gn(t,e=!0){let n=i=>(i=Math.floor(i),i<10&&e?`0${i}`:i),r=n(t/3600),o=n(t%3600/60),s=n(t%60);return {h:r,m:o,s}}var yn=(t=20)=>new Promise(e=>setTimeout(e,t)),hn=(t,e=200,n=!1)=>{let r,o=e,s=void 0;return function(){r&&clearTimeout(r),n?(r||t.call(s,...arguments),r=setTimeout(()=>r=null,o)):r=setTimeout(()=>t.call(s,...arguments),o);}},bn=(t,e=1e3)=>{let n;return function(){n||(n=setTimeout(()=>{t.call(void 0,...arguments),n=null;},e));}};function it(t){return t!==null&&typeof t=="object"&&!Array.isArray(t)}function at(t){return Array.isArray(t)}function ut(t){return t instanceof Date}function ct(t){return t instanceof RegExp}function ft(t){return t instanceof Map}function lt(t){return t instanceof Set}function Pt(t,e,n){if(t.size!==e.size)return !1;for(let[r,o]of t)if(!e.has(r)||!n(o,e.get(r)))return !1;return !0}function Rt(t,e){if(t.size!==e.size)return !1;for(let n of t)if(!e.has(n))return !1;return !0}function Ct(t,e,n){if(t.length!==e.length)return !1;for(let r=0;r<t.length;r++)if(!n(t[r],e[r]))return !1;return !0}function E(t,e,n=new WeakMap){if(t===e)return !0;if(ut(t)&&ut(e))return t.getTime()===e.getTime();if(ct(t)&&ct(e))return t.toString()===e.toString();if(ft(t)&&ft(e))return Pt(t,e,E);if(lt(t)&&lt(e))return Rt(t,e);if(at(t)&&at(e))return Ct(t,e,E);if(it(t)&&it(e)){if(n.has(t))return n.get(t)===e;n.set(t,e);let r=Object.keys(t),o=Object.keys(e);if(r.length!==o.length)return !1;for(let s of r)if(!Object.prototype.hasOwnProperty.call(e,s)||!E(t[s],e[s],n))return !1;return !0}return !1}var pt=(t,e="_blank")=>{if(!x)return;let n=document.createElement("a");n.setAttribute("href",t),n.setAttribute("target",e),n.setAttribute("rel","noreferrer noopener"),n.setAttribute("id","external");let r=document.getElementById("external");r&&document.body.removeChild(r),document.body.appendChild(n),n.click(),n.remove();};function En(t,e,n,r){Q(t).then(o=>{It(o,e,n,r);});}function It(t,e,n,r){let o=J(t);Bt(o,e,n,r);}function Bt(t,e,n,r){if(!x)return;let o=typeof r<"u"?[r,t]:[t],s=new Blob(o,{type:n||"application/octet-stream"}),i=window.URL.createObjectURL(s),a=document.createElement("a");a.style.display="none",a.href=i,a.setAttribute("download",e),typeof a.download>"u"&&a.setAttribute("target","_blank"),document.body.appendChild(a),a.click(),document.body.removeChild(a),window.URL.revokeObjectURL(i);}function kn(t,e,n="_self"){if(!x)return;let r=window.navigator.userAgent.toLowerCase().indexOf("chrome")>-1,o=window.navigator.userAgent.toLowerCase().indexOf("safari")>-1;if(/(iP)/g.test(window.navigator.userAgent))return console.error("Your browser does not support download!"),!1;if(r||o){let s=document.createElement("a");if(s.href=t,s.target=n,s.download!==void 0&&(s.download=e||t.substring(t.lastIndexOf("/")+1,t.length)),document.createEvent){let i=document.createEvent("MouseEvents");return i.initEvent("click",!0,!0),s.dispatchEvent(i),!0}}return t.indexOf("?")===-1&&(t+="?download"),pt(t,n),!0}function mt(t,e){if(t===e)return !0;if(typeof t!="object"||typeof e!="object"||t==null||e==null)return !1;let n=Object.keys(t),r=Object.keys(e);if(n.length!==r.length)return !1;for(let o of n)if(!r.includes(o)||!mt(t[o],e[o]))return !1;return !0}function Ft(t,e){if(!t||!e)return !1;let{length:n}=t;if(n!==e.length)return !1;for(let r=0;r<n;r++)if(!Lt(t[r],e[r]))return !1;return !0}function Lt(t,e){let n=Object.prototype.toString.call(t);return n!==Object.prototype.toString.call(e)?!1:n==="[object Object]"?mt(t,e):n==="[object Array]"?Ft(t,e):n==="[object Function]"?t===e?!0:t.toString()===e.toString():t===e}function jn(t){let e=new FormData;return Object.keys(t).forEach(n=>{e.append(n,t[n]);}),e}function Pn(t,e={}){let n=new FormData,r=e.fileKey||"file",o=e.filter||[],s=a=>o.includes(a),i=(a,u,c)=>{let f=c?`${c}[${a}]`:a;s(u)||(e.handleFile&&(u instanceof File||u instanceof Blob)?e.handleFile({file:u,key:f,formData:n}):u instanceof File||u instanceof Blob?n.append(r,u,u instanceof File?u.name:"blob"):Array.isArray(u)?u.forEach((p,l)=>i(String(l),p,f)):u&&typeof u=="object"&&u.constructor===Object?Object.keys(u).forEach(p=>i(p,u[p],f)):n.append(f,u));};return Object.keys(t).forEach(a=>i(a,t[a])),n}var Cn=t=>{let e=/-(\w)/g;return t.replace(e,(n,r)=>r?r.toUpperCase():"")},In=t=>{let e=/\B([A-Z])/g;return t.replace(e,"-$1").toLowerCase()};var Ln=(t,e)=>{let n={...t};return (h(e)?e:[e]).forEach(o=>{delete n[o];}),n};function gt(t){if(t){if(t instanceof Set)return t;if(Array.isArray(t))return new Set(t);if(typeof t=="object")return new Set(Object.keys(t).filter(e=>t[e]===!0||t[e]===1))}}function Ut(t,e,n){let{includeKeys:r,excludeKeys:o}=n;if(r!==void 0){let s=!1;if(typeof r=="function"?s=r(t,e):s=gt(r)?.has(t)??!1,!s)return !1}if(o!==void 0){let s=!1;if(typeof o=="function"?s=o(t,e):s=gt(o)?.has(t)??!1,s)return !1}return !0}function Nt(t,e){let{excludeWhitespaceStrings:n=!1,excludeEmptyObjects:r=!1,excludeEmptyArrays:o=!1,customFilter:s=()=>!1}=e;return !!(t==null||t===""||n&&typeof t=="string"&&t.trim()===""||r&&typeof t=="object"&&!Array.isArray(t)&&Object.keys(t).length===0||o&&Array.isArray(t)&&t.length===0||s(t))}function Un(t,e={}){let{maxDepth:n=1/0,allowRootIfEmpty:r=!1,customFilter:o,stripKeysInObjects:s={}}=e;function i(u,c,f){let p=s[u];if(!p)return !1;if(typeof p=="function")return p(c,f);for(let l of p)if(typeof l=="function"){if(l(c,f))return !0}else if(l===c)return !0;return !1}function a(u,c,f,p){if(c>n)return u;if(!(typeof o=="function"&&o(u,f,p))&&!Nt(u,e)){if(Array.isArray(u)){let l=u.map(m=>a(m,c+1,void 0,u)).filter(m=>m!==void 0);return e.excludeEmptyArrays&&l.length===0?void 0:l}if(typeof u=="object"&&u!==null){let l=u;if(typeof f=="string"&&s[f]){let b={};for(let w in l)i(f,w,l[w])||(b[w]=l[w]);l=b;}let m={},g=!1;for(let b of Reflect.ownKeys(l)){let w=l[b];if(!Ut(b,w,e))continue;let k=a(w,c+1,b,l);k!==void 0&&(m[b]=k,g=!0);}let d=!g&&Object.keys(m).length===0;return e.excludeEmptyObjects&&d||d&&!r?void 0:m}return u}}return a(t,1)}var M={};xt(M,{Fs:()=>Ht__namespace});F(M,Ht__namespace);var _=[],qt=t=>{let{folder:e="dist",callback:n,format:r=!0}=t;(0, M.readdir)(e,(o,s)=>{if(o)throw o;let i=0,a=()=>{++i==s.length&&n(r?st(I(_)):I(_));};s.forEach(u=>{(0, M.stat)(`${e}/${u}`,async(c,f)=>{if(c)throw c;f.isFile()?(_.push(f.size),a()):f.isDirectory()&&qt({folder:`${e}/${u}/`,callback:a});});}),s.length===0&&n(0);});};function Vn(t){return tt(path.resolve(process.cwd(),t))}function Yt(t,e){return S(e)?t.substring(0,t.indexOf(e)):""}function zt(t,e){return S(e)?t.substring(t.lastIndexOf(e)+e.length,t.length):""}function Zn(t,e){return S(e)?[Yt(t,e),zt(t,e)]:[]}function Jn(t,e,n){if(!S(e)||!S(n))return "";let r=t.substring(t.indexOf(e)+e.length,t.length);return r.substring(0,r.indexOf(n))}function Qn(t,e=3){return t=t.toString(),t.length>e?t.substr(0,e)+"...":t}function Xn(t){return t?[...t+""].map(Number):""}function vn(t,e,n="*"){L(t)&&(t=t.toString()),h(e)||(e=Array.of(e));let r=t.split("");for(let o=0;o<e.length;o++){let s=e[o];if($(s)&&!h(s)){let{start:i,end:a}=s;i>=0&&i<a&&r.fill(n,i,a+1);continue}L(s)&&Number.isInteger(s)&&s>=0&&(r[e[o]]=n);}return r.join("")}var Kt=t=>{if(!Array.isArray(t))return console.warn("tree must be an array"),[];if(!t||t.length===0)return [];let e=[];for(let n of t)n.children&&n.children.length>0&&Kt(n.children),e.push(n.uniqueId);return e},Vt=(t,e=[])=>{if(!Array.isArray(t))return console.warn("menuTree must be an array"),[];if(!t||t.length===0)return [];for(let[n,r]of t.entries())r.children&&r.children.length===1&&delete r.children,r.id=n,r.parentId=e.length?e[e.length-1]:null,r.pathList=[...e,r.id],r.uniqueId=r.pathList.length>1?r.pathList.join("-"):r.pathList[0],r.children&&r.children.length>0&&Vt(r.children,r.pathList);return t},Wt=(t,e=[])=>{if(!Array.isArray(t))return console.warn("tree must be an array"),[];if(!t||t.length===0)return [];for(let[n,r]of t.entries())r.id=n,r.parentId=e.length?e[e.length-1]:null,r.pathList=[...e,r.id],r.children&&r.children.length>0&&Wt(r.children,r.pathList);return t},Gt=(t,e)=>{if(!Array.isArray(t))return console.warn("menuTree must be an array"),[];if(!t||t.length===0)return [];let n=t.find(o=>o.uniqueId===e);if(n)return n;let r=t.filter(o=>o.children).map(o=>o.children).flat(1);return Gt(r,e)},Zt=(t,e,n)=>{if(!Array.isArray(t))return console.warn("menuTree must be an array"),[];if(!t||t.length===0)return [];for(let r of t){let o=r.children&&r.children.length>0;r.uniqueId===e&&Object.prototype.toString.call(n)==="[object Object]"&&Object.assign(r,n),o&&Zt(r.children,e,n);}return t},er=(t,e,n,r)=>{if(!Array.isArray(t))return console.warn("data must be an array"),[];let o={id:e||"id",parentId:n||"parentId",childrenList:r||"children"},s={},i={},a=[];for(let c of t){let f=c[o.parentId];s[f]==null&&(s[f]=[]),i[c[o.id]]=c,s[f].push(c);}for(let c of t){let f=c[o.parentId];i[f]==null&&a.push(c);}for(let c of a)u(c);function u(c){if(s[c[o.id]]!==null&&(c[o.childrenList]=s[c[o.id]]),c[o.childrenList])for(let f of c[o.childrenList])u(f);}return a};var rr=()=>{let t="",e=[];for(let n=0;n<=15;n++)e[n]=n.toString(16);for(let n=1;n<=36;n++)n===9||n===14||n===19||n===24?t+="-":n===15?t+=4:n===20?t+=e[Math.random()*4|8]:t+=e[Math.random()*16|0];return t.replace(/-/g,"")},or=()=>{let t="",e=[];for(let n=0;n<=15;n++)e[n]=n.toString(16);for(let n=1;n<=36;n++)n===9||n===14||n===19||n===24?t+="-":n===15?t+="4":n===20?t+=e[Math.random()*4|8]:t+=e[Math.floor(Math.random()*16)];return t},sr=(t="")=>{let e=0,n=Date.now(),r=Math.floor(Math.random()*1e9);return e++,`${t}${r}${e}${String(n)}`},ir=(t,e,n="")=>{let r="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz".split(""),o=[],s;if(e=e||r.length,t)for(s=0;s<t;s++)o[s]=r[0|Math.random()*e];else {let i;for(o[8]=o[13]=o[18]=o[23]="-",o[14]="4",s=0;s<36;s++)o[s]||(i=0|Math.random()*16,o[s]=r[s==19?i&3|8:i]);}return n?n+o.join(""):o.join("")};function dt(t){for(let e=t.length-1;e>0;e--){let n=Math.floor(Math.random()*(e+1));[t[e],t[n]]=[t[n],t[e]];}return t}function pr(t,e){return t.every(n=>e.some(r=>r===n))}var mr=(...t)=>[...t].reduce((e,n)=>e.filter(r=>n.includes(r)));function gr(t,e,n){return t[e]=t.splice(n,1,t[e])[0],t}function dr(t,e,n=!0){let r=[];for(let o of t)o[e]!==void 0&&o[e]!==null&&r.push(o[e]);return n?Array.from(new Set(r)):r}function yr(t,e,n={}){let r=n.minPerPart??0,o=n.maxPerPart,s=n.order??"random";if(n.minPerPart&&t<e*r||o&&t>e*o)return console.error("\u603B\u6570\u4E0D\u8DB3\u4EE5\u6309\u6307\u5B9A\u7684\u6700\u5C0F\u9600\u503C\u5206\u6210\u76F8\u5E94\u7684\u4EFD\u6570\uFF0C\u6216\u8005\u603B\u6570\u8D85\u8FC7\u4E86\u6309\u6700\u5927\u9600\u503C\u5206\u914D\u7684\u80FD\u529B"),[];let i=t-r*e,a=Array.from({length:e},()=>Math.random()),u=a.reduce((l,m)=>l+m,0),c=a.map(l=>{let m=Math.floor(l/u*i),g=r+m;return o!==void 0&&(g=Math.min(g,o)),g}),f=c.reduce((l,m)=>l+m,0),p=0;for(;f!==t;)p>=c.length&&(p=0),f<t&&(o===void 0||c[p]<o)?(c[p]++,f++):f>t&&c[p]>r&&(c[p]--,f--),p++;switch(s){case"asc":c.sort((l,m)=>l-m);break;case"desc":c.sort((l,m)=>m-l);break;case"random":dt(c);break}return c}var hr=(t,e)=>{if(!h(t)||!h(e))return !1;let n=new Set(t);return e.every(r=>n.has(r))},br=(t,e)=>e.every(n=>t.some(r=>E(r,n))),xr=(t,e)=>{if(!h(t)||!h(e))return !1;let n=new Set(t);return e.some(r=>n.has(r))};function wr(t,e){return e.some(n=>t.some(r=>E(r,n)))}function Ar(t,...e){let n=new Array(t.length);for(let r=0;r<t.length;r++){let o={};for(let s of e)o[s]=t[r][s];n[r]=o;}return n}

exports.addZero = Tt;
exports.addition = on;
exports.appendFieldByUniqueId = Zt;
exports.arrayAllExist = hr;
exports.arrayAllExistDeep = br;
exports.arrayAnyExist = xr;
exports.arrayAnyExistDeep = wr;
exports.average = rn;
exports.bd09togcj02 = Ze;
exports.buildGUID = or;
exports.buildHierarchyTree = Wt;
exports.buildPrefixUUID = sr;
exports.buildUUID = rr;
exports.centsToDollars = je;
exports.cleanObject = Un;
exports.clone = Ne;
exports.cloneDeep = He;
exports.convertImageToGray = Le;
exports.convertPath = tt;
exports.createFormData = Pn;
exports.createYear = pn;
exports.darken = Ke;
exports.dataURLtoBlob = J;
exports.dateFormat = T;
exports.debounce = hn;
exports.deepEqual = E;
exports.delObjectProperty = Ln;
exports.delay = yn;
exports.deleteChildren = Vt;
exports.divisionOperation = un;
exports.dollarsToCents = Pe;
exports.downloadByBase64 = It;
exports.downloadByData = Bt;
exports.downloadByOnlineUrl = En;
exports.downloadByUrl = kn;
exports.exceedMathMax = B;
exports.extractFields = Ar;
exports.extractPathList = Kt;
exports.formDataHander = jn;
exports.formatBytes = st;
exports.gcj02tobd09 = Je;
exports.gcj02towgs84 = Xe;
exports.getAbsolutePath = Vn;
exports.getCurrentDate = mn;
exports.getCurrentWeek = jt;
exports.getDecimalPlaces = Re;
exports.getKeyList = dr;
exports.getNodeByUniqueId = Gt;
exports.getPackageSize = qt;
exports.getTime = gn;
exports.handleTree = er;
exports.hasCNChars = Me;
exports.hasOwnProp = kt;
exports.hexToRgb = X;
exports.hideTextAtIndex = vn;
exports.intersection = mr;
exports.is = O;
exports.isAllEmpty = Z;
exports.isAlphabets = Te;
exports.isArray = h;
exports.isBase64 = pe;
exports.isBoolean = ae;
exports.isBrowser = x;
exports.isClient = ye;
exports.isDate = oe;
exports.isDef = At;
exports.isElement = le;
exports.isEmail = xe;
exports.isEmpty = St;
exports.isEqual = Lt;
exports.isEqualArray = Ft;
exports.isEqualObject = mt;
exports.isExistSpace = Ee;
exports.isFunction = V;
exports.isHex = me;
exports.isHtml = ke;
exports.isIncludeAllChildren = pr;
exports.isInvalidValue = Nt;
exports.isJSON = ce;
exports.isLeapYear = se;
exports.isLowerCase = Se;
exports.isNull = G;
exports.isNullAndUnDef = re;
exports.isNullOrUnDef = Mt;
exports.isNumber = L;
exports.isObject = $;
exports.isPhone = be;
exports.isPlainObject = ne;
exports.isPostCode = Ae;
exports.isPromise = ie;
exports.isQQ = we;
exports.isRegExp = ue;
exports.isRgb = ge;
exports.isRgba = de;
exports.isServer = Ot;
exports.isString = S;
exports.isUnDef = W;
exports.isUpperCase = Oe;
exports.isUrl = he;
exports.isWindow = fe;
exports.lighten = Ve;
exports.mapsEqual = Pt;
exports.max = en;
exports.min = nn;
exports.monthDays = ln;
exports.multiplication = an;
exports.nameCamelize = Cn;
exports.nameHyphenate = In;
exports.numberToChinese = ot;
exports.openLink = pt;
exports.out_of_china = rt;
exports.priceToThousands = Ie;
exports.priceUppercase = Ce;
exports.randomColor = Ye;
exports.randomDivide = yr;
exports.randomGradient = ze;
exports.removeAllSpace = K;
exports.removeBothSidesSpace = vt;
exports.removeLeftSpace = Qt;
exports.removeRightSpace = Xt;
exports.rgbToHex = v;
exports.setsEqual = Rt;
exports.shouldCleanKey = Ut;
exports.shuffleArray = dt;
exports.splitNum = Xn;
exports.subAfter = zt;
exports.subBefore = Yt;
exports.subBetween = Jn;
exports.subBothSides = Zn;
exports.subTextAddEllipsis = Qn;
exports.subtraction = sn;
exports.sum = I;
exports.swapOrder = gr;
exports.throttle = bn;
exports.toSet = gt;
exports.urlToBase64 = Q;
exports.uuid = ir;
exports.wgs84togcj02 = Qe;

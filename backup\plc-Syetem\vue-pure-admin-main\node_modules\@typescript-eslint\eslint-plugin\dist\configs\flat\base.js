"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
/**
 * A minimal ruleset that sets only the required parser and plugin options needed to run typescript-eslint.
 * We don't recommend using this directly; instead, extend from an earlier recommended rule.
 * @see {@link https://typescript-eslint.io/users/configs#base}
 */
exports.default = (plugin, parser) => ({
    name: 'typescript-eslint/base',
    languageOptions: {
        parser,
        sourceType: 'module',
    },
    plugins: {
        '@typescript-eslint': plugin,
    },
});

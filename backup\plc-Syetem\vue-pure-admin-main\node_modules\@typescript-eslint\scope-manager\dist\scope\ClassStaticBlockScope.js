"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ClassStaticBlockScope = void 0;
const ScopeBase_1 = require("./ScopeBase");
const ScopeType_1 = require("./ScopeType");
class ClassStaticBlockScope extends ScopeBase_1.ScopeBase {
    constructor(scopeManager, upperScope, block) {
        super(scopeManager, ScopeType_1.ScopeType.classStaticBlock, upperScope, block, false);
    }
}
exports.ClassStaticBlockScope = ClassStaticBlockScope;

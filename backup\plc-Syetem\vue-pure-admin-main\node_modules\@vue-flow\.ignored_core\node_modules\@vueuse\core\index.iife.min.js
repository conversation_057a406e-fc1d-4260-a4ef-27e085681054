var VueDemi=function(h,d,a){if(h.install)return h;if(!d)return console.error("[vue-demi] no Vue instance found, please be sure to import `vue` before `vue-demi`."),h;if(d.version.slice(0,4)==="2.7."){let q=function(X,J){var K,L={},A={config:d.config,use:d.use.bind(d),mixin:d.mixin.bind(d),component:d.component.bind(d),provide:function(H,U){return L[H]=U,this},directive:function(H,U){return U?(d.directive(H,U),A):d.directive(H)},mount:function(H,U){return K||(K=new d(Object.assign({propsData:J},X,{provide:Object.assign(L,X.provide)})),K.$mount(H,U),K)},unmount:function(){K&&(K.$destroy(),K=void 0)}};return A};var Ve=q;for(var Y in d)h[Y]=d[Y];h.isVue2=!0,h.isVue3=!1,h.install=function(){},h.Vue=d,h.Vue2=d,h.version=d.version,h.warn=d.util.warn,h.hasInjectionContext=function(){return!!h.getCurrentInstance()},h.createApp=q}else if(d.version.slice(0,2)==="2.")if(a){for(var Y in a)h[Y]=a[Y];h.isVue2=!0,h.isVue3=!1,h.install=function(){},h.Vue=d,h.Vue2=d,h.version=d.version,h.hasInjectionContext=function(){return!!h.getCurrentInstance()}}else console.error("[vue-demi] no VueCompositionAPI instance found, please be sure to import `@vue/composition-api` before `vue-demi`.");else if(d.version.slice(0,2)==="3."){for(var Y in d)h[Y]=d[Y];h.isVue2=!1,h.isVue3=!0,h.install=function(){},h.Vue=d,h.Vue2=void 0,h.version=d.version,h.set=function(q,X,J){return Array.isArray(q)?(q.length=Math.max(q.length,X),q.splice(X,1,J),J):(q[X]=J,J)},h.del=function(q,X){if(Array.isArray(q)){q.splice(X,1);return}delete q[X]}}else console.error("[vue-demi] Vue version "+d.version+" is unsupported.");return h}((globalThis||self).VueDemi=(globalThis||self).VueDemi||(typeof VueDemi<"u"?VueDemi:{}),(globalThis||self).Vue||(typeof Vue<"u"?Vue:void 0),(globalThis||self).VueCompositionAPI||(typeof VueCompositionAPI<"u"?VueCompositionAPI:void 0));(function(h,d,a){"use strict";function Y(e,t,n){let r;a.isRef(n)?r={evaluating:n}:r=n||{};const{lazy:l=!1,evaluating:o=void 0,shallow:u=!0,onError:i=d.noop}=r,s=a.ref(!l),c=u?a.shallowRef(t):a.ref(t);let f=0;return a.watchEffect(async v=>{if(!s.value)return;f++;const y=f;let p=!1;o&&Promise.resolve().then(()=>{o.value=!0});try{const m=await e(S=>{v(()=>{o&&(o.value=!1),p||S()})});y===f&&(c.value=m)}catch(m){i(m)}finally{o&&y===f&&(o.value=!1),p=!0}}),l?a.computed(()=>(s.value=!0,c.value)):c}function Ve(e,t,n,r){let l=a.inject(e);return n&&(l=a.inject(e,n)),r&&(l=a.inject(e,n,r)),typeof t=="function"?a.computed(o=>t(l,o)):a.computed({get:o=>t.get(l,o),set:t.set})}function q(e={}){if(!a.isVue3&&!a.version.startsWith("2.7.")){if(process.env.NODE_ENV!=="production")throw new Error("[VueUse] createReusableTemplate only works in Vue 2.7 or above.");return}const{inheritAttrs:t=!0}=e,n=a.shallowRef(),r=a.defineComponent({setup(o,{slots:u}){return()=>{n.value=u.default}}}),l=a.defineComponent({inheritAttrs:t,setup(o,{attrs:u,slots:i}){return()=>{var s;if(!n.value&&process.env.NODE_ENV!=="production")throw new Error("[VueUse] Failed to find the definition of reusable template");const c=(s=n.value)==null?void 0:s.call(n,{...X(u),$slots:i});return t&&c?.length===1?c[0]:c}}});return d.makeDestructurable({define:r,reuse:l},[r,l])}function X(e){const t={};for(const n in e)t[d.camelize(n)]=e[n];return t}function J(e={}){if(!a.isVue3){if(process.env.NODE_ENV!=="production")throw new Error("[VueUse] createTemplatePromise only works in Vue 3 or above.");return}let t=0;const n=a.ref([]);function r(...u){const i=a.shallowReactive({key:t++,args:u,promise:void 0,resolve:()=>{},reject:()=>{},isResolving:!1,options:e});return n.value.push(i),i.promise=new Promise((s,c)=>{i.resolve=f=>(i.isResolving=!0,s(f)),i.reject=c}).finally(()=>{i.promise=void 0;const s=n.value.indexOf(i);s!==-1&&n.value.splice(s,1)}),i.promise}function l(...u){return e.singleton&&n.value.length>0?n.value[0].promise:r(...u)}const o=a.defineComponent((u,{slots:i})=>{const s=()=>n.value.map(c=>{var f;return a.h(a.Fragment,{key:c.key},(f=i.default)==null?void 0:f.call(i,c))});return e.transition?()=>a.h(a.TransitionGroup,e.transition,s):s});return o.start=l,o}function K(e){return function(...t){return e.apply(this,t.map(n=>d.toValue(n)))}}function L(e){var t;const n=d.toValue(e);return(t=n?.$el)!=null?t:n}const A=d.isClient?window:void 0,H=d.isClient?window.document:void 0,U=d.isClient?window.navigator:void 0,kt=d.isClient?window.location:void 0;function _(...e){let t,n,r,l;if(typeof e[0]=="string"||Array.isArray(e[0])?([n,r,l]=e,t=A):[t,n,r,l]=e,!t)return d.noop;Array.isArray(n)||(n=[n]),Array.isArray(r)||(r=[r]);const o=[],u=()=>{o.forEach(f=>f()),o.length=0},i=(f,v,y,p)=>(f.addEventListener(v,y,p),()=>f.removeEventListener(v,y,p)),s=a.watch(()=>[L(t),d.toValue(l)],([f,v])=>{if(u(),!f)return;const y=d.isObject(v)?{...v}:v;o.push(...n.flatMap(p=>r.map(m=>i(f,p,m,y))))},{immediate:!0,flush:"post"}),c=()=>{s(),u()};return d.tryOnScopeDispose(c),c}let Ae=!1;function _t(e,t,n={}){const{window:r=A,ignore:l=[],capture:o=!0,detectIframe:u=!1}=n;if(!r)return d.noop;d.isIOS&&!Ae&&(Ae=!0,Array.from(r.document.body.children).forEach(y=>y.addEventListener("click",d.noop)),r.document.documentElement.addEventListener("click",d.noop));let i=!0;const s=y=>l.some(p=>{if(typeof p=="string")return Array.from(r.document.querySelectorAll(p)).some(m=>m===y.target||y.composedPath().includes(m));{const m=L(p);return m&&(y.target===m||y.composedPath().includes(m))}}),f=[_(r,"click",y=>{const p=L(e);if(!(!p||p===y.target||y.composedPath().includes(p))){if(y.detail===0&&(i=!s(y)),!i){i=!0;return}t(y)}},{passive:!0,capture:o}),_(r,"pointerdown",y=>{const p=L(e);i=!s(y)&&!!(p&&!y.composedPath().includes(p))},{passive:!0}),u&&_(r,"blur",y=>{setTimeout(()=>{var p;const m=L(e);((p=r.document.activeElement)==null?void 0:p.tagName)==="IFRAME"&&!m?.contains(r.document.activeElement)&&t(y)},0)})].filter(Boolean);return()=>f.forEach(y=>y())}function Rt(e){return typeof e=="function"?e:typeof e=="string"?t=>t.key===e:Array.isArray(e)?t=>e.includes(t.key):()=>!0}function ce(...e){let t,n,r={};e.length===3?(t=e[0],n=e[1],r=e[2]):e.length===2?typeof e[1]=="object"?(t=!0,n=e[0],r=e[1]):(t=e[0],n=e[1]):(t=!0,n=e[0]);const{target:l=A,eventName:o="keydown",passive:u=!1,dedupe:i=!1}=r,s=Rt(t);return _(l,o,f=>{f.repeat&&d.toValue(i)||s(f)&&n(f)},u)}function Ft(e,t,n={}){return ce(e,t,{...n,eventName:"keydown"})}function Pt(e,t,n={}){return ce(e,t,{...n,eventName:"keypress"})}function Ct(e,t,n={}){return ce(e,t,{...n,eventName:"keyup"})}const Vt=500,At=10;function It(e,t,n){var r,l;const o=a.computed(()=>L(e));let u,i,s,c=!1;function f(){u&&(clearTimeout(u),u=void 0),i=void 0,s=void 0,c=!1}function v(g){var b,E,R;const[k,V,T]=[s,i,c];if(f(),!n?.onMouseUp||!V||!k||(b=n?.modifiers)!=null&&b.self&&g.target!==o.value)return;(E=n?.modifiers)!=null&&E.prevent&&g.preventDefault(),(R=n?.modifiers)!=null&&R.stop&&g.stopPropagation();const C=g.x-V.x,F=g.y-V.y,O=Math.sqrt(C*C+F*F);n.onMouseUp(g.timeStamp-k,O,T)}function y(g){var b,E,R,k;(b=n?.modifiers)!=null&&b.self&&g.target!==o.value||(f(),(E=n?.modifiers)!=null&&E.prevent&&g.preventDefault(),(R=n?.modifiers)!=null&&R.stop&&g.stopPropagation(),i={x:g.x,y:g.y},s=g.timeStamp,u=setTimeout(()=>{c=!0,t(g)},(k=n?.delay)!=null?k:Vt))}function p(g){var b,E,R,k;if((b=n?.modifiers)!=null&&b.self&&g.target!==o.value||!i||n?.distanceThreshold===!1)return;(E=n?.modifiers)!=null&&E.prevent&&g.preventDefault(),(R=n?.modifiers)!=null&&R.stop&&g.stopPropagation();const V=g.x-i.x,T=g.y-i.y;Math.sqrt(V*V+T*T)>=((k=n?.distanceThreshold)!=null?k:At)&&f()}const m={capture:(r=n?.modifiers)==null?void 0:r.capture,once:(l=n?.modifiers)==null?void 0:l.once},S=[_(o,"pointerdown",y,m),_(o,"pointermove",p,m),_(o,["pointerup","pointerleave"],v,m)];return()=>S.forEach(g=>g())}function Mt(){const{activeElement:e,body:t}=document;if(!e||e===t)return!1;switch(e.tagName){case"INPUT":case"TEXTAREA":return!0}return e.hasAttribute("contenteditable")}function Lt({keyCode:e,metaKey:t,ctrlKey:n,altKey:r}){return t||n||r?!1:e>=48&&e<=57||e>=65&&e<=90||e>=97&&e<=122}function Nt(e,t={}){const{document:n=H}=t;n&&_(n,"keydown",l=>{!Mt()&&Lt(l)&&e(l)},{passive:!0})}function xt(e,t=null){const n=a.getCurrentInstance();let r=()=>{};const l=a.customRef((o,u)=>(r=u,{get(){var i,s;return o(),(s=(i=n?.proxy)==null?void 0:i.$refs[e])!=null?s:t},set(){}}));return d.tryOnMounted(r),a.onUpdated(r),l}function Ie(){const e=a.ref(!1),t=a.getCurrentInstance();return t&&a.onMounted(()=>{e.value=!0},a.isVue2?void 0:t),e}function x(e){const t=Ie();return a.computed(()=>(t.value,!!e()))}function te(e,t,n={}){const{window:r=A,...l}=n;let o;const u=x(()=>r&&"MutationObserver"in r),i=()=>{o&&(o.disconnect(),o=void 0)},s=a.computed(()=>{const y=d.toValue(e),p=(Array.isArray(y)?y:[y]).map(L).filter(d.notNullish);return new Set(p)}),c=a.watch(()=>s.value,y=>{i(),u.value&&y.size&&(o=new MutationObserver(t),y.forEach(p=>o.observe(p,l)))},{immediate:!0,flush:"post"}),f=()=>o?.takeRecords(),v=()=>{i(),c()};return d.tryOnScopeDispose(v),{isSupported:u,stop:v,takeRecords:f}}function Me(e={}){var t;const{window:n=A,deep:r=!0,triggerOnRemoval:l=!1}=e,o=(t=e.document)!=null?t:n?.document,u=()=>{var c;let f=o?.activeElement;if(r)for(;f?.shadowRoot;)f=(c=f?.shadowRoot)==null?void 0:c.activeElement;return f},i=a.ref(),s=()=>{i.value=u()};return n&&(_(n,"blur",c=>{c.relatedTarget===null&&s()},!0),_(n,"focus",s,!0)),l&&te(o,c=>{c.filter(f=>f.removedNodes.length).map(f=>Array.from(f.removedNodes)).flat().forEach(f=>{f===i.value&&s()})},{childList:!0,subtree:!0}),s(),i}function Z(e,t={}){const{immediate:n=!0,fpsLimit:r=void 0,window:l=A}=t,o=a.ref(!1),u=r?1e3/r:null;let i=0,s=null;function c(y){if(!o.value||!l)return;i||(i=y);const p=y-i;if(u&&p<u){s=l.requestAnimationFrame(c);return}i=y,e({delta:p,timestamp:y}),s=l.requestAnimationFrame(c)}function f(){!o.value&&l&&(o.value=!0,i=0,s=l.requestAnimationFrame(c))}function v(){o.value=!1,s!=null&&l&&(l.cancelAnimationFrame(s),s=null)}return n&&f(),d.tryOnScopeDispose(v),{isActive:a.readonly(o),pause:v,resume:f}}function Wt(e,t,n){let r,l;d.isObject(n)?(r=n,l=d.objectOmit(n,["window","immediate","commitStyles","persist","onReady","onError"])):(r={duration:n},l=n);const{window:o=A,immediate:u=!0,commitStyles:i,persist:s,playbackRate:c=1,onReady:f,onError:v=M=>{console.error(M)}}=r,y=x(()=>o&&HTMLElement&&"animate"in HTMLElement.prototype),p=a.shallowRef(void 0),m=a.shallowReactive({startTime:null,currentTime:null,timeline:null,playbackRate:c,pending:!1,playState:u?"idle":"paused",replaceState:"active"}),S=a.computed(()=>m.pending),w=a.computed(()=>m.playState),g=a.computed(()=>m.replaceState),b=a.computed({get(){return m.startTime},set(M){m.startTime=M,p.value&&(p.value.startTime=M)}}),E=a.computed({get(){return m.currentTime},set(M){m.currentTime=M,p.value&&(p.value.currentTime=M,B())}}),R=a.computed({get(){return m.timeline},set(M){m.timeline=M,p.value&&(p.value.timeline=M)}}),k=a.computed({get(){return m.playbackRate},set(M){m.playbackRate=M,p.value&&(p.value.playbackRate=M)}}),V=()=>{if(p.value)try{p.value.play(),B()}catch(M){W(),v(M)}else P()},T=()=>{var M;try{(M=p.value)==null||M.pause(),W()}catch($){v($)}},C=()=>{var M;!p.value&&P();try{(M=p.value)==null||M.reverse(),B()}catch($){W(),v($)}},F=()=>{var M;try{(M=p.value)==null||M.finish(),W()}catch($){v($)}},O=()=>{var M;try{(M=p.value)==null||M.cancel(),W()}catch($){v($)}};a.watch(()=>L(e),M=>{M&&P()}),a.watch(()=>t,M=>{!p.value&&P(),!L(e)&&p.value&&(p.value.effect=new KeyframeEffect(L(e),d.toValue(M),l))},{deep:!0}),d.tryOnMounted(()=>{a.nextTick(()=>P(!0))}),d.tryOnScopeDispose(O);function P(M){const $=L(e);!y.value||!$||(p.value||(p.value=$.animate(d.toValue(t),l)),s&&p.value.persist(),c!==1&&(p.value.playbackRate=c),M&&!u?p.value.pause():B(),f?.(p.value))}_(p,["cancel","finish","remove"],W),_(p,"finish",()=>{var M;i&&((M=p.value)==null||M.commitStyles())});const{resume:I,pause:N}=Z(()=>{p.value&&(m.pending=p.value.pending,m.playState=p.value.playState,m.replaceState=p.value.replaceState,m.startTime=p.value.startTime,m.currentTime=p.value.currentTime,m.timeline=p.value.timeline,m.playbackRate=p.value.playbackRate)},{immediate:!1});function B(){y.value&&I()}function W(){y.value&&o&&o.requestAnimationFrame(N)}return{isSupported:y,animate:p,play:V,pause:T,reverse:C,finish:F,cancel:O,pending:S,playState:w,replaceState:g,startTime:b,currentTime:E,timeline:R,playbackRate:k}}function $t(e,t){const{interrupt:n=!0,onError:r=d.noop,onFinished:l=d.noop,signal:o}=t||{},u={aborted:"aborted",fulfilled:"fulfilled",pending:"pending",rejected:"rejected"},i=Array.from(Array.from({length:e.length}),()=>({state:u.pending,data:null})),s=a.reactive(i),c=a.ref(-1);if(!e||e.length===0)return l(),{activeIndex:c,result:s};function f(v,y){c.value++,s[c.value].data=y,s[c.value].state=v}return e.reduce((v,y)=>v.then(p=>{var m;if(o?.aborted){f(u.aborted,new Error("aborted"));return}if(((m=s[c.value])==null?void 0:m.state)===u.rejected&&n){l();return}const S=y(p).then(w=>(f(u.fulfilled,w),c.value===e.length-1&&l(),w));return o?Promise.race([S,Ht(o)]):S}).catch(p=>o?.aborted?(f(u.aborted,p),p):(f(u.rejected,p),r(),p)),Promise.resolve()),{activeIndex:c,result:s}}function Ht(e){return new Promise((t,n)=>{const r=new Error("aborted");e.aborted?n(r):e.addEventListener("abort",()=>n(r),{once:!0})})}function Le(e,t,n){const{immediate:r=!0,delay:l=0,onError:o=d.noop,onSuccess:u=d.noop,resetOnExecute:i=!0,shallow:s=!0,throwError:c}=n??{},f=s?a.shallowRef(t):a.ref(t),v=a.ref(!1),y=a.ref(!1),p=a.shallowRef(void 0);async function m(g=0,...b){i&&(f.value=t),p.value=void 0,v.value=!1,y.value=!0,g>0&&await d.promiseTimeout(g);const E=typeof e=="function"?e(...b):e;try{const R=await E;f.value=R,v.value=!0,u(R)}catch(R){if(p.value=R,o(R),c)throw R}finally{y.value=!1}return f.value}r&&m(l);const S={state:f,isReady:v,isLoading:y,error:p,execute:m};function w(){return new Promise((g,b)=>{d.until(y).toBe(!1).then(()=>g(S)).catch(b)})}return{...S,then(g,b){return w().then(g,b)}}}const oe={array:e=>JSON.stringify(e),object:e=>JSON.stringify(e),set:e=>JSON.stringify(Array.from(e)),map:e=>JSON.stringify(Object.fromEntries(e)),null:()=>""};function Ut(e){return e?e instanceof Map?oe.map:e instanceof Set?oe.set:Array.isArray(e)?oe.array:oe.object:oe.null}function Bt(e,t){const n=a.ref(""),r=a.ref();function l(){if(d.isClient)return r.value=new Promise((o,u)=>{try{const i=d.toValue(e);if(i==null)o("");else if(typeof i=="string")o(Se(new Blob([i],{type:"text/plain"})));else if(i instanceof Blob)o(Se(i));else if(i instanceof ArrayBuffer)o(window.btoa(String.fromCharCode(...new Uint8Array(i))));else if(i instanceof HTMLCanvasElement)o(i.toDataURL(t?.type,t?.quality));else if(i instanceof HTMLImageElement){const s=i.cloneNode(!1);s.crossOrigin="Anonymous",jt(s).then(()=>{const c=document.createElement("canvas"),f=c.getContext("2d");c.width=s.width,c.height=s.height,f.drawImage(s,0,0,c.width,c.height),o(c.toDataURL(t?.type,t?.quality))}).catch(u)}else if(typeof i=="object"){const c=(t?.serializer||Ut(i))(i);return o(Se(new Blob([c],{type:"application/json"})))}else u(new Error("target is unsupported types"))}catch(i){u(i)}}),r.value.then(o=>n.value=o),r.value}return a.isRef(e)||typeof e=="function"?a.watch(e,l,{immediate:!0}):l(),{base64:n,promise:r,execute:l}}function jt(e){return new Promise((t,n)=>{e.complete?t():(e.onload=()=>{t()},e.onerror=n)})}function Se(e){return new Promise((t,n)=>{const r=new FileReader;r.onload=l=>{t(l.target.result)},r.onerror=n,r.readAsDataURL(e)})}function zt(e={}){const{navigator:t=U}=e,n=["chargingchange","chargingtimechange","dischargingtimechange","levelchange"],r=x(()=>t&&"getBattery"in t&&typeof t.getBattery=="function"),l=a.ref(!1),o=a.ref(0),u=a.ref(0),i=a.ref(1);let s;function c(){l.value=this.charging,o.value=this.chargingTime||0,u.value=this.dischargingTime||0,i.value=this.level}return r.value&&t.getBattery().then(f=>{s=f,c.call(s),_(s,n,c,{passive:!0})}),{isSupported:r,charging:l,chargingTime:o,dischargingTime:u,level:i}}function qt(e){let{acceptAllDevices:t=!1}=e||{};const{filters:n=void 0,optionalServices:r=void 0,navigator:l=U}=e||{},o=x(()=>l&&"bluetooth"in l),u=a.shallowRef(void 0),i=a.shallowRef(null);a.watch(u,()=>{v()});async function s(){if(o.value){i.value=null,n&&n.length>0&&(t=!1);try{u.value=await l?.bluetooth.requestDevice({acceptAllDevices:t,filters:n,optionalServices:r})}catch(y){i.value=y}}}const c=a.ref(),f=a.computed(()=>{var y;return((y=c.value)==null?void 0:y.connected)||!1});async function v(){if(i.value=null,u.value&&u.value.gatt){u.value.addEventListener("gattserverdisconnected",()=>{});try{c.value=await u.value.gatt.connect()}catch(y){i.value=y}}}return d.tryOnMounted(()=>{var y;u.value&&((y=u.value.gatt)==null||y.connect())}),d.tryOnScopeDispose(()=>{var y;u.value&&((y=u.value.gatt)==null||y.disconnect())}),{isSupported:o,isConnected:f,device:u,requestDevice:s,server:c,error:i}}function G(e,t={}){const{window:n=A}=t,r=x(()=>n&&"matchMedia"in n&&typeof n.matchMedia=="function");let l;const o=a.ref(!1),u=c=>{o.value=c.matches},i=()=>{l&&("removeEventListener"in l?l.removeEventListener("change",u):l.removeListener(u))},s=a.watchEffect(()=>{r.value&&(i(),l=n.matchMedia(d.toValue(e)),"addEventListener"in l?l.addEventListener("change",u):l.addListener(u),o.value=l.matches)});return d.tryOnScopeDispose(()=>{s(),i(),l=void 0}),o}const Gt={sm:640,md:768,lg:1024,xl:1280,"2xl":1536},Yt={xs:0,sm:576,md:768,lg:992,xl:1200,xxl:1400},Ne={xs:0,sm:600,md:960,lg:1264,xl:1904},Xt={xs:0,sm:600,md:960,lg:1280,xl:1920,xxl:2560},Kt=Ne,Jt={xs:480,sm:576,md:768,lg:992,xl:1200,xxl:1600},Qt={xs:0,sm:600,md:1024,lg:1440,xl:1920},Zt={mobileS:320,mobileM:375,mobileL:425,tablet:768,laptop:1024,laptopL:1440,desktop4K:2560},Dt={"3xs":360,"2xs":480,xs:600,sm:768,md:1024,lg:1280,xl:1440,"2xl":1600,"3xl":1920,"4xl":2560},en={sm:576,md:768,lg:992,xl:1200};function tn(e,t={}){function n(f,v){let y=d.toValue(e[d.toValue(f)]);return v!=null&&(y=d.increaseWithUnit(y,v)),typeof y=="number"&&(y=`${y}px`),y}const{window:r=A,strategy:l="min-width"}=t;function o(f){return r?r.matchMedia(f).matches:!1}const u=f=>G(()=>`(min-width: ${n(f)})`,t),i=f=>G(()=>`(max-width: ${n(f)})`,t),s=Object.keys(e).reduce((f,v)=>(Object.defineProperty(f,v,{get:()=>l==="min-width"?u(v):i(v),enumerable:!0,configurable:!0}),f),{});function c(){const f=Object.keys(e).map(v=>[v,u(v)]);return a.computed(()=>f.filter(([,v])=>v.value).map(([v])=>v))}return Object.assign(s,{greaterOrEqual:u,smallerOrEqual:i,greater(f){return G(()=>`(min-width: ${n(f,.1)})`,t)},smaller(f){return G(()=>`(max-width: ${n(f,-.1)})`,t)},between(f,v){return G(()=>`(min-width: ${n(f)}) and (max-width: ${n(v,-.1)})`,t)},isGreater(f){return o(`(min-width: ${n(f,.1)})`)},isGreaterOrEqual(f){return o(`(min-width: ${n(f)})`)},isSmaller(f){return o(`(max-width: ${n(f,-.1)})`)},isSmallerOrEqual(f){return o(`(max-width: ${n(f)})`)},isInBetween(f,v){return o(`(min-width: ${n(f)}) and (max-width: ${n(v,-.1)})`)},current:c,active(){const f=c();return a.computed(()=>f.value.length===0?"":f.value.at(-1))}})}function nn(e){const{name:t,window:n=A}=e,r=x(()=>n&&"BroadcastChannel"in n),l=a.ref(!1),o=a.ref(),u=a.ref(),i=a.shallowRef(null),s=f=>{o.value&&o.value.postMessage(f)},c=()=>{o.value&&o.value.close(),l.value=!0};return r.value&&d.tryOnMounted(()=>{i.value=null,o.value=new BroadcastChannel(t),o.value.addEventListener("message",f=>{u.value=f.data},{passive:!0}),o.value.addEventListener("messageerror",f=>{i.value=f},{passive:!0}),o.value.addEventListener("close",()=>{l.value=!0})}),d.tryOnScopeDispose(()=>{c()}),{isSupported:r,channel:o,data:u,post:s,close:c,error:i,isClosed:l}}const xe=["hash","host","hostname","href","pathname","port","protocol","search"];function on(e={}){const{window:t=A}=e,n=Object.fromEntries(xe.map(o=>[o,a.ref()]));for(const[o,u]of d.objectEntries(n))a.watch(u,i=>{!t?.location||t.location[o]===i||(t.location[o]=i)});const r=o=>{var u;const{state:i,length:s}=t?.history||{},{origin:c}=t?.location||{};for(const f of xe)n[f].value=(u=t?.location)==null?void 0:u[f];return a.reactive({trigger:o,state:i,length:s,origin:c,...n})},l=a.ref(r("load"));return t&&(_(t,"popstate",()=>l.value=r("popstate"),{passive:!0}),_(t,"hashchange",()=>l.value=r("hashchange"),{passive:!0})),l}function rn(e,t=(r,l)=>r===l,n){const r=a.ref(e.value);return a.watch(()=>e.value,l=>{t(l,r.value)||(r.value=l)},n),r}function fe(e,t={}){const{controls:n=!1,navigator:r=U}=t,l=x(()=>r&&"permissions"in r);let o;const u=typeof e=="string"?{name:e}:e,i=a.ref(),s=()=>{o&&(i.value=o.state)},c=d.createSingletonPromise(async()=>{if(l.value){if(!o)try{o=await r.permissions.query(u),_(o,"change",s),s()}catch{i.value="prompt"}return o}});return c(),n?{state:i,isSupported:l,query:c}:i}function ln(e={}){const{navigator:t=U,read:n=!1,source:r,copiedDuring:l=1500,legacy:o=!1}=e,u=x(()=>t&&"clipboard"in t),i=fe("clipboard-read"),s=fe("clipboard-write"),c=a.computed(()=>u.value||o),f=a.ref(""),v=a.ref(!1),y=d.useTimeoutFn(()=>v.value=!1,l);function p(){u.value&&g(i.value)?t.clipboard.readText().then(b=>{f.value=b}):f.value=w()}c.value&&n&&_(["copy","cut"],p);async function m(b=d.toValue(r)){c.value&&b!=null&&(u.value&&g(s.value)?await t.clipboard.writeText(b):S(b),f.value=b,v.value=!0,y.start())}function S(b){const E=document.createElement("textarea");E.value=b??"",E.style.position="absolute",E.style.opacity="0",document.body.appendChild(E),E.select(),document.execCommand("copy"),E.remove()}function w(){var b,E,R;return(R=(E=(b=document?.getSelection)==null?void 0:b.call(document))==null?void 0:E.toString())!=null?R:""}function g(b){return b==="granted"||b==="prompt"}return{isSupported:c,text:f,copied:v,copy:m}}function an(e={}){const{navigator:t=U,read:n=!1,source:r,copiedDuring:l=1500}=e,o=x(()=>t&&"clipboard"in t),u=a.ref([]),i=a.ref(!1),s=d.useTimeoutFn(()=>i.value=!1,l);function c(){o.value&&t.clipboard.read().then(v=>{u.value=v})}o.value&&n&&_(["copy","cut"],c);async function f(v=d.toValue(r)){o.value&&v!=null&&(await t.clipboard.write(v),u.value=v,i.value=!0,s.start())}return{isSupported:o,content:u,copied:i,copy:f}}function re(e){return JSON.parse(JSON.stringify(e))}function un(e,t={}){const n=a.ref({}),{manual:r,clone:l=re,deep:o=!0,immediate:u=!0}=t;function i(){n.value=l(d.toValue(e))}return!r&&(a.isRef(e)||typeof e=="function")?a.watch(e,i,{...t,deep:o,immediate:u}):i(),{cloned:n,sync:i}}const de=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{},ve="__vueuse_ssr_handlers__",We=sn();function sn(){return ve in de||(de[ve]=de[ve]||{}),de[ve]}function pe(e,t){return We[e]||t}function cn(e,t){We[e]=t}function $e(e){return e==null?"any":e instanceof Set?"set":e instanceof Map?"map":e instanceof Date?"date":typeof e=="boolean"?"boolean":typeof e=="string"?"string":typeof e=="object"?"object":Number.isNaN(e)?"any":"number"}const Ee={boolean:{read:e=>e==="true",write:e=>String(e)},object:{read:e=>JSON.parse(e),write:e=>JSON.stringify(e)},number:{read:e=>Number.parseFloat(e),write:e=>String(e)},any:{read:e=>e,write:e=>String(e)},string:{read:e=>e,write:e=>String(e)},map:{read:e=>new Map(JSON.parse(e)),write:e=>JSON.stringify(Array.from(e.entries()))},set:{read:e=>new Set(JSON.parse(e)),write:e=>JSON.stringify(Array.from(e))},date:{read:e=>new Date(e),write:e=>e.toISOString()}},Te="vueuse-storage";function ye(e,t,n,r={}){var l;const{flush:o="pre",deep:u=!0,listenToStorageChanges:i=!0,writeDefaults:s=!0,mergeDefaults:c=!1,shallow:f,window:v=A,eventFilter:y,onError:p=O=>{console.error(O)},initOnMounted:m}=r,S=(f?a.shallowRef:a.ref)(typeof t=="function"?t():t);if(!n)try{n=pe("getDefaultStorage",()=>{var O;return(O=A)==null?void 0:O.localStorage})()}catch(O){p(O)}if(!n)return S;const w=d.toValue(t),g=$e(w),b=(l=r.serializer)!=null?l:Ee[g],{pause:E,resume:R}=d.pausableWatch(S,()=>V(S.value),{flush:o,deep:u,eventFilter:y});v&&i&&d.tryOnMounted(()=>{_(v,"storage",C),_(v,Te,F),m&&C()}),m||C();function k(O,P){v&&v.dispatchEvent(new CustomEvent(Te,{detail:{key:e,oldValue:O,newValue:P,storageArea:n}}))}function V(O){try{const P=n.getItem(e);if(O==null)k(P,null),n.removeItem(e);else{const I=b.write(O);P!==I&&(n.setItem(e,I),k(P,I))}}catch(P){p(P)}}function T(O){const P=O?O.newValue:n.getItem(e);if(P==null)return s&&w!=null&&n.setItem(e,b.write(w)),w;if(!O&&c){const I=b.read(P);return typeof c=="function"?c(I,w):g==="object"&&!Array.isArray(I)?{...w,...I}:I}else return typeof P!="string"?P:b.read(P)}function C(O){if(!(O&&O.storageArea!==n)){if(O&&O.key==null){S.value=w;return}if(!(O&&O.key!==e)){E();try{O?.newValue!==b.write(S.value)&&(S.value=T(O))}catch(P){p(P)}finally{O?a.nextTick(R):R()}}}}function F(O){C(O.detail)}return S}function Oe(e){return G("(prefers-color-scheme: dark)",e)}function He(e={}){const{selector:t="html",attribute:n="class",initialValue:r="auto",window:l=A,storage:o,storageKey:u="vueuse-color-scheme",listenToStorageChanges:i=!0,storageRef:s,emitAuto:c,disableTransition:f=!0}=e,v={auto:"",light:"light",dark:"dark",...e.modes||{}},y=Oe({window:l}),p=a.computed(()=>y.value?"dark":"light"),m=s||(u==null?d.toRef(r):ye(u,r,o,{window:l,listenToStorageChanges:i})),S=a.computed(()=>m.value==="auto"?p.value:m.value),w=pe("updateHTMLAttrs",(R,k,V)=>{const T=typeof R=="string"?l?.document.querySelector(R):L(R);if(!T)return;let C;if(f&&(C=l.document.createElement("style"),C.appendChild(document.createTextNode("*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),l.document.head.appendChild(C)),k==="class"){const F=V.split(/\s/g);Object.values(v).flatMap(O=>(O||"").split(/\s/g)).filter(Boolean).forEach(O=>{F.includes(O)?T.classList.add(O):T.classList.remove(O)})}else T.setAttribute(k,V);f&&(l.getComputedStyle(C).opacity,document.head.removeChild(C))});function g(R){var k;w(t,n,(k=v[R])!=null?k:R)}function b(R){e.onChanged?e.onChanged(R,g):g(R)}a.watch(S,b,{flush:"post",immediate:!0}),d.tryOnMounted(()=>b(S.value));const E=a.computed({get(){return c?m.value:S.value},set(R){m.value=R}});try{return Object.assign(E,{store:m,system:p,state:S})}catch{return E}}function fn(e=a.ref(!1)){const t=d.createEventHook(),n=d.createEventHook(),r=d.createEventHook();let l=d.noop;const o=s=>(r.trigger(s),e.value=!0,new Promise(c=>{l=c})),u=s=>{e.value=!1,t.trigger(s),l({data:s,isCanceled:!1})},i=s=>{e.value=!1,n.trigger(s),l({data:s,isCanceled:!0})};return{isRevealed:a.computed(()=>e.value),reveal:o,confirm:u,cancel:i,onReveal:r.on,onConfirm:t.on,onCancel:n.on}}function le(e,t,n={}){const{window:r=A,initialValue:l="",observe:o=!1}=n,u=a.ref(l),i=a.computed(()=>{var c;return L(t)||((c=r?.document)==null?void 0:c.documentElement)});function s(){var c;const f=d.toValue(e),v=d.toValue(i);if(v&&r){const y=(c=r.getComputedStyle(v).getPropertyValue(f))==null?void 0:c.trim();u.value=y||l}}return o&&te(i,s,{attributeFilter:["style","class"],window:r}),a.watch([i,()=>d.toValue(e)],s,{immediate:!0}),a.watch(u,c=>{var f;(f=i.value)!=null&&f.style&&i.value.style.setProperty(d.toValue(e),c)}),u}function Ue(e){const t=a.getCurrentInstance(),n=d.computedWithControl(()=>null,()=>e?L(e):t.proxy.$el);return a.onUpdated(n.trigger),a.onMounted(n.trigger),n}function dn(e,t){const n=a.shallowRef(c()),r=d.toRef(e),l=a.computed({get(){var f;const v=r.value;let y=t?.getIndexOf?t.getIndexOf(n.value,v):v.indexOf(n.value);return y<0&&(y=(f=t?.fallbackIndex)!=null?f:0),y},set(f){o(f)}});function o(f){const v=r.value,y=v.length,p=(f%y+y)%y,m=v[p];return n.value=m,m}function u(f=1){return o(l.value+f)}function i(f=1){return u(f)}function s(f=1){return u(-f)}function c(){var f,v;return(v=d.toValue((f=t?.initialValue)!=null?f:d.toValue(e)[0]))!=null?v:void 0}return a.watch(r,()=>o(l.value)),{state:n,index:l,next:i,prev:s,go:o}}function vn(e={}){const{valueDark:t="dark",valueLight:n="",window:r=A}=e,l=He({...e,onChanged:(i,s)=>{var c;e.onChanged?(c=e.onChanged)==null||c.call(e,i==="dark",s,i):s(i)},modes:{dark:t,light:n}}),o=a.computed(()=>l.system?l.system.value:Oe({window:r}).value?"dark":"light");return a.computed({get(){return l.value==="dark"},set(i){const s=i?"dark":"light";o.value===s?l.value="auto":l.value=s}})}function Be(e){return e}function pn(e,t){return e.value=t}function yn(e){return e?typeof e=="function"?e:re:Be}function mn(e){return e?typeof e=="function"?e:re:Be}function je(e,t={}){const{clone:n=!1,dump:r=yn(n),parse:l=mn(n),setSource:o=pn}=t;function u(){return a.markRaw({snapshot:r(e.value),timestamp:d.timestamp()})}const i=a.ref(u()),s=a.ref([]),c=a.ref([]),f=E=>{o(e,l(E.snapshot)),i.value=E},v=()=>{s.value.unshift(i.value),i.value=u(),t.capacity&&s.value.length>t.capacity&&s.value.splice(t.capacity,Number.POSITIVE_INFINITY),c.value.length&&c.value.splice(0,c.value.length)},y=()=>{s.value.splice(0,s.value.length),c.value.splice(0,c.value.length)},p=()=>{const E=s.value.shift();E&&(c.value.unshift(i.value),f(E))},m=()=>{const E=c.value.shift();E&&(s.value.unshift(i.value),f(E))},S=()=>{f(i.value)},w=a.computed(()=>[i.value,...s.value]),g=a.computed(()=>s.value.length>0),b=a.computed(()=>c.value.length>0);return{source:e,undoStack:s,redoStack:c,last:i,history:w,canUndo:g,canRedo:b,clear:y,commit:v,reset:S,undo:p,redo:m}}function ke(e,t={}){const{deep:n=!1,flush:r="pre",eventFilter:l}=t,{eventFilter:o,pause:u,resume:i,isActive:s}=d.pausableFilter(l),{ignoreUpdates:c,ignorePrevAsyncUpdates:f,stop:v}=d.watchIgnorable(e,w,{deep:n,flush:r,eventFilter:o});function y(R,k){f(),c(()=>{R.value=k})}const p=je(e,{...t,clone:t.clone||n,setSource:y}),{clear:m,commit:S}=p;function w(){f(),S()}function g(R){i(),R&&w()}function b(R){let k=!1;const V=()=>k=!0;c(()=>{R(V)}),k||w()}function E(){v(),m()}return{...p,isTracking:s,pause:u,resume:g,commit:w,batch:b,dispose:E}}function gn(e,t={}){const n=t.debounce?d.debounceFilter(t.debounce):void 0;return{...ke(e,{...t,eventFilter:n})}}function hn(e={}){const{window:t=A,eventFilter:n=d.bypassFilter}=e,r=a.ref({x:null,y:null,z:null}),l=a.ref({alpha:null,beta:null,gamma:null}),o=a.ref(0),u=a.ref({x:null,y:null,z:null});if(t){const i=d.createFilterWrapper(n,s=>{r.value=s.acceleration,u.value=s.accelerationIncludingGravity,l.value=s.rotationRate,o.value=s.interval});_(t,"devicemotion",i)}return{acceleration:r,accelerationIncludingGravity:u,rotationRate:l,interval:o}}function ze(e={}){const{window:t=A}=e,n=x(()=>t&&"DeviceOrientationEvent"in t),r=a.ref(!1),l=a.ref(null),o=a.ref(null),u=a.ref(null);return t&&n.value&&_(t,"deviceorientation",i=>{r.value=i.absolute,l.value=i.alpha,o.value=i.beta,u.value=i.gamma}),{isSupported:n,isAbsolute:r,alpha:l,beta:o,gamma:u}}function wn(e={}){const{window:t=A}=e,n=a.ref(1);if(t){let r=function(){n.value=t.devicePixelRatio,l(),o=t.matchMedia(`(resolution: ${n.value}dppx)`),o.addEventListener("change",r,{once:!0})},l=function(){o?.removeEventListener("change",r)},o;r(),d.tryOnScopeDispose(l)}return{pixelRatio:n}}function bn(e={}){const{navigator:t=U,requestPermissions:n=!1,constraints:r={audio:!0,video:!0},onUpdated:l}=e,o=a.ref([]),u=a.computed(()=>o.value.filter(m=>m.kind==="videoinput")),i=a.computed(()=>o.value.filter(m=>m.kind==="audioinput")),s=a.computed(()=>o.value.filter(m=>m.kind==="audiooutput")),c=x(()=>t&&t.mediaDevices&&t.mediaDevices.enumerateDevices),f=a.ref(!1);let v;async function y(){c.value&&(o.value=await t.mediaDevices.enumerateDevices(),l?.(o.value),v&&(v.getTracks().forEach(m=>m.stop()),v=null))}async function p(){if(!c.value)return!1;if(f.value)return!0;const{state:m,query:S}=fe("camera",{controls:!0});return await S(),m.value!=="granted"&&(v=await t.mediaDevices.getUserMedia(r),y()),f.value=!0,f.value}return c.value&&(n&&p(),_(t.mediaDevices,"devicechange",y),y()),{devices:o,ensurePermissions:p,permissionGranted:f,videoInputs:u,audioInputs:i,audioOutputs:s,isSupported:c}}function Sn(e={}){var t;const n=a.ref((t=e.enabled)!=null?t:!1),r=e.video,l=e.audio,{navigator:o=U}=e,u=x(()=>{var p;return(p=o?.mediaDevices)==null?void 0:p.getDisplayMedia}),i={audio:l,video:r},s=a.shallowRef();async function c(){var p;if(!(!u.value||s.value))return s.value=await o.mediaDevices.getDisplayMedia(i),(p=s.value)==null||p.getTracks().forEach(m=>m.addEventListener("ended",v)),s.value}async function f(){var p;(p=s.value)==null||p.getTracks().forEach(m=>m.stop()),s.value=void 0}function v(){f(),n.value=!1}async function y(){return await c(),s.value&&(n.value=!0),s.value}return a.watch(n,p=>{p?c():f()},{immediate:!0}),{isSupported:u,stream:s,start:y,stop:v,enabled:n}}function En(e={}){const{document:t=H}=e;if(!t)return a.ref("visible");const n=a.ref(t.visibilityState);return _(t,"visibilitychange",()=>{n.value=t.visibilityState}),n}function Tn(e,t={}){var n,r;const{pointerTypes:l,preventDefault:o,stopPropagation:u,exact:i,onMove:s,onEnd:c,onStart:f,initialValue:v,axis:y="both",draggingElement:p=A,containerElement:m,handle:S=e}=t,w=a.ref((n=d.toValue(v))!=null?n:{x:0,y:0}),g=a.ref(),b=T=>l?l.includes(T.pointerType):!0,E=T=>{d.toValue(o)&&T.preventDefault(),d.toValue(u)&&T.stopPropagation()},R=T=>{var C;if(T.button!==0||d.toValue(t.disabled)||!b(T)||d.toValue(i)&&T.target!==d.toValue(e))return;const F=d.toValue(m),O=(C=F?.getBoundingClientRect)==null?void 0:C.call(F),P=d.toValue(e).getBoundingClientRect(),I={x:T.clientX-(F?P.left-O.left+F.scrollLeft:P.left),y:T.clientY-(F?P.top-O.top+F.scrollTop:P.top)};f?.(I,T)!==!1&&(g.value=I,E(T))},k=T=>{if(d.toValue(t.disabled)||!b(T)||!g.value)return;const C=d.toValue(m),F=d.toValue(e).getBoundingClientRect();let{x:O,y:P}=w.value;(y==="x"||y==="both")&&(O=T.clientX-g.value.x,C&&(O=Math.min(Math.max(0,O),C.scrollWidth-F.width))),(y==="y"||y==="both")&&(P=T.clientY-g.value.y,C&&(P=Math.min(Math.max(0,P),C.scrollHeight-F.height))),w.value={x:O,y:P},s?.(w.value,T),E(T)},V=T=>{d.toValue(t.disabled)||!b(T)||g.value&&(g.value=void 0,c?.(w.value,T),E(T))};if(d.isClient){const T={capture:(r=t.capture)!=null?r:!0};_(S,"pointerdown",R,T),_(p,"pointermove",k,T),_(p,"pointerup",V,T)}return{...d.toRefs(w),position:w,isDragging:a.computed(()=>!!g.value),style:a.computed(()=>`left:${w.value.x}px;top:${w.value.y}px;`)}}function On(e,t={}){const n=a.ref(!1),r=a.shallowRef(null);let l=0,o=!0;if(d.isClient){const u=typeof t=="function"?{onDrop:t}:t,i=s=>{var c,f;const v=Array.from((f=(c=s.dataTransfer)==null?void 0:c.files)!=null?f:[]);return r.value=v.length===0?null:v};_(e,"dragenter",s=>{var c,f;const v=Array.from(((c=s?.dataTransfer)==null?void 0:c.items)||[]).map(y=>y.kind==="file"?y.type:null).filter(d.notNullish);if(u.dataTypes&&s.dataTransfer){const y=a.unref(u.dataTypes);if(o=typeof y=="function"?y(v):y?y.some(p=>v.includes(p)):!0,!o)return}s.preventDefault(),l+=1,n.value=!0,(f=u.onEnter)==null||f.call(u,i(s),s)}),_(e,"dragover",s=>{var c;o&&(s.preventDefault(),(c=u.onOver)==null||c.call(u,i(s),s))}),_(e,"dragleave",s=>{var c;o&&(s.preventDefault(),l-=1,l===0&&(n.value=!1),(c=u.onLeave)==null||c.call(u,i(s),s))}),_(e,"drop",s=>{var c;s.preventDefault(),l=0,n.value=!1,(c=u.onDrop)==null||c.call(u,i(s),s)})}return{files:r,isOverDropZone:n}}function me(e,t,n={}){const{window:r=A,...l}=n;let o;const u=x(()=>r&&"ResizeObserver"in r),i=()=>{o&&(o.disconnect(),o=void 0)},s=a.computed(()=>Array.isArray(e)?e.map(v=>L(v)):[L(e)]),c=a.watch(s,v=>{if(i(),u.value&&r){o=new ResizeObserver(t);for(const y of v)y&&o.observe(y,l)}},{immediate:!0,flush:"post"}),f=()=>{i(),c()};return d.tryOnScopeDispose(f),{isSupported:u,stop:f}}function kn(e,t={}){const{reset:n=!0,windowResize:r=!0,windowScroll:l=!0,immediate:o=!0}=t,u=a.ref(0),i=a.ref(0),s=a.ref(0),c=a.ref(0),f=a.ref(0),v=a.ref(0),y=a.ref(0),p=a.ref(0);function m(){const S=L(e);if(!S){n&&(u.value=0,i.value=0,s.value=0,c.value=0,f.value=0,v.value=0,y.value=0,p.value=0);return}const w=S.getBoundingClientRect();u.value=w.height,i.value=w.bottom,s.value=w.left,c.value=w.right,f.value=w.top,v.value=w.width,y.value=w.x,p.value=w.y}return me(e,m),a.watch(()=>L(e),S=>!S&&m()),te(e,m,{attributeFilter:["style","class"]}),l&&_("scroll",m,{capture:!0,passive:!0}),r&&_("resize",m,{passive:!0}),d.tryOnMounted(()=>{o&&m()}),{height:u,bottom:i,left:s,right:c,top:f,width:v,x:y,y:p,update:m}}function _n(e){const{x:t,y:n,document:r=H,multiple:l,interval:o="requestAnimationFrame",immediate:u=!0}=e,i=x(()=>d.toValue(l)?r&&"elementsFromPoint"in r:r&&"elementFromPoint"in r),s=a.ref(null),c=()=>{var v,y;s.value=d.toValue(l)?(v=r?.elementsFromPoint(d.toValue(t),d.toValue(n)))!=null?v:[]:(y=r?.elementFromPoint(d.toValue(t),d.toValue(n)))!=null?y:null},f=o==="requestAnimationFrame"?Z(c,{immediate:u}):d.useIntervalFn(c,o,{immediate:u});return{isSupported:i,element:s,...f}}function Rn(e,t={}){const{delayEnter:n=0,delayLeave:r=0,window:l=A}=t,o=a.ref(!1);let u;const i=s=>{const c=s?n:r;u&&(clearTimeout(u),u=void 0),c?u=setTimeout(()=>o.value=s,c):o.value=s};return l&&(_(e,"mouseenter",()=>i(!0),{passive:!0}),_(e,"mouseleave",()=>i(!1),{passive:!0})),o}function qe(e,t={width:0,height:0},n={}){const{window:r=A,box:l="content-box"}=n,o=a.computed(()=>{var v,y;return(y=(v=L(e))==null?void 0:v.namespaceURI)==null?void 0:y.includes("svg")}),u=a.ref(t.width),i=a.ref(t.height),{stop:s}=me(e,([v])=>{const y=l==="border-box"?v.borderBoxSize:l==="content-box"?v.contentBoxSize:v.devicePixelContentBoxSize;if(r&&o.value){const p=L(e);if(p){const m=p.getBoundingClientRect();u.value=m.width,i.value=m.height}}else if(y){const p=Array.isArray(y)?y:[y];u.value=p.reduce((m,{inlineSize:S})=>m+S,0),i.value=p.reduce((m,{blockSize:S})=>m+S,0)}else u.value=v.contentRect.width,i.value=v.contentRect.height},n);d.tryOnMounted(()=>{const v=L(e);v&&(u.value="offsetWidth"in v?v.offsetWidth:t.width,i.value="offsetHeight"in v?v.offsetHeight:t.height)});const c=a.watch(()=>L(e),v=>{u.value=v?t.width:0,i.value=v?t.height:0});function f(){s(),c()}return{width:u,height:i,stop:f}}function Ge(e,t,n={}){const{root:r,rootMargin:l="0px",threshold:o=.1,window:u=A,immediate:i=!0}=n,s=x(()=>u&&"IntersectionObserver"in u),c=a.computed(()=>{const m=d.toValue(e);return(Array.isArray(m)?m:[m]).map(L).filter(d.notNullish)});let f=d.noop;const v=a.ref(i),y=s.value?a.watch(()=>[c.value,L(r),v.value],([m,S])=>{if(f(),!v.value||!m.length)return;const w=new IntersectionObserver(t,{root:L(S),rootMargin:l,threshold:o});m.forEach(g=>g&&w.observe(g)),f=()=>{w.disconnect(),f=d.noop}},{immediate:i,flush:"post"}):d.noop,p=()=>{f(),y(),v.value=!1};return d.tryOnScopeDispose(p),{isSupported:s,isActive:v,pause(){f(),v.value=!1},resume(){v.value=!0},stop:p}}function Ye(e,t={}){const{window:n=A,scrollTarget:r,threshold:l=0}=t,o=a.ref(!1);return Ge(e,u=>{let i=o.value,s=0;for(const c of u)c.time>=s&&(s=c.time,i=c.isIntersecting);o.value=i},{root:r,window:n,threshold:l}),o}const ae=new Map;function Fn(e){const t=a.getCurrentScope();function n(i){var s;const c=ae.get(e)||new Set;c.add(i),ae.set(e,c);const f=()=>l(i);return(s=t?.cleanups)==null||s.push(f),f}function r(i){function s(...c){l(s),i(...c)}return n(s)}function l(i){const s=ae.get(e);s&&(s.delete(i),s.size||o())}function o(){ae.delete(e)}function u(i,s){var c;(c=ae.get(e))==null||c.forEach(f=>f(i,s))}return{on:n,once:r,off:l,emit:u,reset:o}}function Pn(e){return e===!0?{}:e}function Cn(e,t=[],n={}){const r=a.ref(null),l=a.ref(null),o=a.ref("CONNECTING"),u=a.ref(null),i=a.shallowRef(null),s=d.toRef(e),c=a.shallowRef(null);let f=!1,v=0;const{withCredentials:y=!1,immediate:p=!0}=n,m=()=>{d.isClient&&u.value&&(u.value.close(),u.value=null,o.value="CLOSED",f=!0)},S=()=>{if(f||typeof s.value>"u")return;const g=new EventSource(s.value,{withCredentials:y});o.value="CONNECTING",u.value=g,g.onopen=()=>{o.value="OPEN",i.value=null},g.onerror=b=>{if(o.value="CLOSED",i.value=b,g.readyState===2&&!f&&n.autoReconnect){g.close();const{retries:E=-1,delay:R=1e3,onFailed:k}=Pn(n.autoReconnect);v+=1,typeof E=="number"&&(E<0||v<E)||typeof E=="function"&&E()?setTimeout(S,R):k?.()}},g.onmessage=b=>{r.value=null,l.value=b.data,c.value=b.lastEventId};for(const b of t)_(g,b,E=>{r.value=b,l.value=E.data||null})},w=()=>{d.isClient&&(m(),f=!1,v=0,S())};return p&&a.watch(s,w,{immediate:!0}),d.tryOnScopeDispose(m),{eventSource:u,event:r,data:l,status:o,error:i,open:w,close:m,lastEventId:c}}function Vn(e={}){const{initialValue:t=""}=e,n=x(()=>typeof window<"u"&&"EyeDropper"in window),r=a.ref(t);async function l(o){if(!n.value)return;const i=await new window.EyeDropper().open(o);return r.value=i.sRGBHex,i}return{isSupported:n,sRGBHex:r,open:l}}function An(e=null,t={}){const{baseUrl:n="",rel:r="icon",document:l=H}=t,o=d.toRef(e),u=i=>{const s=l?.head.querySelectorAll(`link[rel*="${r}"]`);if(!s||s.length===0){const c=l?.createElement("link");c&&(c.rel=r,c.href=`${n}${i}`,c.type=`image/${i.split(".").pop()}`,l?.head.append(c));return}s?.forEach(c=>c.href=`${n}${i}`)};return a.watch(o,(i,s)=>{typeof i=="string"&&i!==s&&u(i)},{immediate:!0}),o}const In={json:"application/json",text:"text/plain"};function ge(e){return e&&d.containsProp(e,"immediate","refetch","initialData","timeout","beforeFetch","afterFetch","onFetchError","fetch","updateDataOnError")}const Mn=/^(?:[a-z][a-z\d+\-.]*:)?\/\//i;function Ln(e){return Mn.test(e)}function ue(e){return typeof Headers<"u"&&e instanceof Headers?Object.fromEntries(e.entries()):e}function ne(e,...t){return e==="overwrite"?async n=>{const r=t[t.length-1];return r?{...n,...await r(n)}:n}:async n=>{for(const r of t)r&&(n={...n,...await r(n)});return n}}function Nn(e={}){const t=e.combination||"chain",n=e.options||{},r=e.fetchOptions||{};function l(o,...u){const i=a.computed(()=>{const f=d.toValue(e.baseUrl),v=d.toValue(o);return f&&!Ln(v)?xn(f,v):v});let s=n,c=r;return u.length>0&&(ge(u[0])?s={...s,...u[0],beforeFetch:ne(t,n.beforeFetch,u[0].beforeFetch),afterFetch:ne(t,n.afterFetch,u[0].afterFetch),onFetchError:ne(t,n.onFetchError,u[0].onFetchError)}:c={...c,...u[0],headers:{...ue(c.headers)||{},...ue(u[0].headers)||{}}}),u.length>1&&ge(u[1])&&(s={...s,...u[1],beforeFetch:ne(t,n.beforeFetch,u[1].beforeFetch),afterFetch:ne(t,n.afterFetch,u[1].afterFetch),onFetchError:ne(t,n.onFetchError,u[1].onFetchError)}),Xe(i,c,s)}return l}function Xe(e,...t){var n;const r=typeof AbortController=="function";let l={},o={immediate:!0,refetch:!1,timeout:0,updateDataOnError:!1};const u={method:"GET",type:"text",payload:void 0};t.length>0&&(ge(t[0])?o={...o,...t[0]}:l=t[0]),t.length>1&&ge(t[1])&&(o={...o,...t[1]});const{fetch:i=(n=A)==null?void 0:n.fetch,initialData:s,timeout:c}=o,f=d.createEventHook(),v=d.createEventHook(),y=d.createEventHook(),p=a.ref(!1),m=a.ref(!1),S=a.ref(!1),w=a.ref(null),g=a.shallowRef(null),b=a.shallowRef(null),E=a.shallowRef(s||null),R=a.computed(()=>r&&m.value);let k,V;const T=()=>{r&&(k?.abort(),k=new AbortController,k.signal.onabort=()=>S.value=!0,l={...l,signal:k.signal})},C=M=>{m.value=M,p.value=!M};c&&(V=d.useTimeoutFn(T,c,{immediate:!1}));let F=0;const O=async(M=!1)=>{var $,j;T(),C(!0),b.value=null,w.value=null,S.value=!1,F+=1;const ie=F,D={method:u.method,headers:{}};if(u.payload){const z=ue(D.headers),Q=d.toValue(u.payload);!u.payloadType&&Q&&Object.getPrototypeOf(Q)===Object.prototype&&!(Q instanceof FormData)&&(u.payloadType="json"),u.payloadType&&(z["Content-Type"]=($=In[u.payloadType])!=null?$:u.payloadType),D.body=u.payloadType==="json"?JSON.stringify(Q):Q}let Ot=!1;const se={url:d.toValue(e),options:{...D,...l},cancel:()=>{Ot=!0}};if(o.beforeFetch&&Object.assign(se,await o.beforeFetch(se)),Ot||!i)return C(!1),Promise.resolve(null);let ee=null;return V&&V.start(),i(se.url,{...D,...se.options,headers:{...ue(D.headers),...ue((j=se.options)==null?void 0:j.headers)}}).then(async z=>{if(g.value=z,w.value=z.status,ee=await z.clone()[u.type](),!z.ok)throw E.value=s||null,new Error(z.statusText);return o.afterFetch&&({data:ee}=await o.afterFetch({data:ee,response:z})),E.value=ee,f.trigger(z),z}).catch(async z=>{let Q=z.message||z.name;if(o.onFetchError&&({error:Q,data:ee}=await o.onFetchError({data:ee,error:z,response:g.value})),b.value=Q,o.updateDataOnError&&(E.value=ee),v.trigger(z),M)throw z;return null}).finally(()=>{ie===F&&C(!1),V&&V.stop(),y.trigger(null)})},P=d.toRef(o.refetch);a.watch([P,d.toRef(e)],([M])=>M&&O(),{deep:!0});const I={isFinished:a.readonly(p),isFetching:a.readonly(m),statusCode:w,response:g,error:b,data:E,canAbort:R,aborted:S,abort:T,execute:O,onFetchResponse:f.on,onFetchError:v.on,onFetchFinally:y.on,get:N("GET"),put:N("PUT"),post:N("POST"),delete:N("DELETE"),patch:N("PATCH"),head:N("HEAD"),options:N("OPTIONS"),json:W("json"),text:W("text"),blob:W("blob"),arrayBuffer:W("arrayBuffer"),formData:W("formData")};function N(M){return($,j)=>{if(!m.value)return u.method=M,u.payload=$,u.payloadType=j,a.isRef(u.payload)&&a.watch([P,d.toRef(u.payload)],([ie])=>ie&&O(),{deep:!0}),{...I,then(ie,D){return B().then(ie,D)}}}}function B(){return new Promise((M,$)=>{d.until(p).toBe(!0).then(()=>M(I)).catch(j=>$(j))})}function W(M){return()=>{if(!m.value)return u.type=M,{...I,then($,j){return B().then($,j)}}}}return o.immediate&&Promise.resolve().then(()=>O()),{...I,then(M,$){return B().then(M,$)}}}function xn(e,t){return!e.endsWith("/")&&!t.startsWith("/")?`${e}/${t}`:`${e}${t}`}const Wn={multiple:!0,accept:"*",reset:!1,directory:!1};function $n(e={}){const{document:t=H}=e,n=a.ref(null),{on:r,trigger:l}=d.createEventHook();let o;t&&(o=t.createElement("input"),o.type="file",o.onchange=s=>{const c=s.target;n.value=c.files,l(n.value)});const u=()=>{n.value=null,o&&o.value&&(o.value="",l(null))},i=s=>{if(!o)return;const c={...Wn,...e,...s};o.multiple=c.multiple,o.accept=c.accept,o.webkitdirectory=c.directory,d.hasOwn(c,"capture")&&(o.capture=c.capture),c.reset&&u(),o.click()};return{files:a.readonly(n),open:i,reset:u,onChange:r}}function Hn(e={}){const{window:t=A,dataType:n="Text"}=e,r=t,l=x(()=>r&&"showSaveFilePicker"in r&&"showOpenFilePicker"in r),o=a.ref(),u=a.ref(),i=a.ref(),s=a.computed(()=>{var b,E;return(E=(b=i.value)==null?void 0:b.name)!=null?E:""}),c=a.computed(()=>{var b,E;return(E=(b=i.value)==null?void 0:b.type)!=null?E:""}),f=a.computed(()=>{var b,E;return(E=(b=i.value)==null?void 0:b.size)!=null?E:0}),v=a.computed(()=>{var b,E;return(E=(b=i.value)==null?void 0:b.lastModified)!=null?E:0});async function y(b={}){if(!l.value)return;const[E]=await r.showOpenFilePicker({...d.toValue(e),...b});o.value=E,await g()}async function p(b={}){l.value&&(o.value=await r.showSaveFilePicker({...e,...b}),u.value=void 0,await g())}async function m(b={}){if(l.value){if(!o.value)return S(b);if(u.value){const E=await o.value.createWritable();await E.write(u.value),await E.close()}await w()}}async function S(b={}){if(l.value){if(o.value=await r.showSaveFilePicker({...e,...b}),u.value){const E=await o.value.createWritable();await E.write(u.value),await E.close()}await w()}}async function w(){var b;i.value=await((b=o.value)==null?void 0:b.getFile())}async function g(){var b,E;await w();const R=d.toValue(n);R==="Text"?u.value=await((b=i.value)==null?void 0:b.text()):R==="ArrayBuffer"?u.value=await((E=i.value)==null?void 0:E.arrayBuffer()):R==="Blob"&&(u.value=i.value)}return a.watch(()=>d.toValue(n),g),{isSupported:l,data:u,file:i,fileName:s,fileMIME:c,fileSize:f,fileLastModified:v,open:y,create:p,save:m,saveAs:S,updateData:g}}function Un(e,t={}){const{initialValue:n=!1,focusVisible:r=!1,preventScroll:l=!1}=t,o=a.ref(!1),u=a.computed(()=>L(e));_(u,"focus",s=>{var c,f;(!r||(f=(c=s.target).matches)!=null&&f.call(c,":focus-visible"))&&(o.value=!0)}),_(u,"blur",()=>o.value=!1);const i=a.computed({get:()=>o.value,set(s){var c,f;!s&&o.value?(c=u.value)==null||c.blur():s&&!o.value&&((f=u.value)==null||f.focus({preventScroll:l}))}});return a.watch(u,()=>{i.value=n},{immediate:!0,flush:"post"}),{focused:i}}function Bn(e,t={}){const n=Me(t),r=a.computed(()=>L(e));return{focused:a.computed(()=>r.value&&n.value?r.value.contains(n.value):!1)}}function jn(e){var t;const n=a.ref(0);if(typeof performance>"u")return n;const r=(t=e?.every)!=null?t:10;let l=performance.now(),o=0;return Z(()=>{if(o+=1,o>=r){const u=performance.now(),i=u-l;n.value=Math.round(1e3/(i/o)),l=u,o=0}}),n}const Ke=["fullscreenchange","webkitfullscreenchange","webkitendfullscreen","mozfullscreenchange","MSFullscreenChange"];function zn(e,t={}){const{document:n=H,autoExit:r=!1}=t,l=a.computed(()=>{var g;return(g=L(e))!=null?g:n?.querySelector("html")}),o=a.ref(!1),u=a.computed(()=>["requestFullscreen","webkitRequestFullscreen","webkitEnterFullscreen","webkitEnterFullScreen","webkitRequestFullScreen","mozRequestFullScreen","msRequestFullscreen"].find(g=>n&&g in n||l.value&&g in l.value)),i=a.computed(()=>["exitFullscreen","webkitExitFullscreen","webkitExitFullScreen","webkitCancelFullScreen","mozCancelFullScreen","msExitFullscreen"].find(g=>n&&g in n||l.value&&g in l.value)),s=a.computed(()=>["fullScreen","webkitIsFullScreen","webkitDisplayingFullscreen","mozFullScreen","msFullscreenElement"].find(g=>n&&g in n||l.value&&g in l.value)),c=["fullscreenElement","webkitFullscreenElement","mozFullScreenElement","msFullscreenElement"].find(g=>n&&g in n),f=x(()=>l.value&&n&&u.value!==void 0&&i.value!==void 0&&s.value!==void 0),v=()=>c?n?.[c]===l.value:!1,y=()=>{if(s.value){if(n&&n[s.value]!=null)return n[s.value];{const g=l.value;if(g?.[s.value]!=null)return!!g[s.value]}}return!1};async function p(){if(!(!f.value||!o.value)){if(i.value)if(n?.[i.value]!=null)await n[i.value]();else{const g=l.value;g?.[i.value]!=null&&await g[i.value]()}o.value=!1}}async function m(){if(!f.value||o.value)return;y()&&await p();const g=l.value;u.value&&g?.[u.value]!=null&&(await g[u.value](),o.value=!0)}async function S(){await(o.value?p():m())}const w=()=>{const g=y();(!g||g&&v())&&(o.value=g)};return _(n,Ke,w,!1),_(()=>L(l),Ke,w,!1),r&&d.tryOnScopeDispose(p),{isSupported:f,isFullscreen:o,enter:m,exit:p,toggle:S}}function qn(e){return a.computed(()=>e.value?{buttons:{a:e.value.buttons[0],b:e.value.buttons[1],x:e.value.buttons[2],y:e.value.buttons[3]},bumper:{left:e.value.buttons[4],right:e.value.buttons[5]},triggers:{left:e.value.buttons[6],right:e.value.buttons[7]},stick:{left:{horizontal:e.value.axes[0],vertical:e.value.axes[1],button:e.value.buttons[10]},right:{horizontal:e.value.axes[2],vertical:e.value.axes[3],button:e.value.buttons[11]}},dpad:{up:e.value.buttons[12],down:e.value.buttons[13],left:e.value.buttons[14],right:e.value.buttons[15]},back:e.value.buttons[8],start:e.value.buttons[9]}:null)}function Gn(e={}){const{navigator:t=U}=e,n=x(()=>t&&"getGamepads"in t),r=a.ref([]),l=d.createEventHook(),o=d.createEventHook(),u=p=>{const m=[],S="vibrationActuator"in p?p.vibrationActuator:null;return S&&m.push(S),p.hapticActuators&&m.push(...p.hapticActuators),{id:p.id,index:p.index,connected:p.connected,mapping:p.mapping,timestamp:p.timestamp,vibrationActuator:p.vibrationActuator,hapticActuators:m,axes:p.axes.map(w=>w),buttons:p.buttons.map(w=>({pressed:w.pressed,touched:w.touched,value:w.value}))}},i=()=>{const p=t?.getGamepads()||[];for(const m of p)m&&r.value[m.index]&&(r.value[m.index]=u(m))},{isActive:s,pause:c,resume:f}=Z(i),v=p=>{r.value.some(({index:m})=>m===p.index)||(r.value.push(u(p)),l.trigger(p.index)),f()},y=p=>{r.value=r.value.filter(m=>m.index!==p.index),o.trigger(p.index)};return _("gamepadconnected",p=>v(p.gamepad)),_("gamepaddisconnected",p=>y(p.gamepad)),d.tryOnMounted(()=>{const p=t?.getGamepads()||[];for(const m of p)m&&r.value[m.index]&&v(m)}),c(),{isSupported:n,onConnected:l.on,onDisconnected:o.on,gamepads:r,pause:c,resume:f,isActive:s}}function Yn(e={}){const{enableHighAccuracy:t=!0,maximumAge:n=3e4,timeout:r=27e3,navigator:l=U,immediate:o=!0}=e,u=x(()=>l&&"geolocation"in l),i=a.ref(null),s=a.shallowRef(null),c=a.ref({accuracy:0,latitude:Number.POSITIVE_INFINITY,longitude:Number.POSITIVE_INFINITY,altitude:null,altitudeAccuracy:null,heading:null,speed:null});function f(m){i.value=m.timestamp,c.value=m.coords,s.value=null}let v;function y(){u.value&&(v=l.geolocation.watchPosition(f,m=>s.value=m,{enableHighAccuracy:t,maximumAge:n,timeout:r}))}o&&y();function p(){v&&l&&l.geolocation.clearWatch(v)}return d.tryOnScopeDispose(()=>{p()}),{isSupported:u,coords:c,locatedAt:i,error:s,resume:y,pause:p}}const Xn=["mousemove","mousedown","resize","keydown","touchstart","wheel"],Kn=6e4;function Jn(e=Kn,t={}){const{initialState:n=!1,listenForVisibilityChange:r=!0,events:l=Xn,window:o=A,eventFilter:u=d.throttleFilter(50)}=t,i=a.ref(n),s=a.ref(d.timestamp());let c;const f=()=>{i.value=!1,clearTimeout(c),c=setTimeout(()=>i.value=!0,e)},v=d.createFilterWrapper(u,()=>{s.value=d.timestamp(),f()});if(o){const y=o.document;for(const p of l)_(o,p,v,{passive:!0});r&&_(y,"visibilitychange",()=>{y.hidden||v()}),f()}return{idle:i,lastActive:s,reset:f}}async function Qn(e){return new Promise((t,n)=>{const r=new Image,{src:l,srcset:o,sizes:u,class:i,loading:s,crossorigin:c,referrerPolicy:f}=e;r.src=l,o&&(r.srcset=o),u&&(r.sizes=u),i&&(r.className=i),s&&(r.loading=s),c&&(r.crossOrigin=c),f&&(r.referrerPolicy=f),r.onload=()=>t(r),r.onerror=n})}function Zn(e,t={}){const n=Le(()=>Qn(d.toValue(e)),void 0,{resetOnExecute:!0,...t});return a.watch(()=>d.toValue(e),()=>n.execute(t.delay),{deep:!0}),n}const Je=1;function Qe(e,t={}){const{throttle:n=0,idle:r=200,onStop:l=d.noop,onScroll:o=d.noop,offset:u={left:0,right:0,top:0,bottom:0},eventListenerOptions:i={capture:!1,passive:!0},behavior:s="auto",window:c=A,onError:f=T=>{console.error(T)}}=t,v=a.ref(0),y=a.ref(0),p=a.computed({get(){return v.value},set(T){S(T,void 0)}}),m=a.computed({get(){return y.value},set(T){S(void 0,T)}});function S(T,C){var F,O,P,I;if(!c)return;const N=d.toValue(e);if(!N)return;(P=N instanceof Document?c.document.body:N)==null||P.scrollTo({top:(F=d.toValue(C))!=null?F:m.value,left:(O=d.toValue(T))!=null?O:p.value,behavior:d.toValue(s)});const B=((I=N?.document)==null?void 0:I.documentElement)||N?.documentElement||N;p!=null&&(v.value=B.scrollLeft),m!=null&&(y.value=B.scrollTop)}const w=a.ref(!1),g=a.reactive({left:!0,right:!1,top:!0,bottom:!1}),b=a.reactive({left:!1,right:!1,top:!1,bottom:!1}),E=T=>{w.value&&(w.value=!1,b.left=!1,b.right=!1,b.top=!1,b.bottom=!1,l(T))},R=d.useDebounceFn(E,n+r),k=T=>{var C;if(!c)return;const F=((C=T?.document)==null?void 0:C.documentElement)||T?.documentElement||L(T),{display:O,flexDirection:P}=getComputedStyle(F),I=F.scrollLeft;b.left=I<v.value,b.right=I>v.value;const N=Math.abs(I)<=(u.left||0),B=Math.abs(I)+F.clientWidth>=F.scrollWidth-(u.right||0)-Je;O==="flex"&&P==="row-reverse"?(g.left=B,g.right=N):(g.left=N,g.right=B),v.value=I;let W=F.scrollTop;T===c.document&&!W&&(W=c.document.body.scrollTop),b.top=W<y.value,b.bottom=W>y.value;const M=Math.abs(W)<=(u.top||0),$=Math.abs(W)+F.clientHeight>=F.scrollHeight-(u.bottom||0)-Je;O==="flex"&&P==="column-reverse"?(g.top=$,g.bottom=M):(g.top=M,g.bottom=$),y.value=W},V=T=>{var C;if(!c)return;const F=(C=T.target.documentElement)!=null?C:T.target;k(F),w.value=!0,R(T),o(T)};return _(e,"scroll",n?d.useThrottleFn(V,n,!0,!1):V,i),d.tryOnMounted(()=>{try{const T=d.toValue(e);if(!T)return;k(T)}catch(T){f(T)}}),_(e,"scrollend",E,i),{x:p,y:m,isScrolling:w,arrivedState:g,directions:b,measure(){const T=d.toValue(e);c&&T&&k(T)}}}function he(e){return typeof Window<"u"&&e instanceof Window?e.document.documentElement:typeof Document<"u"&&e instanceof Document?e.documentElement:e}function Dn(e,t,n={}){var r;const{direction:l="bottom",interval:o=100,canLoadMore:u=()=>!0}=n,i=a.reactive(Qe(e,{...n,offset:{[l]:(r=n.distance)!=null?r:0,...n.offset}})),s=a.ref(),c=a.computed(()=>!!s.value),f=a.computed(()=>he(d.toValue(e))),v=Ye(f);function y(){if(i.measure(),!f.value||!v.value||!u(f.value))return;const{scrollHeight:p,clientHeight:m,scrollWidth:S,clientWidth:w}=f.value,g=l==="bottom"||l==="top"?p<=m:S<=w;(i.arrivedState[l]||g)&&(s.value||(s.value=Promise.all([t(i),new Promise(b=>setTimeout(b,o))]).finally(()=>{s.value=null,a.nextTick(()=>y())})))}return a.watch(()=>[i.arrivedState[l],v.value],y,{immediate:!0}),{isLoading:c}}const eo=["mousedown","mouseup","keydown","keyup"];function to(e,t={}){const{events:n=eo,document:r=H,initial:l=null}=t,o=a.ref(l);return r&&n.forEach(u=>{_(r,u,i=>{typeof i.getModifierState=="function"&&(o.value=i.getModifierState(e))})}),o}function no(e,t,n={}){const{window:r=A}=n;return ye(e,t,r?.localStorage,n)}const Ze={ctrl:"control",command:"meta",cmd:"meta",option:"alt",up:"arrowup",down:"arrowdown",left:"arrowleft",right:"arrowright"};function oo(e={}){const{reactive:t=!1,target:n=A,aliasMap:r=Ze,passive:l=!0,onEventFired:o=d.noop}=e,u=a.reactive(new Set),i={toJSON(){return{}},current:u},s=t?a.reactive(i):i,c=new Set,f=new Set;function v(S,w){S in s&&(t?s[S]=w:s[S].value=w)}function y(){u.clear();for(const S of f)v(S,!1)}function p(S,w){var g,b;const E=(g=S.key)==null?void 0:g.toLowerCase(),k=[(b=S.code)==null?void 0:b.toLowerCase(),E].filter(Boolean);E&&(w?u.add(E):u.delete(E));for(const V of k)f.add(V),v(V,w);E==="meta"&&!w?(c.forEach(V=>{u.delete(V),v(V,!1)}),c.clear()):typeof S.getModifierState=="function"&&S.getModifierState("Meta")&&w&&[...u,...k].forEach(V=>c.add(V))}_(n,"keydown",S=>(p(S,!0),o(S)),{passive:l}),_(n,"keyup",S=>(p(S,!1),o(S)),{passive:l}),_("blur",y,{passive:!0}),_("focus",y,{passive:!0});const m=new Proxy(s,{get(S,w,g){if(typeof w!="string")return Reflect.get(S,w,g);if(w=w.toLowerCase(),w in r&&(w=r[w]),!(w in s))if(/[+_-]/.test(w)){const E=w.split(/[+_-]/g).map(R=>R.trim());s[w]=a.computed(()=>E.every(R=>d.toValue(m[R])))}else s[w]=a.ref(!1);const b=Reflect.get(S,w,g);return t?d.toValue(b):b}});return m}function _e(e,t){d.toValue(e)&&t(d.toValue(e))}function ro(e){let t=[];for(let n=0;n<e.length;++n)t=[...t,[e.start(n),e.end(n)]];return t}function Re(e){return Array.from(e).map(({label:t,kind:n,language:r,mode:l,activeCues:o,cues:u,inBandMetadataTrackDispatchType:i},s)=>({id:s,label:t,kind:n,language:r,mode:l,activeCues:o,cues:u,inBandMetadataTrackDispatchType:i}))}const lo={src:"",tracks:[]};function ao(e,t={}){e=d.toRef(e),t={...lo,...t};const{document:n=H}=t,r=a.ref(0),l=a.ref(0),o=a.ref(!1),u=a.ref(1),i=a.ref(!1),s=a.ref(!1),c=a.ref(!1),f=a.ref(1),v=a.ref(!1),y=a.ref([]),p=a.ref([]),m=a.ref(-1),S=a.ref(!1),w=a.ref(!1),g=n&&"pictureInPictureEnabled"in n,b=d.createEventHook(),E=O=>{_e(e,P=>{if(O){const I=typeof O=="number"?O:O.id;P.textTracks[I].mode="disabled"}else for(let I=0;I<P.textTracks.length;++I)P.textTracks[I].mode="disabled";m.value=-1})},R=(O,P=!0)=>{_e(e,I=>{const N=typeof O=="number"?O:O.id;P&&E(),I.textTracks[N].mode="showing",m.value=N})},k=()=>new Promise((O,P)=>{_e(e,async I=>{g&&(S.value?n.exitPictureInPicture().then(O).catch(P):I.requestPictureInPicture().then(O).catch(P))})});a.watchEffect(()=>{if(!n)return;const O=d.toValue(e);if(!O)return;const P=d.toValue(t.src);let I=[];P&&(typeof P=="string"?I=[{src:P}]:Array.isArray(P)?I=P:d.isObject(P)&&(I=[P]),O.querySelectorAll("source").forEach(N=>{N.removeEventListener("error",b.trigger),N.remove()}),I.forEach(({src:N,type:B})=>{const W=n.createElement("source");W.setAttribute("src",N),W.setAttribute("type",B||""),W.addEventListener("error",b.trigger),O.appendChild(W)}),O.load())}),d.tryOnScopeDispose(()=>{const O=d.toValue(e);O&&O.querySelectorAll("source").forEach(P=>P.removeEventListener("error",b.trigger))}),a.watch([e,u],()=>{const O=d.toValue(e);O&&(O.volume=u.value)}),a.watch([e,w],()=>{const O=d.toValue(e);O&&(O.muted=w.value)}),a.watch([e,f],()=>{const O=d.toValue(e);O&&(O.playbackRate=f.value)}),a.watchEffect(()=>{if(!n)return;const O=d.toValue(t.tracks),P=d.toValue(e);!O||!O.length||!P||(P.querySelectorAll("track").forEach(I=>I.remove()),O.forEach(({default:I,kind:N,label:B,src:W,srcLang:M},$)=>{const j=n.createElement("track");j.default=I||!1,j.kind=N,j.label=B,j.src=W,j.srclang=M,j.default&&(m.value=$),P.appendChild(j)}))});const{ignoreUpdates:V}=d.watchIgnorable(r,O=>{const P=d.toValue(e);P&&(P.currentTime=O)}),{ignoreUpdates:T}=d.watchIgnorable(c,O=>{const P=d.toValue(e);P&&(O?P.play():P.pause())});_(e,"timeupdate",()=>V(()=>r.value=d.toValue(e).currentTime)),_(e,"durationchange",()=>l.value=d.toValue(e).duration),_(e,"progress",()=>y.value=ro(d.toValue(e).buffered)),_(e,"seeking",()=>o.value=!0),_(e,"seeked",()=>o.value=!1),_(e,["waiting","loadstart"],()=>{i.value=!0,T(()=>c.value=!1)}),_(e,"loadeddata",()=>i.value=!1),_(e,"playing",()=>{i.value=!1,s.value=!1,T(()=>c.value=!0)}),_(e,"ratechange",()=>f.value=d.toValue(e).playbackRate),_(e,"stalled",()=>v.value=!0),_(e,"ended",()=>s.value=!0),_(e,"pause",()=>T(()=>c.value=!1)),_(e,"play",()=>T(()=>c.value=!0)),_(e,"enterpictureinpicture",()=>S.value=!0),_(e,"leavepictureinpicture",()=>S.value=!1),_(e,"volumechange",()=>{const O=d.toValue(e);O&&(u.value=O.volume,w.value=O.muted)});const C=[],F=a.watch([e],()=>{const O=d.toValue(e);O&&(F(),C[0]=_(O.textTracks,"addtrack",()=>p.value=Re(O.textTracks)),C[1]=_(O.textTracks,"removetrack",()=>p.value=Re(O.textTracks)),C[2]=_(O.textTracks,"change",()=>p.value=Re(O.textTracks)))});return d.tryOnScopeDispose(()=>C.forEach(O=>O())),{currentTime:r,duration:l,waiting:i,seeking:o,ended:s,stalled:v,buffered:y,playing:c,rate:f,volume:u,muted:w,tracks:p,selectedTrack:m,enableTrack:R,disableTrack:E,supportsPictureInPicture:g,togglePictureInPicture:k,isPictureInPicture:S,onSourceError:b.on}}function uo(){const e=a.shallowReactive({});return{get:t=>e[t],set:(t,n)=>a.set(e,t,n),has:t=>d.hasOwn(e,t),delete:t=>a.del(e,t),clear:()=>{Object.keys(e).forEach(t=>{a.del(e,t)})}}}function io(e,t){const r=t?.cache?a.shallowReactive(t.cache):a.isVue2?uo():a.shallowReactive(new Map),l=(...f)=>t?.getKey?t.getKey(...f):JSON.stringify(f),o=(f,...v)=>(r.set(f,e(...v)),r.get(f)),u=(...f)=>o(l(...f),...f),i=(...f)=>{r.delete(l(...f))},s=()=>{r.clear()},c=(...f)=>{const v=l(...f);return r.has(v)?r.get(v):o(v,...f)};return c.load=u,c.delete=i,c.clear=s,c.generateKey=l,c.cache=r,c}function so(e={}){const t=a.ref(),n=x(()=>typeof performance<"u"&&"memory"in performance);if(n.value){const{interval:r=1e3}=e;d.useIntervalFn(()=>{t.value=performance.memory},r,{immediate:e.immediate,immediateCallback:e.immediateCallback})}return{isSupported:n,memory:t}}const co={page:e=>[e.pageX,e.pageY],client:e=>[e.clientX,e.clientY],screen:e=>[e.screenX,e.screenY],movement:e=>e instanceof Touch?null:[e.movementX,e.movementY]};function De(e={}){const{type:t="page",touch:n=!0,resetOnTouchEnds:r=!1,initialValue:l={x:0,y:0},window:o=A,target:u=o,scroll:i=!0,eventFilter:s}=e;let c=null;const f=a.ref(l.x),v=a.ref(l.y),y=a.ref(null),p=typeof t=="function"?t:co[t],m=k=>{const V=p(k);c=k,V&&([f.value,v.value]=V,y.value="mouse")},S=k=>{if(k.touches.length>0){const V=p(k.touches[0]);V&&([f.value,v.value]=V,y.value="touch")}},w=()=>{if(!c||!o)return;const k=p(c);c instanceof MouseEvent&&k&&(f.value=k[0]+o.scrollX,v.value=k[1]+o.scrollY)},g=()=>{f.value=l.x,v.value=l.y},b=s?k=>s(()=>m(k),{}):k=>m(k),E=s?k=>s(()=>S(k),{}):k=>S(k),R=s?()=>s(()=>w(),{}):()=>w();if(u){const k={passive:!0};_(u,["mousemove","dragover"],b,k),n&&t!=="movement"&&(_(u,["touchstart","touchmove"],E,k),r&&_(u,"touchend",g,k)),i&&t==="page"&&_(o,"scroll",R,{passive:!0})}return{x:f,y:v,sourceType:y}}function et(e,t={}){const{handleOutside:n=!0,window:r=A}=t,l=t.type||"page",{x:o,y:u,sourceType:i}=De(t),s=a.ref(e??r?.document.body),c=a.ref(0),f=a.ref(0),v=a.ref(0),y=a.ref(0),p=a.ref(0),m=a.ref(0),S=a.ref(!0);let w=()=>{};return r&&(w=a.watch([s,o,u],()=>{const g=L(s);if(!g)return;const{left:b,top:E,width:R,height:k}=g.getBoundingClientRect();v.value=b+(l==="page"?r.pageXOffset:0),y.value=E+(l==="page"?r.pageYOffset:0),p.value=k,m.value=R;const V=o.value-v.value,T=u.value-y.value;S.value=R===0||k===0||V<0||T<0||V>R||T>k,(n||!S.value)&&(c.value=V,f.value=T)},{immediate:!0}),_(document,"mouseleave",()=>{S.value=!0})),{x:o,y:u,sourceType:i,elementX:c,elementY:f,elementPositionX:v,elementPositionY:y,elementHeight:p,elementWidth:m,isOutside:S,stop:w}}function fo(e={}){const{touch:t=!0,drag:n=!0,capture:r=!1,initialValue:l=!1,window:o=A}=e,u=a.ref(l),i=a.ref(null);if(!o)return{pressed:u,sourceType:i};const s=v=>()=>{u.value=!0,i.value=v},c=()=>{u.value=!1,i.value=null},f=a.computed(()=>L(e.target)||o);return _(f,"mousedown",s("mouse"),{passive:!0,capture:r}),_(o,"mouseleave",c,{passive:!0,capture:r}),_(o,"mouseup",c,{passive:!0,capture:r}),n&&(_(f,"dragstart",s("mouse"),{passive:!0,capture:r}),_(o,"drop",c,{passive:!0,capture:r}),_(o,"dragend",c,{passive:!0,capture:r})),t&&(_(f,"touchstart",s("touch"),{passive:!0,capture:r}),_(o,"touchend",c,{passive:!0,capture:r}),_(o,"touchcancel",c,{passive:!0,capture:r})),{pressed:u,sourceType:i}}function vo(e={}){const{window:t=A}=e,n=t?.navigator,r=x(()=>n&&"language"in n),l=a.ref(n?.language);return _(t,"languagechange",()=>{n&&(l.value=n.language)}),{isSupported:r,language:l}}function tt(e={}){const{window:t=A}=e,n=t?.navigator,r=x(()=>n&&"connection"in n),l=a.ref(!0),o=a.ref(!1),u=a.ref(void 0),i=a.ref(void 0),s=a.ref(void 0),c=a.ref(void 0),f=a.ref(void 0),v=a.ref(void 0),y=a.ref("unknown"),p=r.value&&n.connection;function m(){n&&(l.value=n.onLine,u.value=l.value?void 0:Date.now(),i.value=l.value?Date.now():void 0,p&&(s.value=p.downlink,c.value=p.downlinkMax,v.value=p.effectiveType,f.value=p.rtt,o.value=p.saveData,y.value=p.type))}return t&&(_(t,"offline",()=>{l.value=!1,u.value=Date.now()}),_(t,"online",()=>{l.value=!0,i.value=Date.now()})),p&&_(p,"change",m,!1),m(),{isSupported:r,isOnline:l,saveData:o,offlineAt:u,onlineAt:i,downlink:s,downlinkMax:c,effectiveType:v,rtt:f,type:y}}function nt(e={}){const{controls:t=!1,interval:n="requestAnimationFrame"}=e,r=a.ref(new Date),l=()=>r.value=new Date,o=n==="requestAnimationFrame"?Z(l,{immediate:!0}):d.useIntervalFn(l,n,{immediate:!0});return t?{now:r,...o}:r}function po(e){const t=a.ref(),n=()=>{t.value&&URL.revokeObjectURL(t.value),t.value=void 0};return a.watch(()=>d.toValue(e),r=>{n(),r&&(t.value=URL.createObjectURL(r))},{immediate:!0}),d.tryOnScopeDispose(n),a.readonly(t)}function ot(e,t,n){if(typeof e=="function"||a.isReadonly(e))return a.computed(()=>d.clamp(d.toValue(e),d.toValue(t),d.toValue(n)));const r=a.ref(e);return a.computed({get(){return r.value=d.clamp(r.value,d.toValue(t),d.toValue(n))},set(l){r.value=d.clamp(l,d.toValue(t),d.toValue(n))}})}function yo(e){const{total:t=Number.POSITIVE_INFINITY,pageSize:n=10,page:r=1,onPageChange:l=d.noop,onPageSizeChange:o=d.noop,onPageCountChange:u=d.noop}=e,i=ot(n,1,Number.POSITIVE_INFINITY),s=a.computed(()=>Math.max(1,Math.ceil(d.toValue(t)/d.toValue(i)))),c=ot(r,1,s),f=a.computed(()=>c.value===1),v=a.computed(()=>c.value===s.value);a.isRef(r)&&d.syncRef(r,c,{direction:a.isReadonly(r)?"ltr":"both"}),a.isRef(n)&&d.syncRef(n,i,{direction:a.isReadonly(n)?"ltr":"both"});function y(){c.value--}function p(){c.value++}const m={currentPage:c,currentPageSize:i,pageCount:s,isFirstPage:f,isLastPage:v,prev:y,next:p};return a.watch(c,()=>{l(a.reactive(m))}),a.watch(i,()=>{o(a.reactive(m))}),a.watch(s,()=>{u(a.reactive(m))}),m}function mo(e={}){const{isOnline:t}=tt(e);return t}function go(e={}){const{window:t=A}=e,n=a.ref(!1),r=l=>{if(!t)return;l=l||t.event;const o=l.relatedTarget||l.toElement;n.value=!o};return t&&(_(t,"mouseout",r,{passive:!0}),_(t.document,"mouseleave",r,{passive:!0}),_(t.document,"mouseenter",r,{passive:!0})),n}function rt(e={}){const{window:t=A}=e,n=x(()=>t&&"screen"in t&&"orientation"in t.screen),r=n.value?t.screen.orientation:{},l=a.ref(r.type),o=a.ref(r.angle||0);return n.value&&_(t,"orientationchange",()=>{l.value=r.type,o.value=r.angle}),{isSupported:n,orientation:l,angle:o,lockOrientation:s=>n.value&&typeof r.lock=="function"?r.lock(s):Promise.reject(new Error("Not supported")),unlockOrientation:()=>{n.value&&typeof r.unlock=="function"&&r.unlock()}}}function ho(e,t={}){const{deviceOrientationTiltAdjust:n=w=>w,deviceOrientationRollAdjust:r=w=>w,mouseTiltAdjust:l=w=>w,mouseRollAdjust:o=w=>w,window:u=A}=t,i=a.reactive(ze({window:u})),s=a.reactive(rt({window:u})),{elementX:c,elementY:f,elementWidth:v,elementHeight:y}=et(e,{handleOutside:!1,window:u}),p=a.computed(()=>i.isSupported&&(i.alpha!=null&&i.alpha!==0||i.gamma!=null&&i.gamma!==0)?"deviceOrientation":"mouse"),m=a.computed(()=>{if(p.value==="deviceOrientation"){let w;switch(s.orientation){case"landscape-primary":w=i.gamma/90;break;case"landscape-secondary":w=-i.gamma/90;break;case"portrait-primary":w=-i.beta/90;break;case"portrait-secondary":w=i.beta/90;break;default:w=-i.beta/90}return r(w)}else{const w=-(f.value-y.value/2)/y.value;return o(w)}}),S=a.computed(()=>{if(p.value==="deviceOrientation"){let w;switch(s.orientation){case"landscape-primary":w=i.beta/90;break;case"landscape-secondary":w=-i.beta/90;break;case"portrait-primary":w=i.gamma/90;break;case"portrait-secondary":w=-i.gamma/90;break;default:w=i.gamma/90}return n(w)}else{const w=(c.value-v.value/2)/v.value;return l(w)}});return{roll:m,tilt:S,source:p}}function wo(e=Ue()){const t=a.shallowRef(),n=()=>{const r=L(e);r&&(t.value=r.parentElement)};return d.tryOnMounted(n),a.watch(()=>d.toValue(e),n),t}function bo(e,t){const{window:n=A,immediate:r=!0,...l}=e,o=x(()=>n&&"PerformanceObserver"in n);let u;const i=()=>{u?.disconnect()},s=()=>{o.value&&(i(),u=new PerformanceObserver(t),u.observe(l))};return d.tryOnScopeDispose(i),r&&s(),{isSupported:o,start:s,stop:i}}const lt={x:0,y:0,pointerId:0,pressure:0,tiltX:0,tiltY:0,width:0,height:0,twist:0,pointerType:null},So=Object.keys(lt);function Eo(e={}){const{target:t=A}=e,n=a.ref(!1),r=a.ref(e.initialValue||{});Object.assign(r.value,lt,r.value);const l=o=>{n.value=!0,!(e.pointerTypes&&!e.pointerTypes.includes(o.pointerType))&&(r.value=d.objectPick(o,So,!1))};if(t){const o={passive:!0};_(t,["pointerdown","pointermove","pointerup"],l,o),_(t,"pointerleave",()=>n.value=!1,o)}return{...d.toRefs(r),isInside:n}}function To(e,t={}){const{document:n=H}=t,r=x(()=>n&&"pointerLockElement"in n),l=a.ref(),o=a.ref();let u;r.value&&(_(n,"pointerlockchange",()=>{var c;const f=(c=n.pointerLockElement)!=null?c:l.value;u&&f===u&&(l.value=n.pointerLockElement,l.value||(u=o.value=null))}),_(n,"pointerlockerror",()=>{var c;const f=(c=n.pointerLockElement)!=null?c:l.value;if(u&&f===u){const v=n.pointerLockElement?"release":"acquire";throw new Error(`Failed to ${v} pointer lock.`)}}));async function i(c){var f;if(!r.value)throw new Error("Pointer Lock API is not supported by your browser.");if(o.value=c instanceof Event?c.currentTarget:null,u=c instanceof Event?(f=L(e))!=null?f:o.value:L(c),!u)throw new Error("Target element undefined.");return u.requestPointerLock(),await d.until(l).toBe(u)}async function s(){return l.value?(n.exitPointerLock(),await d.until(l).toBeNull(),!0):!1}return{isSupported:r,element:l,triggerElement:o,lock:i,unlock:s}}function Oo(e,t={}){const n=d.toRef(e),{threshold:r=50,onSwipe:l,onSwipeEnd:o,onSwipeStart:u,disableTextSelect:i=!1}=t,s=a.reactive({x:0,y:0}),c=(T,C)=>{s.x=T,s.y=C},f=a.reactive({x:0,y:0}),v=(T,C)=>{f.x=T,f.y=C},y=a.computed(()=>s.x-f.x),p=a.computed(()=>s.y-f.y),{max:m,abs:S}=Math,w=a.computed(()=>m(S(y.value),S(p.value))>=r),g=a.ref(!1),b=a.ref(!1),E=a.computed(()=>w.value?S(y.value)>S(p.value)?y.value>0?"left":"right":p.value>0?"up":"down":"none"),R=T=>{var C,F,O;const P=T.buttons===0,I=T.buttons===1;return(O=(F=(C=t.pointerTypes)==null?void 0:C.includes(T.pointerType))!=null?F:P||I)!=null?O:!0},k=[_(e,"pointerdown",T=>{if(!R(T))return;b.value=!0;const C=T.target;C?.setPointerCapture(T.pointerId);const{clientX:F,clientY:O}=T;c(F,O),v(F,O),u?.(T)}),_(e,"pointermove",T=>{if(!R(T)||!b.value)return;const{clientX:C,clientY:F}=T;v(C,F),!g.value&&w.value&&(g.value=!0),g.value&&l?.(T)}),_(e,"pointerup",T=>{R(T)&&(g.value&&o?.(T,E.value),b.value=!1,g.value=!1)})];d.tryOnMounted(()=>{var T,C,F,O,P,I,N,B;(C=(T=n.value)==null?void 0:T.style)==null||C.setProperty("touch-action","none"),i&&((O=(F=n.value)==null?void 0:F.style)==null||O.setProperty("-webkit-user-select","none"),(I=(P=n.value)==null?void 0:P.style)==null||I.setProperty("-ms-user-select","none"),(B=(N=n.value)==null?void 0:N.style)==null||B.setProperty("user-select","none"))});const V=()=>k.forEach(T=>T());return{isSwiping:a.readonly(g),direction:a.readonly(E),posStart:a.readonly(s),posEnd:a.readonly(f),distanceX:y,distanceY:p,stop:V}}function ko(e){const t=G("(prefers-color-scheme: light)",e),n=G("(prefers-color-scheme: dark)",e);return a.computed(()=>n.value?"dark":t.value?"light":"no-preference")}function _o(e){const t=G("(prefers-contrast: more)",e),n=G("(prefers-contrast: less)",e),r=G("(prefers-contrast: custom)",e);return a.computed(()=>t.value?"more":n.value?"less":r.value?"custom":"no-preference")}function Ro(e={}){const{window:t=A}=e;if(!t)return a.ref(["en"]);const n=t.navigator,r=a.ref(n.languages);return _(t,"languagechange",()=>{r.value=n.languages}),r}function Fo(e){const t=G("(prefers-reduced-motion: reduce)",e);return a.computed(()=>t.value?"reduce":"no-preference")}function Po(e,t){const n=a.shallowRef(t);return a.watch(d.toRef(e),(r,l)=>{n.value=l},{flush:"sync"}),a.readonly(n)}const at="--vueuse-safe-area-top",ut="--vueuse-safe-area-right",it="--vueuse-safe-area-bottom",st="--vueuse-safe-area-left";function Co(){const e=a.ref(""),t=a.ref(""),n=a.ref(""),r=a.ref("");if(d.isClient){const o=le(at),u=le(ut),i=le(it),s=le(st);o.value="env(safe-area-inset-top, 0px)",u.value="env(safe-area-inset-right, 0px)",i.value="env(safe-area-inset-bottom, 0px)",s.value="env(safe-area-inset-left, 0px)",l(),_("resize",d.useDebounceFn(l))}function l(){e.value=we(at),t.value=we(ut),n.value=we(it),r.value=we(st)}return{top:e,right:t,bottom:n,left:r,update:l}}function we(e){return getComputedStyle(document.documentElement).getPropertyValue(e)}function Vo(e,t=d.noop,n={}){const{immediate:r=!0,manual:l=!1,type:o="text/javascript",async:u=!0,crossOrigin:i,referrerPolicy:s,noModule:c,defer:f,document:v=H,attrs:y={}}=n,p=a.ref(null);let m=null;const S=b=>new Promise((E,R)=>{const k=C=>(p.value=C,E(C),C);if(!v){E(!1);return}let V=!1,T=v.querySelector(`script[src="${d.toValue(e)}"]`);T?T.hasAttribute("data-loaded")&&k(T):(T=v.createElement("script"),T.type=o,T.async=u,T.src=d.toValue(e),f&&(T.defer=f),i&&(T.crossOrigin=i),c&&(T.noModule=c),s&&(T.referrerPolicy=s),Object.entries(y).forEach(([C,F])=>T?.setAttribute(C,F)),V=!0),T.addEventListener("error",C=>R(C)),T.addEventListener("abort",C=>R(C)),T.addEventListener("load",()=>{T.setAttribute("data-loaded","true"),t(T),k(T)}),V&&(T=v.head.appendChild(T)),b||k(T)}),w=(b=!0)=>(m||(m=S(b)),m),g=()=>{if(!v)return;m=null,p.value&&(p.value=null);const b=v.querySelector(`script[src="${d.toValue(e)}"]`);b&&v.head.removeChild(b)};return r&&!l&&d.tryOnMounted(w),l||d.tryOnUnmounted(g),{scriptTag:p,load:w,unload:g}}function ct(e){const t=window.getComputedStyle(e);if(t.overflowX==="scroll"||t.overflowY==="scroll"||t.overflowX==="auto"&&e.clientWidth<e.scrollWidth||t.overflowY==="auto"&&e.clientHeight<e.scrollHeight)return!0;{const n=e.parentNode;return!n||n.tagName==="BODY"?!1:ct(n)}}function Ao(e){const t=e||window.event,n=t.target;return ct(n)?!1:t.touches.length>1?!0:(t.preventDefault&&t.preventDefault(),!1)}const Fe=new WeakMap;function Io(e,t=!1){const n=a.ref(t);let r=null,l="";a.watch(d.toRef(e),i=>{const s=he(d.toValue(i));if(s){const c=s;if(Fe.get(c)||Fe.set(c,c.style.overflow),c.style.overflow!=="hidden"&&(l=c.style.overflow),c.style.overflow==="hidden")return n.value=!0;if(n.value)return c.style.overflow="hidden"}},{immediate:!0});const o=()=>{const i=he(d.toValue(e));!i||n.value||(d.isIOS&&(r=_(i,"touchmove",s=>{Ao(s)},{passive:!1})),i.style.overflow="hidden",n.value=!0)},u=()=>{const i=he(d.toValue(e));!i||!n.value||(d.isIOS&&r?.(),i.style.overflow=l,Fe.delete(i),n.value=!1)};return d.tryOnScopeDispose(u),a.computed({get(){return n.value},set(i){i?o():u()}})}function Mo(e,t,n={}){const{window:r=A}=n;return ye(e,t,r?.sessionStorage,n)}function Lo(e={},t={}){const{navigator:n=U}=t,r=n,l=x(()=>r&&"canShare"in r);return{isSupported:l,share:async(u={})=>{if(l.value){const i={...d.toValue(e),...d.toValue(u)};let s=!0;if(i.files&&r.canShare&&(s=r.canShare({files:i.files})),s)return r.share(i)}}}}const No=(e,t)=>e.sort(t),be=(e,t)=>e-t;function xo(...e){var t,n,r,l;const[o]=e;let u=be,i={};e.length===2?typeof e[1]=="object"?(i=e[1],u=(t=i.compareFn)!=null?t:be):u=(n=e[1])!=null?n:be:e.length>2&&(u=(r=e[1])!=null?r:be,i=(l=e[2])!=null?l:{});const{dirty:s=!1,sortFn:c=No}=i;return s?(a.watchEffect(()=>{const f=c(d.toValue(o),u);a.isRef(o)?o.value=f:o.splice(0,o.length,...f)}),o):a.computed(()=>c([...d.toValue(o)],u))}function Wo(e={}){const{interimResults:t=!0,continuous:n=!0,window:r=A}=e,l=d.toRef(e.lang||"en-US"),o=a.ref(!1),u=a.ref(!1),i=a.ref(""),s=a.shallowRef(void 0),c=(S=!o.value)=>{o.value=S},f=()=>{o.value=!0},v=()=>{o.value=!1},y=r&&(r.SpeechRecognition||r.webkitSpeechRecognition),p=x(()=>y);let m;return p.value&&(m=new y,m.continuous=n,m.interimResults=t,m.lang=d.toValue(l),m.onstart=()=>{u.value=!1},a.watch(l,S=>{m&&!o.value&&(m.lang=S)}),m.onresult=S=>{const w=S.results[S.resultIndex],{transcript:g}=w[0];u.value=w.isFinal,i.value=g,s.value=void 0},m.onerror=S=>{s.value=S},m.onend=()=>{o.value=!1,m.lang=d.toValue(l)},a.watch(o,()=>{o.value?m.start():m.stop()})),d.tryOnScopeDispose(()=>{o.value=!1}),{isSupported:p,isListening:o,isFinal:u,recognition:m,result:i,error:s,toggle:c,start:f,stop:v}}function $o(e,t={}){const{pitch:n=1,rate:r=1,volume:l=1,window:o=A}=t,u=o&&o.speechSynthesis,i=x(()=>u),s=a.ref(!1),c=a.ref("init"),f=d.toRef(e||""),v=d.toRef(t.lang||"en-US"),y=a.shallowRef(void 0),p=(b=!s.value)=>{s.value=b},m=b=>{b.lang=d.toValue(v),b.voice=d.toValue(t.voice)||null,b.pitch=d.toValue(n),b.rate=d.toValue(r),b.volume=l,b.onstart=()=>{s.value=!0,c.value="play"},b.onpause=()=>{s.value=!1,c.value="pause"},b.onresume=()=>{s.value=!0,c.value="play"},b.onend=()=>{s.value=!1,c.value="end"},b.onerror=E=>{y.value=E}},S=a.computed(()=>{s.value=!1,c.value="init";const b=new SpeechSynthesisUtterance(f.value);return m(b),b}),w=()=>{u.cancel(),S&&u.speak(S.value)},g=()=>{u.cancel(),s.value=!1};return i.value&&(m(S.value),a.watch(v,b=>{S.value&&!s.value&&(S.value.lang=b)}),t.voice&&a.watch(t.voice,()=>{u.cancel()}),a.watch(s,()=>{s.value?u.resume():u.pause()})),d.tryOnScopeDispose(()=>{s.value=!1}),{isSupported:i,isPlaying:s,status:c,utterance:S,error:y,stop:g,toggle:p,speak:w}}function Ho(e,t){const n=a.ref(e),r=a.computed(()=>Array.isArray(n.value)?n.value:Object.keys(n.value)),l=a.ref(r.value.indexOf(t??r.value[0])),o=a.computed(()=>f(l.value)),u=a.computed(()=>l.value===0),i=a.computed(()=>l.value===r.value.length-1),s=a.computed(()=>r.value[l.value+1]),c=a.computed(()=>r.value[l.value-1]);function f(k){return Array.isArray(n.value)?n.value[k]:n.value[r.value[k]]}function v(k){if(r.value.includes(k))return f(r.value.indexOf(k))}function y(k){r.value.includes(k)&&(l.value=r.value.indexOf(k))}function p(){i.value||l.value++}function m(){u.value||l.value--}function S(k){R(k)&&y(k)}function w(k){return r.value.indexOf(k)===l.value+1}function g(k){return r.value.indexOf(k)===l.value-1}function b(k){return r.value.indexOf(k)===l.value}function E(k){return l.value<r.value.indexOf(k)}function R(k){return l.value>r.value.indexOf(k)}return{steps:n,stepNames:r,index:l,current:o,next:s,previous:c,isFirst:u,isLast:i,at:f,get:v,goTo:y,goToNext:p,goToPrevious:m,goBackTo:S,isNext:w,isPrevious:g,isCurrent:b,isBefore:E,isAfter:R}}function Uo(e,t,n,r={}){var l;const{flush:o="pre",deep:u=!0,listenToStorageChanges:i=!0,writeDefaults:s=!0,mergeDefaults:c=!1,shallow:f,window:v=A,eventFilter:y,onError:p=E=>{console.error(E)}}=r,m=d.toValue(t),S=$e(m),w=(f?a.shallowRef:a.ref)(t),g=(l=r.serializer)!=null?l:Ee[S];if(!n)try{n=pe("getDefaultStorageAsync",()=>{var E;return(E=A)==null?void 0:E.localStorage})()}catch(E){p(E)}async function b(E){if(!(!n||E&&E.key!==e))try{const R=E?E.newValue:await n.getItem(e);if(R==null)w.value=m,s&&m!==null&&await n.setItem(e,await g.write(m));else if(c){const k=await g.read(R);typeof c=="function"?w.value=c(k,m):S==="object"&&!Array.isArray(k)?w.value={...m,...k}:w.value=k}else w.value=await g.read(R)}catch(R){p(R)}}return b(),v&&i&&_(v,"storage",E=>Promise.resolve().then(()=>b(E))),n&&d.watchWithFilter(w,async()=>{try{w.value==null?await n.removeItem(e):await n.setItem(e,await g.write(w.value))}catch(E){p(E)}},{flush:o,deep:u,eventFilter:y}),w}let Bo=0;function jo(e,t={}){const n=a.ref(!1),{document:r=H,immediate:l=!0,manual:o=!1,id:u=`vueuse_styletag_${++Bo}`}=t,i=a.ref(e);let s=()=>{};const c=()=>{if(!r)return;const v=r.getElementById(u)||r.createElement("style");v.isConnected||(v.id=u,t.media&&(v.media=t.media),r.head.appendChild(v)),!n.value&&(s=a.watch(i,y=>{v.textContent=y},{immediate:!0}),n.value=!0)},f=()=>{!r||!n.value||(s(),r.head.removeChild(r.getElementById(u)),n.value=!1)};return l&&!o&&d.tryOnMounted(c),o||d.tryOnScopeDispose(f),{id:u,css:i,unload:f,load:c,isLoaded:a.readonly(n)}}function zo(e,t={}){const{threshold:n=50,onSwipe:r,onSwipeEnd:l,onSwipeStart:o,passive:u=!0,window:i=A}=t,s=a.reactive({x:0,y:0}),c=a.reactive({x:0,y:0}),f=a.computed(()=>s.x-c.x),v=a.computed(()=>s.y-c.y),{max:y,abs:p}=Math,m=a.computed(()=>y(p(f.value),p(v.value))>=n),S=a.ref(!1),w=a.computed(()=>m.value?p(f.value)>p(v.value)?f.value>0?"left":"right":v.value>0?"up":"down":"none"),g=F=>[F.touches[0].clientX,F.touches[0].clientY],b=(F,O)=>{s.x=F,s.y=O},E=(F,O)=>{c.x=F,c.y=O};let R;const k=qo(i?.document);u?R=k?{passive:!0}:{capture:!1}:R=k?{passive:!1,capture:!0}:{capture:!0};const V=F=>{S.value&&l?.(F,w.value),S.value=!1},T=[_(e,"touchstart",F=>{if(F.touches.length!==1)return;R.capture&&!R.passive&&F.preventDefault();const[O,P]=g(F);b(O,P),E(O,P),o?.(F)},R),_(e,"touchmove",F=>{if(F.touches.length!==1)return;const[O,P]=g(F);E(O,P),!S.value&&m.value&&(S.value=!0),S.value&&r?.(F)},R),_(e,["touchend","touchcancel"],V,R)];return{isPassiveEventSupported:k,isSwiping:S,direction:w,coordsStart:s,coordsEnd:c,lengthX:f,lengthY:v,stop:()=>T.forEach(F=>F())}}function qo(e){if(!e)return!1;let t=!1;const n={get passive(){return t=!0,!1}};return e.addEventListener("x",d.noop,n),e.removeEventListener("x",d.noop),t}function Go(){const e=a.ref([]);return e.value.set=t=>{t&&e.value.push(t)},a.onBeforeUpdate(()=>{e.value.length=0}),e}function Yo(e={}){const{document:t=H,selector:n="html",observe:r=!1,initialValue:l="ltr"}=e;function o(){var i,s;return(s=(i=t?.querySelector(n))==null?void 0:i.getAttribute("dir"))!=null?s:l}const u=a.ref(o());return d.tryOnMounted(()=>u.value=o()),r&&t&&te(t.querySelector(n),()=>u.value=o(),{attributes:!0}),a.computed({get(){return u.value},set(i){var s,c;u.value=i,t&&(u.value?(s=t.querySelector(n))==null||s.setAttribute("dir",u.value):(c=t.querySelector(n))==null||c.removeAttribute("dir"))}})}function Xo(e){var t;const n=(t=e.rangeCount)!=null?t:0;return Array.from({length:n},(r,l)=>e.getRangeAt(l))}function Ko(e={}){const{window:t=A}=e,n=a.ref(null),r=a.computed(()=>{var i,s;return(s=(i=n.value)==null?void 0:i.toString())!=null?s:""}),l=a.computed(()=>n.value?Xo(n.value):[]),o=a.computed(()=>l.value.map(i=>i.getBoundingClientRect()));function u(){n.value=null,t&&(n.value=t.getSelection())}return t&&_(t.document,"selectionchange",u),{text:r,rects:o,ranges:l,selection:n}}function Jo(e){var t;const n=a.ref(e?.element),r=a.ref(e?.input),l=(t=e?.styleProp)!=null?t:"height",o=a.ref(1);function u(){var i;if(!n.value)return;let s="";n.value.style[l]="1px",o.value=(i=n.value)==null?void 0:i.scrollHeight,e?.styleTarget?d.toValue(e.styleTarget).style[l]=`${o.value}px`:s=`${o.value}px`,n.value.style[l]=s}return a.watch([r,n],()=>a.nextTick(u),{immediate:!0}),a.watch(o,()=>{var i;return(i=e?.onResize)==null?void 0:i.call(e)}),me(n,()=>u()),e?.watch&&a.watch(e.watch,u,{immediate:!0,deep:!0}),{textarea:n,input:r,triggerResize:u}}function Qo(e,t={}){const{throttle:n=200,trailing:r=!0}=t,l=d.throttleFilter(n,r);return{...ke(e,{...t,eventFilter:l})}}const Zo=[{max:6e4,value:1e3,name:"second"},{max:276e4,value:6e4,name:"minute"},{max:72e6,value:36e5,name:"hour"},{max:5184e5,value:864e5,name:"day"},{max:24192e5,value:6048e5,name:"week"},{max:28512e6,value:2592e6,name:"month"},{max:Number.POSITIVE_INFINITY,value:31536e6,name:"year"}],Do={justNow:"just now",past:e=>e.match(/\d/)?`${e} ago`:e,future:e=>e.match(/\d/)?`in ${e}`:e,month:(e,t)=>e===1?t?"last month":"next month":`${e} month${e>1?"s":""}`,year:(e,t)=>e===1?t?"last year":"next year":`${e} year${e>1?"s":""}`,day:(e,t)=>e===1?t?"yesterday":"tomorrow":`${e} day${e>1?"s":""}`,week:(e,t)=>e===1?t?"last week":"next week":`${e} week${e>1?"s":""}`,hour:e=>`${e} hour${e>1?"s":""}`,minute:e=>`${e} minute${e>1?"s":""}`,second:e=>`${e} second${e>1?"s":""}`,invalid:""};function er(e){return e.toISOString().slice(0,10)}function tr(e,t={}){const{controls:n=!1,updateInterval:r=3e4}=t,{now:l,...o}=nt({interval:r,controls:!0}),u=a.computed(()=>ft(new Date(d.toValue(e)),t,d.toValue(l)));return n?{timeAgo:u,...o}:u}function ft(e,t={},n=Date.now()){var r;const{max:l,messages:o=Do,fullDateFormatter:u=er,units:i=Zo,showSecond:s=!1,rounding:c="round"}=t,f=typeof c=="number"?w=>+w.toFixed(c):Math[c],v=+n-+e,y=Math.abs(v);function p(w,g){return f(Math.abs(w)/g.value)}function m(w,g){const b=p(w,g),E=w>0,R=S(g.name,b,E);return S(E?"past":"future",R,E)}function S(w,g,b){const E=o[w];return typeof E=="function"?E(g,b):E.replace("{0}",g.toString())}if(y<6e4&&!s)return o.justNow;if(typeof l=="number"&&y>l)return u(new Date(e));if(typeof l=="string"){const w=(r=i.find(g=>g.name===l))==null?void 0:r.max;if(w&&y>w)return u(new Date(e))}for(const[w,g]of i.entries()){if(p(v,g)<=0&&i[w-1])return m(v,i[w-1]);if(y<g.max)return m(v,g)}return o.invalid}function nr(e,t,n){const{start:r}=d.useTimeoutFn(o,t,{immediate:!1}),l=a.ref(!1);async function o(){l.value&&(await e(),r())}function u(){l.value||(l.value=!0,o())}function i(){l.value=!1}return n?.immediate&&u(),d.tryOnScopeDispose(i),{isActive:l,pause:i,resume:u}}function or(e={}){const{controls:t=!1,offset:n=0,immediate:r=!0,interval:l="requestAnimationFrame",callback:o}=e,u=a.ref(d.timestamp()+n),i=()=>u.value=d.timestamp()+n,s=o?()=>{i(),o(u.value)}:i,c=l==="requestAnimationFrame"?Z(s,{immediate:r}):d.useIntervalFn(s,l,{immediate:r});return t?{timestamp:u,...c}:u}function rr(e=null,t={}){var n,r,l;const{document:o=H,restoreOnUnmount:u=v=>v}=t,i=(n=o?.title)!=null?n:"",s=d.toRef((r=e??o?.title)!=null?r:null),c=e&&typeof e=="function";function f(v){if(!("titleTemplate"in t))return v;const y=t.titleTemplate||"%s";return typeof y=="function"?y(v):d.toValue(y).replace(/%s/g,v)}return a.watch(s,(v,y)=>{v!==y&&o&&(o.title=f(typeof v=="string"?v:""))},{immediate:!0}),t.observe&&!t.titleTemplate&&o&&!c&&te((l=o.head)==null?void 0:l.querySelector("title"),()=>{o&&o.title!==s.value&&(s.value=f(o.title))},{childList:!0}),d.tryOnBeforeUnmount(()=>{if(u){const v=u(i,s.value||"");v!=null&&o&&(o.title=v)}}),s}const lr={easeInSine:[.12,0,.39,0],easeOutSine:[.61,1,.88,1],easeInOutSine:[.37,0,.63,1],easeInQuad:[.11,0,.5,0],easeOutQuad:[.5,1,.89,1],easeInOutQuad:[.45,0,.55,1],easeInCubic:[.32,0,.67,0],easeOutCubic:[.33,1,.68,1],easeInOutCubic:[.65,0,.35,1],easeInQuart:[.5,0,.75,0],easeOutQuart:[.25,1,.5,1],easeInOutQuart:[.76,0,.24,1],easeInQuint:[.64,0,.78,0],easeOutQuint:[.22,1,.36,1],easeInOutQuint:[.83,0,.17,1],easeInExpo:[.7,0,.84,0],easeOutExpo:[.16,1,.3,1],easeInOutExpo:[.87,0,.13,1],easeInCirc:[.55,0,1,.45],easeOutCirc:[0,.55,.45,1],easeInOutCirc:[.85,0,.15,1],easeInBack:[.36,0,.66,-.56],easeOutBack:[.34,1.56,.64,1],easeInOutBack:[.68,-.6,.32,1.6]},ar=Object.assign({},{linear:d.identity},lr);function ur([e,t,n,r]){const l=(f,v)=>1-3*v+3*f,o=(f,v)=>3*v-6*f,u=f=>3*f,i=(f,v,y)=>((l(v,y)*f+o(v,y))*f+u(v))*f,s=(f,v,y)=>3*l(v,y)*f*f+2*o(v,y)*f+u(v),c=f=>{let v=f;for(let y=0;y<4;++y){const p=s(v,e,n);if(p===0)return v;const m=i(v,e,n)-f;v-=m/p}return v};return f=>e===t&&n===r?f:i(c(f),t,r)}function dt(e,t,n){return e+n*(t-e)}function Pe(e){return(typeof e=="number"?[e]:e)||[]}function vt(e,t,n,r={}){var l,o;const u=d.toValue(t),i=d.toValue(n),s=Pe(u),c=Pe(i),f=(l=d.toValue(r.duration))!=null?l:1e3,v=Date.now(),y=Date.now()+f,p=typeof r.transition=="function"?r.transition:(o=d.toValue(r.transition))!=null?o:d.identity,m=typeof p=="function"?p:ur(p);return new Promise(S=>{e.value=u;const w=()=>{var g;if((g=r.abort)!=null&&g.call(r)){S();return}const b=Date.now(),E=m((b-v)/f),R=Pe(e.value).map((k,V)=>dt(s[V],c[V],E));Array.isArray(e.value)?e.value=R.map((k,V)=>{var T,C;return dt((T=s[V])!=null?T:0,(C=c[V])!=null?C:0,E)}):typeof e.value=="number"&&(e.value=R[0]),b<y?requestAnimationFrame(w):(e.value=i,S())};w()})}function ir(e,t={}){let n=0;const r=()=>{const o=d.toValue(e);return typeof o=="number"?o:o.map(d.toValue)},l=a.ref(r());return a.watch(r,async o=>{var u,i;if(d.toValue(t.disabled))return;const s=++n;if(t.delay&&await d.promiseTimeout(d.toValue(t.delay)),s!==n)return;const c=Array.isArray(o)?o.map(d.toValue):d.toValue(o);(u=t.onStarted)==null||u.call(t),await vt(l,l.value,c,{...t,abort:()=>{var f;return s!==n||((f=t.abort)==null?void 0:f.call(t))}}),(i=t.onFinished)==null||i.call(t)},{deep:!0}),a.watch(()=>d.toValue(t.disabled),o=>{o&&(n++,l.value=r())}),d.tryOnScopeDispose(()=>{n++}),a.computed(()=>d.toValue(t.disabled)?r():l.value)}function sr(e="history",t={}){const{initialValue:n={},removeNullishValues:r=!0,removeFalsyValues:l=!1,write:o=!0,window:u=A}=t;if(!u)return a.reactive(n);const i=a.reactive({});function s(){if(e==="history")return u.location.search||"";if(e==="hash"){const g=u.location.hash||"",b=g.indexOf("?");return b>0?g.slice(b):""}else return(u.location.hash||"").replace(/^#/,"")}function c(g){const b=g.toString();if(e==="history")return`${b?`?${b}`:""}${u.location.hash||""}`;if(e==="hash-params")return`${u.location.search||""}${b?`#${b}`:""}`;const E=u.location.hash||"#",R=E.indexOf("?");return R>0?`${E.slice(0,R)}${b?`?${b}`:""}`:`${E}${b?`?${b}`:""}`}function f(){return new URLSearchParams(s())}function v(g){const b=new Set(Object.keys(i));for(const E of g.keys()){const R=g.getAll(E);i[E]=R.length>1?R:g.get(E)||"",b.delete(E)}Array.from(b).forEach(E=>delete i[E])}const{pause:y,resume:p}=d.pausableWatch(i,()=>{const g=new URLSearchParams("");Object.keys(i).forEach(b=>{const E=i[b];Array.isArray(E)?E.forEach(R=>g.append(b,R)):r&&E==null||l&&!E?g.delete(b):g.set(b,E)}),m(g)},{deep:!0});function m(g,b){y(),b&&v(g),u.history.replaceState(u.history.state,u.document.title,u.location.pathname+c(g)),p()}function S(){o&&m(f(),!0)}_(u,"popstate",S,!1),e!=="history"&&_(u,"hashchange",S,!1);const w=f();return w.keys().next().value?v(w):Object.assign(i,n),i}function cr(e={}){var t,n;const r=a.ref((t=e.enabled)!=null?t:!1),l=a.ref((n=e.autoSwitch)!=null?n:!0),o=a.ref(e.constraints),{navigator:u=U}=e,i=x(()=>{var S;return(S=u?.mediaDevices)==null?void 0:S.getUserMedia}),s=a.shallowRef();function c(S){switch(S){case"video":{if(o.value)return o.value.video||!1;break}case"audio":{if(o.value)return o.value.audio||!1;break}}}async function f(){if(!(!i.value||s.value))return s.value=await u.mediaDevices.getUserMedia({video:c("video"),audio:c("audio")}),s.value}function v(){var S;(S=s.value)==null||S.getTracks().forEach(w=>w.stop()),s.value=void 0}function y(){v(),r.value=!1}async function p(){return await f(),s.value&&(r.value=!0),s.value}async function m(){return v(),await p()}return a.watch(r,S=>{S?f():v()},{immediate:!0}),a.watch(o,()=>{l.value&&s.value&&m()},{immediate:!0}),d.tryOnScopeDispose(()=>{y()}),{isSupported:i,stream:s,start:p,stop:y,restart:m,constraints:o,enabled:r,autoSwitch:l}}function pt(e,t,n,r={}){var l,o,u,i,s;const{clone:c=!1,passive:f=!1,eventName:v,deep:y=!1,defaultValue:p,shouldEmit:m}=r,S=a.getCurrentInstance(),w=n||S?.emit||((l=S?.$emit)==null?void 0:l.bind(S))||((u=(o=S?.proxy)==null?void 0:o.$emit)==null?void 0:u.bind(S?.proxy));let g=v;if(!t)if(a.isVue2){const k=(s=(i=S?.proxy)==null?void 0:i.$options)==null?void 0:s.model;t=k?.value||"value",v||(g=k?.event||"input")}else t="modelValue";g=g||`update:${t.toString()}`;const b=k=>c?typeof c=="function"?c(k):re(k):k,E=()=>d.isDef(e[t])?b(e[t]):p,R=k=>{m?m(k)&&w(g,k):w(g,k)};if(f){const k=E(),V=a.ref(k);let T=!1;return a.watch(()=>e[t],C=>{T||(T=!0,V.value=b(C),a.nextTick(()=>T=!1))}),a.watch(V,C=>{!T&&(C!==e[t]||y)&&R(C)},{deep:y}),V}else return a.computed({get(){return E()},set(k){R(k)}})}function fr(e,t,n={}){const r={};for(const l in e)r[l]=pt(e,l,t,n);return r}function dr(e){const{pattern:t=[],interval:n=0,navigator:r=U}=e||{},l=x(()=>typeof r<"u"&&"vibrate"in r),o=d.toRef(t);let u;const i=(c=o.value)=>{l.value&&r.vibrate(c)},s=()=>{l.value&&r.vibrate(0),u?.pause()};return n>0&&(u=d.useIntervalFn(i,n,{immediate:!1,immediateCallback:!1})),{isSupported:l,pattern:t,intervalControls:u,vibrate:i,stop:s}}function vr(e,t){const{containerStyle:n,wrapperProps:r,scrollTo:l,calculateRange:o,currentList:u,containerRef:i}="itemHeight"in t?mr(t,e):yr(t,e);return{list:u,scrollTo:l,containerProps:{ref:i,onScroll:()=>{o()},style:n},wrapperProps:r}}function yt(e){const t=a.ref(null),n=qe(t),r=a.ref([]),l=a.shallowRef(e);return{state:a.ref({start:0,end:10}),source:l,currentList:r,size:n,containerRef:t}}function mt(e,t,n){return r=>{if(typeof n=="number")return Math.ceil(r/n);const{start:l=0}=e.value;let o=0,u=0;for(let i=l;i<t.value.length;i++){const s=n(i);if(o+=s,u=i,o>r)break}return u-l}}function gt(e,t){return n=>{if(typeof t=="number")return Math.floor(n/t)+1;let r=0,l=0;for(let o=0;o<e.value.length;o++){const u=t(o);if(r+=u,r>=n){l=o;break}}return l+1}}function ht(e,t,n,r,{containerRef:l,state:o,currentList:u,source:i}){return()=>{const s=l.value;if(s){const c=n(e==="vertical"?s.scrollTop:s.scrollLeft),f=r(e==="vertical"?s.clientHeight:s.clientWidth),v=c-t,y=c+f+t;o.value={start:v<0?0:v,end:y>i.value.length?i.value.length:y},u.value=i.value.slice(o.value.start,o.value.end).map((p,m)=>({data:p,index:m+o.value.start}))}}}function wt(e,t){return n=>typeof e=="number"?n*e:t.value.slice(0,n).reduce((l,o,u)=>l+e(u),0)}function bt(e,t,n,r){a.watch([e.width,e.height,t,n],()=>{r()})}function St(e,t){return a.computed(()=>typeof e=="number"?t.value.length*e:t.value.reduce((n,r,l)=>n+e(l),0))}const pr={horizontal:"scrollLeft",vertical:"scrollTop"};function Et(e,t,n,r){return l=>{r.value&&(r.value[pr[e]]=n(l),t())}}function yr(e,t){const n=yt(t),{state:r,source:l,currentList:o,size:u,containerRef:i}=n,s={overflowX:"auto"},{itemWidth:c,overscan:f=5}=e,v=mt(r,l,c),y=gt(l,c),p=ht("horizontal",f,y,v,n),m=wt(c,l),S=a.computed(()=>m(r.value.start)),w=St(c,l);bt(u,t,i,p);const g=Et("horizontal",p,m,i),b=a.computed(()=>({style:{height:"100%",width:`${w.value-S.value}px`,marginLeft:`${S.value}px`,display:"flex"}}));return{scrollTo:g,calculateRange:p,wrapperProps:b,containerStyle:s,currentList:o,containerRef:i}}function mr(e,t){const n=yt(t),{state:r,source:l,currentList:o,size:u,containerRef:i}=n,s={overflowY:"auto"},{itemHeight:c,overscan:f=5}=e,v=mt(r,l,c),y=gt(l,c),p=ht("vertical",f,y,v,n),m=wt(c,l),S=a.computed(()=>m(r.value.start)),w=St(c,l);bt(u,t,i,p);const g=Et("vertical",p,m,i),b=a.computed(()=>({style:{width:"100%",height:`${w.value-S.value}px`,marginTop:`${S.value}px`}}));return{calculateRange:p,scrollTo:g,containerStyle:s,wrapperProps:b,currentList:o,containerRef:i}}function gr(e={}){const{navigator:t=U,document:n=H}=e;let r;const l=x(()=>t&&"wakeLock"in t),o=a.ref(!1);async function u(){!l.value||!r||(n&&n.visibilityState==="visible"&&(r=await t.wakeLock.request("screen")),o.value=!r.released)}n&&_(n,"visibilitychange",u,{passive:!0});async function i(c){l.value&&(r=await t.wakeLock.request(c),o.value=!r.released)}async function s(){!l.value||!r||(await r.release(),o.value=!r.released,r=null)}return{isSupported:l,isActive:o,request:i,release:s}}function hr(e={}){const{window:t=A,requestPermissions:n=!0}=e,r=e,l=x(()=>{if(!t||!("Notification"in t))return!1;try{new Notification("")}catch{return!1}return!0}),o=a.ref(l.value&&"permission"in Notification&&Notification.permission==="granted"),u=a.ref(null),i=async()=>{if(l.value)return!o.value&&Notification.permission!=="denied"&&await Notification.requestPermission()==="granted"&&(o.value=!0),o.value},{on:s,trigger:c}=d.createEventHook(),{on:f,trigger:v}=d.createEventHook(),{on:y,trigger:p}=d.createEventHook(),{on:m,trigger:S}=d.createEventHook(),w=async b=>{if(!l.value||!o.value)return;const E=Object.assign({},r,b);return u.value=new Notification(E.title||"",E),u.value.onclick=c,u.value.onshow=v,u.value.onerror=p,u.value.onclose=S,u.value},g=()=>{u.value&&u.value.close(),u.value=null};if(n&&d.tryOnMounted(i),d.tryOnScopeDispose(g),l.value&&t){const b=t.document;_(b,"visibilitychange",E=>{E.preventDefault(),b.visibilityState==="visible"&&g()})}return{isSupported:l,notification:u,ensurePermissions:i,permissionGranted:o,show:w,close:g,onClick:s,onShow:f,onError:y,onClose:m}}const Tt="ping";function Ce(e){return e===!0?{}:e}function wr(e,t={}){const{onConnected:n,onDisconnected:r,onError:l,onMessage:o,immediate:u=!0,autoClose:i=!0,protocols:s=[]}=t,c=a.ref(null),f=a.ref("CLOSED"),v=a.ref(),y=d.toRef(e);let p,m,S=!1,w=0,g=[],b;const E=()=>{if(g.length&&v.value&&f.value==="OPEN"){for(const F of g)v.value.send(F);g=[]}},R=()=>{clearTimeout(b),b=void 0},k=(F=1e3,O)=>{!d.isClient||!v.value||(S=!0,R(),p?.(),v.value.close(F,O),v.value=void 0)},V=(F,O=!0)=>!v.value||f.value!=="OPEN"?(O&&g.push(F),!1):(E(),v.value.send(F),!0),T=()=>{if(S||typeof y.value>"u")return;const F=new WebSocket(y.value,s);v.value=F,f.value="CONNECTING",F.onopen=()=>{f.value="OPEN",n?.(F),m?.(),E()},F.onclose=O=>{if(f.value="CLOSED",r?.(F,O),!S&&t.autoReconnect){const{retries:P=-1,delay:I=1e3,onFailed:N}=Ce(t.autoReconnect);w+=1,typeof P=="number"&&(P<0||w<P)||typeof P=="function"&&P()?setTimeout(T,I):N?.()}},F.onerror=O=>{l?.(F,O)},F.onmessage=O=>{if(t.heartbeat){R();const{message:P=Tt}=Ce(t.heartbeat);if(O.data===P)return}c.value=O.data,o?.(F,O)}};if(t.heartbeat){const{message:F=Tt,interval:O=1e3,pongTimeout:P=1e3}=Ce(t.heartbeat),{pause:I,resume:N}=d.useIntervalFn(()=>{V(F,!1),b==null&&(b=setTimeout(()=>{k(),S=!1},P))},O,{immediate:!1});p=I,m=N}i&&(d.isClient&&_("beforeunload",()=>k()),d.tryOnScopeDispose(k));const C=()=>{!d.isClient&&!d.isWorker||(k(),S=!1,w=0,T())};return u&&C(),a.watch(y,C),{data:c,status:f,close:k,send:V,open:C,ws:v}}function br(e,t,n){const{window:r=A}=n??{},l=a.ref(null),o=a.shallowRef(),u=(...s)=>{o.value&&o.value.postMessage(...s)},i=function(){o.value&&o.value.terminate()};return r&&(typeof e=="string"?o.value=new Worker(e,t):typeof e=="function"?o.value=e():o.value=e,o.value.onmessage=s=>{l.value=s.data},d.tryOnScopeDispose(()=>{o.value&&o.value.terminate()})),{data:l,post:u,terminate:i,worker:o}}function Sr(e){return t=>{const n=t.data[0];return Promise.resolve(e.apply(void 0,n)).then(r=>{postMessage(["SUCCESS",r])}).catch(r=>{postMessage(["ERROR",r])})}}function Er(e,t){if(e.length===0&&t.length===0)return"";const n=e.map(o=>`'${o}'`).toString(),r=t.filter(o=>typeof o=="function").map(o=>{const u=o.toString();return u.trim().startsWith("function")?u:`const ${o.name} = ${u}`}).join(";"),l=`importScripts(${n});`;return`${n.trim()===""?"":l} ${r}`}function Tr(e,t,n){const r=`${Er(t,n)}; onmessage=(${Sr})(${e})`,l=new Blob([r],{type:"text/javascript"});return URL.createObjectURL(l)}function Or(e,t={}){const{dependencies:n=[],localDependencies:r=[],timeout:l,window:o=A}=t,u=a.ref(),i=a.ref("PENDING"),s=a.ref({}),c=a.ref(),f=(m="PENDING")=>{u.value&&u.value._url&&o&&(u.value.terminate(),URL.revokeObjectURL(u.value._url),s.value={},u.value=void 0,o.clearTimeout(c.value),i.value=m)};f(),d.tryOnScopeDispose(f);const v=()=>{const m=Tr(e,n,r),S=new Worker(m);return S._url=m,S.onmessage=w=>{const{resolve:g=()=>{},reject:b=()=>{}}=s.value,[E,R]=w.data;switch(E){case"SUCCESS":g(R),f(E);break;default:b(R),f("ERROR");break}},S.onerror=w=>{const{reject:g=()=>{}}=s.value;w.preventDefault(),g(w),f("ERROR")},l&&(c.value=setTimeout(()=>f("TIMEOUT_EXPIRED"),l)),S},y=(...m)=>new Promise((S,w)=>{s.value={resolve:S,reject:w},u.value&&u.value.postMessage([[...m]]),i.value="RUNNING"});return{workerFn:(...m)=>i.value==="RUNNING"?(console.error("[useWebWorkerFn] You can only run one instance of the worker at a time."),Promise.reject()):(u.value=v(),y(...m)),workerStatus:i,workerTerminate:f}}function kr(e={}){const{window:t=A}=e;if(!t)return a.ref(!1);const n=a.ref(t.document.hasFocus());return _(t,"blur",()=>{n.value=!1}),_(t,"focus",()=>{n.value=!0}),n}function _r(e={}){const{window:t=A,behavior:n="auto"}=e;if(!t)return{x:a.ref(0),y:a.ref(0)};const r=a.ref(t.scrollX),l=a.ref(t.scrollY),o=a.computed({get(){return r.value},set(i){scrollTo({left:i,behavior:n})}}),u=a.computed({get(){return l.value},set(i){scrollTo({top:i,behavior:n})}});return _(t,"scroll",()=>{r.value=t.scrollX,l.value=t.scrollY},{capture:!1,passive:!0}),{x:o,y:u}}function Rr(e={}){const{window:t=A,initialWidth:n=Number.POSITIVE_INFINITY,initialHeight:r=Number.POSITIVE_INFINITY,listenOrientation:l=!0,includeScrollbar:o=!0}=e,u=a.ref(n),i=a.ref(r),s=()=>{t&&(o?(u.value=t.innerWidth,i.value=t.innerHeight):(u.value=t.document.documentElement.clientWidth,i.value=t.document.documentElement.clientHeight))};if(s(),d.tryOnMounted(s),_("resize",s,{passive:!0}),l){const c=G("(orientation: portrait)");a.watch(c,()=>s())}return{width:u,height:i}}h.DefaultMagicKeysAliasMap=Ze,h.StorageSerializers=Ee,h.TransitionPresets=ar,h.asyncComputed=Y,h.breakpointsAntDesign=Jt,h.breakpointsBootstrapV5=Yt,h.breakpointsMasterCss=Dt,h.breakpointsPrimeFlex=en,h.breakpointsQuasar=Qt,h.breakpointsSematic=Zt,h.breakpointsTailwind=Gt,h.breakpointsVuetify=Kt,h.breakpointsVuetifyV2=Ne,h.breakpointsVuetifyV3=Xt,h.cloneFnJSON=re,h.computedAsync=Y,h.computedInject=Ve,h.createFetch=Nn,h.createReusableTemplate=q,h.createTemplatePromise=J,h.createUnrefFn=K,h.customStorageEventName=Te,h.defaultDocument=H,h.defaultLocation=kt,h.defaultNavigator=U,h.defaultWindow=A,h.executeTransition=vt,h.formatTimeAgo=ft,h.getSSRHandler=pe,h.mapGamepadToXbox360Controller=qn,h.onClickOutside=_t,h.onKeyDown=Ft,h.onKeyPressed=Pt,h.onKeyStroke=ce,h.onKeyUp=Ct,h.onLongPress=It,h.onStartTyping=Nt,h.setSSRHandler=cn,h.templateRef=xt,h.unrefElement=L,h.useActiveElement=Me,h.useAnimate=Wt,h.useAsyncQueue=$t,h.useAsyncState=Le,h.useBase64=Bt,h.useBattery=zt,h.useBluetooth=qt,h.useBreakpoints=tn,h.useBroadcastChannel=nn,h.useBrowserLocation=on,h.useCached=rn,h.useClipboard=ln,h.useClipboardItems=an,h.useCloned=un,h.useColorMode=He,h.useConfirmDialog=fn,h.useCssVar=le,h.useCurrentElement=Ue,h.useCycleList=dn,h.useDark=vn,h.useDebouncedRefHistory=gn,h.useDeviceMotion=hn,h.useDeviceOrientation=ze,h.useDevicePixelRatio=wn,h.useDevicesList=bn,h.useDisplayMedia=Sn,h.useDocumentVisibility=En,h.useDraggable=Tn,h.useDropZone=On,h.useElementBounding=kn,h.useElementByPoint=_n,h.useElementHover=Rn,h.useElementSize=qe,h.useElementVisibility=Ye,h.useEventBus=Fn,h.useEventListener=_,h.useEventSource=Cn,h.useEyeDropper=Vn,h.useFavicon=An,h.useFetch=Xe,h.useFileDialog=$n,h.useFileSystemAccess=Hn,h.useFocus=Un,h.useFocusWithin=Bn,h.useFps=jn,h.useFullscreen=zn,h.useGamepad=Gn,h.useGeolocation=Yn,h.useIdle=Jn,h.useImage=Zn,h.useInfiniteScroll=Dn,h.useIntersectionObserver=Ge,h.useKeyModifier=to,h.useLocalStorage=no,h.useMagicKeys=oo,h.useManualRefHistory=je,h.useMediaControls=ao,h.useMediaQuery=G,h.useMemoize=io,h.useMemory=so,h.useMounted=Ie,h.useMouse=De,h.useMouseInElement=et,h.useMousePressed=fo,h.useMutationObserver=te,h.useNavigatorLanguage=vo,h.useNetwork=tt,h.useNow=nt,h.useObjectUrl=po,h.useOffsetPagination=yo,h.useOnline=mo,h.usePageLeave=go,h.useParallax=ho,h.useParentElement=wo,h.usePerformanceObserver=bo,h.usePermission=fe,h.usePointer=Eo,h.usePointerLock=To,h.usePointerSwipe=Oo,h.usePreferredColorScheme=ko,h.usePreferredContrast=_o,h.usePreferredDark=Oe,h.usePreferredLanguages=Ro,h.usePreferredReducedMotion=Fo,h.usePrevious=Po,h.useRafFn=Z,h.useRefHistory=ke,h.useResizeObserver=me,h.useScreenOrientation=rt,h.useScreenSafeArea=Co,h.useScriptTag=Vo,h.useScroll=Qe,h.useScrollLock=Io,h.useSessionStorage=Mo,h.useShare=Lo,h.useSorted=xo,h.useSpeechRecognition=Wo,h.useSpeechSynthesis=$o,h.useStepper=Ho,h.useStorage=ye,h.useStorageAsync=Uo,h.useStyleTag=jo,h.useSupported=x,h.useSwipe=zo,h.useTemplateRefsList=Go,h.useTextDirection=Yo,h.useTextSelection=Ko,h.useTextareaAutosize=Jo,h.useThrottledRefHistory=Qo,h.useTimeAgo=tr,h.useTimeoutPoll=nr,h.useTimestamp=or,h.useTitle=rr,h.useTransition=ir,h.useUrlSearchParams=sr,h.useUserMedia=cr,h.useVModel=pt,h.useVModels=fr,h.useVibrate=dr,h.useVirtualList=vr,h.useWakeLock=gr,h.useWebNotification=hr,h.useWebSocket=wr,h.useWebWorker=br,h.useWebWorkerFn=Or,h.useWindowFocus=kr,h.useWindowScroll=_r,h.useWindowSize=Rr,Object.keys(d).forEach(function(e){e!=="default"&&!Object.prototype.hasOwnProperty.call(h,e)&&Object.defineProperty(h,e,{enumerable:!0,get:function(){return d[e]}})})})(this.VueUse=this.VueUse||{},VueUse,VueDemi);

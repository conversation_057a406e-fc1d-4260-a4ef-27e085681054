"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.default=exports.Cell=void 0;var _vue=require("vue"),_xeUtils=_interopRequireDefault(require("xe-utils")),_conf=_interopRequireDefault(require("../../v-x-e-table/src/conf")),_vXETable=require("../../v-x-e-table"),_utils=require("../../tools/utils"),_dom=require("../../tools/dom"),_util=require("./util"),_vn=require("../../tools/vn");function _interopRequireDefault(e){return e&&e.__esModule?e:{default:e}}function renderTitlePrefixIcon(l){const{$table:t,column:e}=l,r=e.titlePrefix||e.titleHelp;return r?[(0,_vue.h)("i",{class:["vxe-cell-title-prefix-icon",r.icon||_conf.default.icon.TABLE_TITLE_PREFIX],onMouseenter(e){t.triggerHeaderTitleEvent(e,r,l)},onMouseleave(e){t.handleTargetLeaveEvent(e)}})]:[]}function renderTitleSuffixIcon(l){const{$table:t,column:e}=l,r=e.titleSuffix;return r?[(0,_vue.h)("i",{class:["vxe-cell-title-suffix-icon",r.icon||_conf.default.icon.TABLE_TITLE_SUFFIX],onMouseenter(e){t.triggerHeaderTitleEvent(e,r,l)},onMouseleave(e){t.handleTargetLeaveEvent(e)}})]:[]}function renderTitleContent(l,e){const{$table:t,column:r}=l,{props:n,reactData:a}=t;var o=t.getComputeMaps()["computeTooltipOpts"],d=n["showHeaderOverflow"],{type:i,showHeaderOverflow:c}=r;const s=o.value.showAll;o=_xeUtils.default.isUndefined(c)||_xeUtils.default.isNull(c)?d:c;const u="title"===o,C=!0===o||"tooltip"===o;d={};return(u||C||s)&&(d.onMouseenter=e=>{a._isResize||(u?(0,_dom.updateCellTitle)(e.currentTarget,r):(C||s)&&t.triggerHeaderTooltipEvent(e,l))}),(C||s)&&(d.onMouseleave=e=>{a._isResize||(C||s)&&t.handleTargetLeaveEvent(e)}),["html"===i&&_xeUtils.default.isString(e)?(0,_vue.h)("span",Object.assign({class:"vxe-cell--title",innerHTML:e},d)):(0,_vue.h)("span",Object.assign({class:"vxe-cell--title"},d),(0,_vn.getSlotVNs)(e))]}function getFooterContent(e){var{$table:l,column:t,_columnIndex:r,items:n,row:a}=e,{slots:o,editRender:d,cellRender:i}=t,d=d||i,i=o?o.footer:null;if(i)return l.callSlot(i,e);if(d){o=_vXETable.VXETable.renderer.get(d.name);if(o&&o.renderFooter)return(0,_vn.getSlotVNs)(o.renderFooter(d,e))}return _xeUtils.default.isArray(n)?[(0,_utils.formatText)(n[r],1)]:[(0,_utils.formatText)(_xeUtils.default.get(a,t.field),1)]}function getDefaultCellLabel(e){var{$table:e,row:l,column:t}=e;return(0,_utils.formatText)(e.getCellLabel(l,t),1)}const Cell=exports.Cell={createColumn(e,l){var{type:t,sortable:r,filters:n,editRender:a,treeNode:o}=l,d=e["props"],i=d["editConfig"],{computeEditOpts:d,computeCheckboxOpts:c}=e.getComputeMaps(),s=c.value,u=d.value,C={renderHeader:Cell.renderDefaultHeader,renderCell:o?Cell.renderTreeCell:Cell.renderDefaultCell,renderFooter:Cell.renderDefaultFooter};switch(t){case"seq":C.renderHeader=Cell.renderSeqHeader,C.renderCell=o?Cell.renderTreeIndexCell:Cell.renderSeqCell;break;case"radio":C.renderHeader=Cell.renderRadioHeader,C.renderCell=o?Cell.renderTreeRadioCell:Cell.renderRadioCell;break;case"checkbox":C.renderHeader=Cell.renderCheckboxHeader,C.renderCell=s.checkField?o?Cell.renderTreeSelectionCellByProp:Cell.renderCheckboxCellByProp:o?Cell.renderTreeSelectionCell:Cell.renderCheckboxCell;break;case"expand":C.renderCell=Cell.renderExpandCell,C.renderData=Cell.renderExpandData;break;case"html":C.renderCell=o?Cell.renderTreeHTMLCell:Cell.renderHTMLCell,n&&r?C.renderHeader=Cell.renderSortAndFilterHeader:r?C.renderHeader=Cell.renderSortHeader:n&&(C.renderHeader=Cell.renderFilterHeader);break;default:i&&a?(C.renderHeader=Cell.renderEditHeader,C.renderCell="cell"===u.mode?o?Cell.renderTreeCellEdit:Cell.renderCellEdit:o?Cell.renderTreeRowEdit:Cell.renderRowEdit):n&&r?C.renderHeader=Cell.renderSortAndFilterHeader:r?C.renderHeader=Cell.renderSortHeader:n&&(C.renderHeader=Cell.renderFilterHeader)}return(0,_util.createColumn)(e,l,C)},renderHeaderTitle(e){var{$table:l,column:t}=e,{slots:r,editRender:n,cellRender:a}=t,n=n||a,a=r?r.header:null;if(a)return renderTitleContent(e,l.callSlot(a,e));if(n){r=_vXETable.VXETable.renderer.get(n.name);if(r&&r.renderHeader)return renderTitleContent(e,(0,_vn.getSlotVNs)(r.renderHeader(n,e)))}return renderTitleContent(e,(0,_utils.formatText)(t.getTitle(),1))},renderDefaultHeader(e){return renderTitlePrefixIcon(e).concat(Cell.renderHeaderTitle(e)).concat(renderTitleSuffixIcon(e))},renderDefaultCell(e){var{$table:l,row:t,column:r}=e,{slots:n,editRender:a,cellRender:o}=r,o=a||o,n=n?n.default:null;if(n)return l.callSlot(n,e);if(o){var n=a?"renderCell":"renderDefault",d=_vXETable.VXETable.renderer.get(o.name),d=d?d[n]:null;if(d)return(0,_vn.getSlotVNs)(d(o,Object.assign({$type:a?"edit":"cell"},e)))}n=l.getCellLabel(t,r),d=a?a.placeholder:"";return[(0,_vue.h)("span",{class:"vxe-cell--label"},a&&(0,_utils.eqEmptyValue)(n)?[(0,_vue.h)("span",{class:"vxe-cell--placeholder"},(0,_utils.formatText)((0,_utils.getFuncText)(d),1))]:(0,_utils.formatText)(n,1))]},renderTreeCell(e){return Cell.renderTreeIcon(e,Cell.renderDefaultCell(e))},renderDefaultFooter(e){return[(0,_vue.h)("span",{class:"vxe-cell--item"},getFooterContent(e))]},renderTreeIcon(l,e){const{$table:t,isHidden:r}=l;var n=t["reactData"],a=t.getComputeMaps()["computeTreeOpts"],{treeExpandedMaps:n,treeExpandLazyLoadedMaps:o}=n,a=a.value,{row:d,column:i,level:c}=l,i=i["slots"],{indent:s,lazy:u,trigger:C,iconLoaded:v,showIcon:_,iconOpen:p,iconClose:f}=a,E=a.children||a.childrenField,a=a.hasChild||a.hasChildField,E=d[E],i=i?i.icon:null;let b=!1,h=!1,x=!1;var T={};return i?t.callSlot(i,l):(r||(i=(0,_util.getRowid)(t,d),h=!!n[i],u&&(x=!!o[i],b=d[a])),C&&"default"!==C||(T.onClick=e=>{e.stopPropagation(),t.triggerTreeExpandEvent(e,l)}),[(0,_vue.h)("div",{class:["vxe-cell--tree-node",{"is--active":h}],style:{paddingLeft:c*s+"px"}},[_&&(E&&E.length||b)?[(0,_vue.h)("div",Object.assign({class:"vxe-tree--btn-wrapper"},T),[(0,_vue.h)("i",{class:["vxe-tree--node-btn",x?v||_conf.default.icon.TABLE_TREE_LOADED:h?p||_conf.default.icon.TABLE_TREE_OPEN:f||_conf.default.icon.TABLE_TREE_CLOSE]})])]:null,(0,_vue.h)("div",{class:"vxe-tree-cell"},e)])])},renderSeqHeader(e){var{$table:l,column:t}=e,r=t["slots"],r=r?r.header:null;return renderTitleContent(e,r?l.callSlot(r,e):(0,_utils.formatText)(t.getTitle(),1))},renderSeqCell(e){var{$table:l,column:t}=e,r=l["props"],r=r["treeConfig"],n=l.getComputeMaps()["computeSeqOpts"],n=n.value,t=t["slots"],t=t?t.default:null;return t?l.callSlot(t,e):(l=e["seq"],t=n.seqMethod,[(0,_utils.formatText)(t?t(e):r?l:(n.startIndex||0)+l,1)])},renderTreeIndexCell(e){return Cell.renderTreeIcon(e,Cell.renderSeqCell(e))},renderRadioHeader(e){var{$table:l,column:t}=e,r=t["slots"],n=r?r.header:null,r=r?r.title:null;return renderTitleContent(e,n?l.callSlot(n,e):[(0,_vue.h)("span",{class:"vxe-radio--label"},r?l.callSlot(r,e):(0,_utils.formatText)(t.getTitle(),1))])},renderRadioCell(l){const{$table:t,column:e,isHidden:r}=l;var n=t["reactData"],a=t.getComputeMaps()["computeRadioOpts"],n=n["selectRadioRow"],o=e["slots"],{labelField:a,checkMethod:d,visibleMethod:i}=a.value,c=l["row"],s=o?o.default:null,o=o?o.radio:null,n=t.eqRow(c,n);const u=!i||i({row:c});let C=!!d,v;r||(v={onClick(e){!C&&u&&(e.stopPropagation(),t.triggerRadioRowEvent(e,l))}},d&&(C=!d({row:c})));i=Object.assign(Object.assign({},l),{checked:n,disabled:C,visible:u});return o?t.callSlot(o,i):(d=[],u&&d.push((0,_vue.h)("span",{class:["vxe-radio--icon",n?_conf.default.icon.TABLE_RADIO_CHECKED:_conf.default.icon.TABLE_RADIO_UNCHECKED]})),(s||a)&&d.push((0,_vue.h)("span",{class:"vxe-radio--label"},s?t.callSlot(s,i):_xeUtils.default.get(c,a))),[(0,_vue.h)("span",Object.assign({class:["vxe-cell--radio",{"is--checked":n,"is--disabled":C}]},v),d)])},renderTreeRadioCell(e){return Cell.renderTreeIcon(e,Cell.renderRadioCell(e))},renderCheckboxHeader(e){const{$table:l,column:t,isHidden:r}=e;var n=l["reactData"],{computeIsAllCheckboxDisabled:a,computeCheckboxOpts:o}=l.getComputeMaps();const{isAllSelected:d,isIndeterminate:i}=n,c=a.value;var n=t["slots"],a=n?n.header:null,n=n?n.title:null,o=o.value,s=t.getTitle();let u;r||(u={onClick(e){c||(e.stopPropagation(),l.triggerCheckAllEvent(e,!d))}});e=Object.assign(Object.assign({},e),{checked:d,disabled:c,indeterminate:i});return a?renderTitleContent(e,l.callSlot(a,e)):(o.checkStrictly?o.showHeader:!1!==o.showHeader)?renderTitleContent(e,[(0,_vue.h)("span",Object.assign({class:["vxe-cell--checkbox",{"is--checked":d,"is--disabled":c,"is--indeterminate":i}],title:_conf.default.i18n("vxe.table.allTitle")},u),[(0,_vue.h)("span",{class:["vxe-checkbox--icon",i?_conf.default.icon.TABLE_CHECKBOX_INDETERMINATE:d?_conf.default.icon.TABLE_CHECKBOX_CHECKED:_conf.default.icon.TABLE_CHECKBOX_UNCHECKED]})].concat(n||s?[(0,_vue.h)("span",{class:"vxe-checkbox--label"},n?l.callSlot(n,e):s)]:[]))]):renderTitleContent(e,[(0,_vue.h)("span",{class:"vxe-checkbox--label"},n?l.callSlot(n,e):s)])},renderCheckboxCell(l){const{$table:t,row:e,column:r,isHidden:n}=l;var{props:a,reactData:o}=t,a=a["treeConfig"],{selectCheckboxMaps:o,treeIndeterminateMaps:d}=o,i=t.getComputeMaps()["computeCheckboxOpts"],{labelField:i,checkMethod:c,visibleMethod:s}=i.value,u=r["slots"],C=u?u.default:null,u=u?u.checkbox:null;let v=!1,_=!1;const p=!s||s({row:e});let f=!!c,E;n||(s=(0,_util.getRowid)(t,e),_=!!o[s],E={onClick(e){!f&&p&&(e.stopPropagation(),t.triggerCheckRowEvent(e,l,!_))}},c&&(f=!c({row:e})),a&&(v=!!d[s]));o=Object.assign(Object.assign({},l),{checked:_,disabled:f,visible:p,indeterminate:v});return u?t.callSlot(u,o):(c=[],p&&c.push((0,_vue.h)("span",{class:["vxe-checkbox--icon",v?_conf.default.icon.TABLE_CHECKBOX_INDETERMINATE:_?_conf.default.icon.TABLE_CHECKBOX_CHECKED:_conf.default.icon.TABLE_CHECKBOX_UNCHECKED]})),(C||i)&&c.push((0,_vue.h)("span",{class:"vxe-checkbox--label"},C?t.callSlot(C,o):_xeUtils.default.get(e,i))),[(0,_vue.h)("span",Object.assign({class:["vxe-cell--checkbox",{"is--checked":_,"is--disabled":f,"is--indeterminate":v,"is--hidden":!p}]},E),c)])},renderTreeSelectionCell(e){return Cell.renderTreeIcon(e,Cell.renderCheckboxCell(e))},renderCheckboxCellByProp(l){const{$table:t,row:e,column:r,isHidden:n}=l;var{props:a,reactData:o}=t,a=a["treeConfig"],o=o["treeIndeterminateMaps"],d=t.getComputeMaps()["computeCheckboxOpts"],d=d.value,{labelField:i,checkField:c,checkMethod:s,visibleMethod:u}=d,d=d.indeterminateField||d.halfField,C=r["slots"],v=C?C.default:null,C=C?C.checkbox:null;let _=!1,p=!1;const f=!u||u({row:e});let E=!!s,b;n||(u=(0,_util.getRowid)(t,e),p=_xeUtils.default.get(e,c),b={onClick(e){!E&&f&&(e.stopPropagation(),t.triggerCheckRowEvent(e,l,!p))}},s&&(E=!s({row:e})),a&&(_=!!o[u]));c=Object.assign(Object.assign({},l),{checked:p,disabled:E,visible:f,indeterminate:_});return C?t.callSlot(C,c):(s=[],f&&(s.push((0,_vue.h)("span",{class:["vxe-checkbox--icon",_?_conf.default.icon.TABLE_CHECKBOX_INDETERMINATE:p?_conf.default.icon.TABLE_CHECKBOX_CHECKED:_conf.default.icon.TABLE_CHECKBOX_UNCHECKED]})),v||i)&&s.push((0,_vue.h)("span",{class:"vxe-checkbox--label"},v?t.callSlot(v,c):_xeUtils.default.get(e,i))),[(0,_vue.h)("span",Object.assign({class:["vxe-cell--checkbox",{"is--checked":p,"is--disabled":E,"is--indeterminate":d&&!p?e[d]:_,"is--hidden":!f}]},b),s)])},renderTreeSelectionCellByProp(e){return Cell.renderTreeIcon(e,Cell.renderCheckboxCellByProp(e))},renderExpandCell(l){const{$table:t,isHidden:e,row:r,column:n}=l;var a=t["reactData"],{rowExpandedMaps:a,rowExpandLazyLoadedMaps:o}=a,d=t.getComputeMaps()["computeExpandOpts"],{lazy:d,labelField:i,iconLoaded:c,showIcon:s,iconOpen:u,iconClose:C,visibleMethod:v}=d.value,_=n["slots"],p=_?_.default:null,_=_?_.icon:null;let f=!1,E=!1;return _?t.callSlot(_,l):(e||(_=(0,_util.getRowid)(t,r),f=!!a[_],d&&(E=!!o[_])),[!s||v&&!v(l)?null:(0,_vue.h)("span",{class:["vxe-table--expanded",{"is--active":f}],onClick(e){e.stopPropagation(),t.triggerRowExpandEvent(e,l)}},[(0,_vue.h)("i",{class:["vxe-table--expand-btn",E?c||_conf.default.icon.TABLE_EXPAND_LOADED:f?u||_conf.default.icon.TABLE_EXPAND_OPEN:C||_conf.default.icon.TABLE_EXPAND_CLOSE]})]),p||i?(0,_vue.h)("span",{class:"vxe-table--expand-label"},p?t.callSlot(p,l):_xeUtils.default.get(r,i)):null])},renderExpandData(e){var{$table:l,column:t}=e,{slots:t,contentRender:r}=t,t=t?t.content:null;if(t)return l.callSlot(t,e);if(r){l=_vXETable.VXETable.renderer.get(r.name);if(l&&l.renderExpand)return(0,_vn.getSlotVNs)(l.renderExpand(r,e))}return[]},renderHTMLCell(e){var{$table:l,column:t}=e,t=t["slots"],t=t?t.default:null;return t?l.callSlot(t,e):[(0,_vue.h)("span",{class:"vxe-cell--html",innerHTML:getDefaultCellLabel(e)})]},renderTreeHTMLCell(e){return Cell.renderTreeIcon(e,Cell.renderHTMLCell(e))},renderSortAndFilterHeader(e){return Cell.renderDefaultHeader(e).concat(Cell.renderSortIcon(e)).concat(Cell.renderFilterIcon(e))},renderSortHeader(e){return Cell.renderDefaultHeader(e).concat(Cell.renderSortIcon(e))},renderSortIcon(e){const{$table:l,column:t}=e;var e=l.getComputeMaps()["computeSortOpts"],{showIcon:e,iconLayout:r,iconAsc:n,iconDesc:a}=e.value,o=t["order"];return e?[(0,_vue.h)("span",{class:["vxe-cell--sort",`vxe-cell--sort-${r}-layout`]},[(0,_vue.h)("i",{class:["vxe-sort--asc-btn",n||_conf.default.icon.TABLE_SORT_ASC,{"sort--active":"asc"===o}],title:_conf.default.i18n("vxe.table.sortAsc"),onClick(e){e.stopPropagation(),l.triggerSortEvent(e,t,"asc")}}),(0,_vue.h)("i",{class:["vxe-sort--desc-btn",a||_conf.default.icon.TABLE_SORT_DESC,{"sort--active":"desc"===o}],title:_conf.default.i18n("vxe.table.sortDesc"),onClick(e){e.stopPropagation(),l.triggerSortEvent(e,t,"desc")}})])]:[]},renderFilterHeader(e){return Cell.renderDefaultHeader(e).concat(Cell.renderFilterIcon(e))},renderFilterIcon(l){const{$table:t,column:e,hasFilter:r}=l;var n=t["reactData"],n=n["filterStore"],a=t.getComputeMaps()["computeFilterOpts"],{showIcon:a,iconNone:o,iconMatch:d}=a.value;return a?[(0,_vue.h)("span",{class:["vxe-cell--filter",{"is--active":n.visible&&n.column===e}]},[(0,_vue.h)("i",{class:["vxe-filter--btn",r?d||_conf.default.icon.TABLE_FILTER_MATCH:o||_conf.default.icon.TABLE_FILTER_NONE],title:_conf.default.i18n("vxe.table.filter"),onClick(e){t.triggerFilterEvent&&t.triggerFilterEvent(e,l.column,l)}})])]:[]},renderEditHeader(e){var{$table:l,column:t}=e,r=l["props"],l=l.getComputeMaps()["computeEditOpts"],{editConfig:r,editRules:n}=r,l=l.value,{sortable:a,filters:o,editRender:d}=t;let i=!1;return n&&(n=_xeUtils.default.get(n,t.field))&&(i=n.some(e=>e.required)),((0,_utils.isEnableConf)(r)?[i&&l.showAsterisk?(0,_vue.h)("i",{class:"vxe-cell--required-icon"}):null,(0,_utils.isEnableConf)(d)&&l.showIcon?(0,_vue.h)("i",{class:["vxe-cell--edit-icon",l.icon||_conf.default.icon.TABLE_EDIT]}):null]:[]).concat(Cell.renderDefaultHeader(e)).concat(a?Cell.renderSortIcon(e):[]).concat(o?Cell.renderFilterIcon(e):[])},renderRowEdit(e){var{$table:l,column:t}=e,l=l["reactData"],l=l["editStore"],l=l["actived"],t=t["editRender"];return Cell.runRenderer(e,(0,_utils.isEnableConf)(t)&&l&&l.row===e.row)},renderTreeRowEdit(e){return Cell.renderTreeIcon(e,Cell.renderRowEdit(e))},renderCellEdit(e){var{$table:l,column:t}=e,l=l["reactData"],l=l["editStore"],l=l["actived"],t=t["editRender"];return Cell.runRenderer(e,(0,_utils.isEnableConf)(t)&&l&&l.row===e.row&&l.column===e.column)},renderTreeCellEdit(e){return Cell.renderTreeIcon(e,Cell.renderCellEdit(e))},runRenderer(e,l){var{$table:t,column:r}=e,{slots:r,editRender:n,formatter:a}=r,o=r?r.default:null,r=r?r.edit:null,d=_vXETable.VXETable.renderer.get(n.name);return l?r?t.callSlot(r,e):d&&d.renderEdit?(0,_vn.getSlotVNs)(d.renderEdit(n,Object.assign({$type:"edit"},e))):[]:o?t.callSlot(o,e):a?[(0,_vue.h)("span",{class:"vxe-cell--label"},getDefaultCellLabel(e))]:Cell.renderDefaultCell(e)}};var _default=exports.default=Cell;
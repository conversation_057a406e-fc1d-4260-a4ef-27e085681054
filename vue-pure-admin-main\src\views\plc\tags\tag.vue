<template>
  <div class="tag-container">
    <!-- 頁面標題 -->
    <div class="page-header">
      <h2>測點管理</h2>
      <p>管理 PLC 系統的測點，包含測點列表和測點分類</p>
    </div>

    <!-- 功能標籤頁 -->
    <el-card class="tag-card">
      <el-tabs v-model="activeTab" type="border-card">
        <!-- 測點列表 -->
        <el-tab-pane label="測點列表" name="list">
          <div class="tab-content">
            <el-card shadow="never">
              <template #header>
                <div class="card-header">
                  <span>測點列表</span>
                  <div>
                    <el-button type="primary" @click="addNewTag">
                      新增測點
                    </el-button>
                  </div>
                </div>
              </template>

              <!-- 搜尋和篩選 -->
              <div class="filter-section">
                <el-row :gutter="20">
                  <el-col :span="6">
                    <el-input
                      v-model="searchKeyword"
                      placeholder="搜尋測點名稱..."
                      clearable
                      @input="handleSearch"
                    >
                      <template #prefix>
                        <el-icon><Search /></el-icon>
                      </template>
                    </el-input>
                  </el-col>
                  <el-col :span="4">
                    <el-select
                      v-model="statusFilter"
                      placeholder="狀態篩選"
                      clearable
                      @change="handleFilter"
                    >
                      <el-option label="全部" value="" />
                      <el-option label="啟用" value="active" />
                      <el-option label="停用" value="inactive" />
                    </el-select>
                  </el-col>
                  <el-col :span="4">
                    <el-select
                      v-model="typeFilter"
                      placeholder="類型篩選"
                      clearable
                      @change="handleFilter"
                    >
                      <el-option label="全部" value="" />
                      <el-option label="類比" value="Analog" />
                      <el-option label="數位" value="Digital" />
                      <el-option label="計算" value="Calculated" />
                    </el-select>
                  </el-col>
                  <el-col :span="4">
                    <el-select
                      v-model="deviceFilter"
                      placeholder="裝置篩選"
                      clearable
                      @change="handleFilter"
                    >
                      <el-option label="全部" value="" />
                      <el-option
                        v-for="device in deviceOptions"
                        :key="device.id"
                        :label="device.name"
                        :value="device.id"
                      />
                    </el-select>
                  </el-col>
                  <el-col :span="6">
                    <el-button type="info" @click="refreshTagList">
                      <el-icon><Refresh /></el-icon>
                      刷新
                    </el-button>
                    <el-button type="success" @click="exportTags">
                      <el-icon><Download /></el-icon>
                      匯出
                    </el-button>
                  </el-col>
                </el-row>
              </div>

              <!-- 測點表格 -->
              <el-table
                v-loading="tagLoading"
                :data="filteredTagList"
                stripe
                border
                height="500"
              >
                <el-table-column prop="name" label="測點名稱" width="150" fixed="left">
                  <template #default="{ row }">
                    <div class="tag-name">
                      <el-icon class="tag-icon">
                        <Cpu />
                      </el-icon>
                      {{ row.name }}
                    </div>
                  </template>
                </el-table-column>

                <el-table-column prop="description" label="描述" width="200" />

                <el-table-column prop="tagType" label="測點類型" width="100">
                  <template #default="{ row }">
                    <el-tag :type="getTagTypeColor(row.tagType)">
                      {{ getTagTypeText(row.tagType) }}
                    </el-tag>
                  </template>
                </el-table-column>

                <el-table-column prop="usage" label="測點用途" width="100">
                  <template #default="{ row }">
                    <!-- 與舊系統一致：有值時顯示標籤，沒有值時顯示空白 -->
                    <el-tag v-if="row.usage && row.usage !== ''" :type="getUsageTypeColor(row.usage)">
                      {{ getTagUsageName(row.usage) }}
                    </el-tag>
                    <span v-else>-</span>
                  </template>
                </el-table-column>

                <el-table-column prop="contactType" label="接點種類" width="100">
                  <template #default="{ row }">
                    <!-- 與舊系統一致：有值時顯示標籤，沒有值時顯示空白 -->
                    <el-tag v-if="row.contactType && row.contactType !== ''" :type="getContactTypeColor(row.contactType)">
                      {{ getContactTypeName(row.contactType) }}
                    </el-tag>
                    <span v-else>-</span>
                  </template>
                </el-table-column>

                <el-table-column prop="dataType" label="數據類型" width="100">
                  <template #default="{ row }">
                    <el-tag type="info">{{ row.dataType }}</el-tag>
                  </template>
                </el-table-column>

                <el-table-column prop="address" label="地址" width="120" />

                <el-table-column prop="deviceName" label="裝置" width="200">
                  <template #default="{ row }">
                    <el-tooltip :content="row.deviceName" placement="top">
                      <span class="device-name">{{ row.deviceName || '-' }}</span>
                    </el-tooltip>
                  </template>
                </el-table-column>

                <el-table-column prop="locationName" label="地區" width="150">
                  <template #default="{ row }">
                    <el-tooltip :content="row.locationName" placement="top">
                      <span class="location-name">{{ row.locationName || '-' }}</span>
                    </el-tooltip>
                  </template>
                </el-table-column>

                <el-table-column prop="cctvList" label="CCTV" width="100">
                  <template #default="{ row }">
                    <el-tag v-if="row.cctvList && row.cctvList.length > 0" type="success" size="small">
                      {{ row.cctvList.length }} 個
                    </el-tag>
                    <span v-else>-</span>
                  </template>
                </el-table-column>

                <el-table-column prop="unit" label="單位" width="80">
                  <template #default="{ row }">
                    {{ getUnitName(row.unit) || '-' }}
                  </template>
                </el-table-column>

                <el-table-column prop="currentValue" label="當前值" width="100" align="right">
                  <template #default="{ row }">
                    <span :class="getValueClass(row)">
                      {{ formatValue(row.currentValue, row.dataType) }}
                    </span>
                  </template>
                </el-table-column>

                <el-table-column prop="alarmEnabled" label="警報" width="80" align="center">
                  <template #default="{ row }">
                    <el-tag :type="row.alarmEnabled ? 'warning' : 'info'" size="small">
                      {{ row.alarmEnabled ? '啟用' : '停用' }}
                    </el-tag>
                  </template>
                </el-table-column>

                <el-table-column prop="status" label="狀態" width="80">
                  <template #default="{ row }">
                    <el-tag :type="row.status === 'active' ? 'success' : 'danger'" size="small">
                      {{ row.status === 'active' ? '啟用' : '停用' }}
                    </el-tag>
                  </template>
                </el-table-column>

                <el-table-column prop="updateTime" label="更新時間" width="180">
                  <template #default="{ row }">
                    {{ formatDateTime(row.updateTime) }}
                  </template>
                </el-table-column>

                <el-table-column label="操作" width="200" fixed="right">
                  <template #default="{ row }">
                    <el-button
                      type="primary"
                      size="small"
                      @click="editTag(row)"
                    >
                      編輯
                    </el-button>
                    <el-button
                      type="warning"
                      size="small"
                      @click="toggleTagStatus(row)"
                    >
                      {{ row.status === 'active' ? '停用' : '啟用' }}
                    </el-button>
                    <el-button
                      type="danger"
                      size="small"
                      @click="deleteTag(row)"
                    >
                      刪除
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>

              <!-- 分頁 -->
              <div class="pagination-container">
                <el-pagination
                  v-model:current-page="currentPage"
                  v-model:page-size="pageSize"
                  :total="totalCount"
                  :page-sizes="[10, 20, 50, 100]"
                  layout="total, sizes, prev, pager, next, jumper"
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                />
              </div>
            </el-card>
          </div>
        </el-tab-pane>

        <!-- 測點分類 -->
        <el-tab-pane label="測點分類" name="class">
          <div class="tab-content">
            <el-card shadow="never">
              <template #header>
                <div class="card-header">
                  <span>測點分類</span>
                </div>
              </template>

              <!-- 新增分類按鈕區域 -->
              <div class="class-actions" style="margin-bottom: 16px;">
                <el-button type="primary" @click="addClass">
                  新增分類
                </el-button>
              </div>

              <!-- 分類表格 -->
              <el-table
                v-loading="classLoading"
                :data="flattenedClassList"
                stripe
                border
                height="500"
              >
                <el-table-column prop="name" label="分類名稱" width="250" fixed="left">
                  <template #default="{ row }">
                    <div class="class-name" :style="{ paddingLeft: (row.level * 20) + 'px' }">
                      <!-- 展開/收合圖標 -->
                      <el-icon
                        v-if="row.hasChildren"
                        class="expand-icon"
                        @click="toggleCategoryExpand(row.id)"
                        :class="{ 'expanded': row.isExpanded }"
                      >
                        <ArrowRight />
                      </el-icon>
                      <span v-else class="no-children-spacer"></span>

                      <!-- 分類圖標 -->
                      <el-icon class="class-icon">
                        <Folder />
                      </el-icon>

                      <!-- 分類名稱 -->
                      <span :style="{ color: getClassLevelColor(row.level) }">
                        {{ row.name }}
                      </span>

                      <!-- 子分類數量提示 -->
                      <el-tag v-if="row.hasChildren" type="info" size="small" class="children-count">
                        {{ row.childrenCount }}個
                      </el-tag>
                    </div>
                  </template>
                </el-table-column>

                <el-table-column prop="level" label="層級" width="80" align="center">
                  <template #default="{ row }">
                    <el-tag :type="getClassLevelTagType(row.level)" size="small">
                      第{{ row.level + 1 }}層
                    </el-tag>
                  </template>
                </el-table-column>



                <el-table-column prop="parentName" label="上級分類" width="150">
                  <template #default="{ row }">
                    <span v-if="row.parentName" class="parent-name">{{ row.parentName }}</span>
                    <el-tag v-else type="info" size="small">根分類</el-tag>
                  </template>
                </el-table-column>

                <el-table-column prop="childrenCount" label="子分類數量" width="120" align="center">
                  <template #default="{ row }">
                    <el-tag v-if="row.childrenCount > 0" type="success" size="small">
                      {{ row.childrenCount }} 個
                    </el-tag>
                    <span v-else>-</span>
                  </template>
                </el-table-column>

                <el-table-column prop="status" label="狀態" width="80" align="center">
                  <template #default="{ row }">
                    <el-tag :type="row.status === 'active' ? 'success' : 'warning'" size="small">
                      {{ row.status === 'active' ? '啟用' : '停用' }}
                    </el-tag>
                  </template>
                </el-table-column>

                <el-table-column label="操作" width="250" fixed="right">
                  <template #default="{ row }">
                    <el-button
                      type="primary"
                      size="small"
                      @click="editClass(row.originalData)"
                    >
                      編輯
                    </el-button>
                    <el-button
                      type="success"
                      size="small"
                      @click="addSubClass(row.originalData)"
                    >
                      新增子分類
                    </el-button>
                    <el-button
                      type="danger"
                      size="small"
                      @click="deleteClass(row.originalData)"
                    >
                      刪除
                    </el-button>
                  </template>
                </el-table-column>
              </el-table>
            </el-card>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 新增/編輯測點對話框 -->
    <el-dialog
      v-model="showTagDialog"
      :title="tagForm.id ? '編輯測點' : '新增測點'"
      width="900px"
      :close-on-click-modal="false"
    >
      <el-tabs v-model="tagDialogTab" type="border-card">
        <!-- 基礎設定 -->
        <el-tab-pane label="基礎設定" name="basic">
          <el-form
            ref="tagFormRef"
            :model="tagForm"
            :rules="tagRules"
            label-width="120px"
          >
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="測點狀態">
                  <el-radio-group v-model="tagForm.status" :model-value="tagForm.status || false">
                    <el-radio :value="true">啟用</el-radio>
                    <el-radio :value="false">停用</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="地區" prop="regionId">
                  <el-select 
                    v-model="tagForm.regionId" 
                    placeholder="請選擇地區" 
                    style="width: 100%"
                  >
                    <el-option
                      v-for="region in regionList"
                      :key="region.Id"
                      :label="region.Name"
                      :value="region.Id"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="裝置" prop="deviceId">
                  <el-select 
                    v-model="tagForm.deviceId" 
                    placeholder="請選擇裝置" 
                    style="width: 100%"
                  >
                    <el-option
                      v-for="device in deviceOptions"
                      :key="device.id"
                      :label="device.name"
                      :value="device.id"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="CCTV">
                  <el-input v-model="tagForm.cctv" placeholder="包含 0 個CCTV" readonly>
                    <template #append>
                      <el-button type="primary" link>選擇CCTV</el-button>
                    </template>
                  </el-input>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="測點名稱" prop="name">
                  <el-input v-model="tagForm.name" placeholder="請輸入測點名稱" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="說明" prop="description">
                  <el-input v-model="tagForm.description" placeholder="請輸入說明" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="PLC位址" prop="address">
                  <el-input v-model="tagForm.address" placeholder="PLC位址(base1)" />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="測點分類">
                  <el-tree-select
                    v-model="tagForm.tagCategoryIds"
                    :data="tagClassTree"
                    :props="treeProps"
                    placeholder="請選擇"
                    style="width: 100%"
                    multiple
                    clearable
                  />
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item label="測量單位" prop="unit">
                  <el-select
                    v-model="tagForm.unit"
                    placeholder="請選擇單位"
                    style="width: 100%"
                    @change="onUnitChange"
                  >
                    <el-option
                      v-for="unit in unitList"
                      :key="unit.Id"
                      :label="unit.Name"
                      :value="unit.Id"
                    />
                  </el-select>
                  <!-- 調試信息 -->
                  <div style="font-size: 12px; color: #999; margin-top: 4px;">
                    調試: tagForm.unit = {{ tagForm.unit }} ({{ typeof tagForm.unit }})
                    <br>
                    單位列表數量: {{ unitList.length }}
                    <br>
                    匹配的單位: {{ unitList.find(u => u.Id == tagForm.unit)?.Name || '未找到' }}
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="資料型別" prop="dataType">
                  <el-select v-model="tagForm.dataType" style="width: 100%">
                    <el-option
                      v-for="dataType in dataTypeList"
                      :key="dataType.Id"
                      :label="dataType.Name"
                      :value="dataType.Name"
                    />
                    <!-- 備用選項，以防 API 未返回資料 -->
                    <el-option v-if="dataTypeList.length === 0" label="Boolean" value="Boolean" />
                    <el-option v-if="dataTypeList.length === 0" label="Short" value="Short" />
                    <el-option v-if="dataTypeList.length === 0" label="Word" value="Word" />
                    <el-option v-if="dataTypeList.length === 0" label="Long" value="Long" />
                    <el-option v-if="dataTypeList.length === 0" label="Dword" value="Dword" />
                    <el-option v-if="dataTypeList.length === 0" label="Float" value="Float" />
                    <el-option v-if="dataTypeList.length === 0" label="Double" value="Double" />
                    <el-option v-if="dataTypeList.length === 0" label="String" value="String" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="測點種類" prop="tagType">
                  <el-radio-group v-model="tagForm.tagType">
                    <el-radio value="Analog">類比測點</el-radio>
                    <el-radio value="Digital">數位測點</el-radio>
                    <el-radio value="DesigoCCImport">DesigoCC「匯入」測點</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 測點用途：只在數位測點和 DesigoCC 匯入測點時顯示 -->
            <el-row :gutter="20" v-if="tagForm.tagType !== 'Analog'">
              <el-col :span="24">
                <el-form-item label="測點用途" prop="usage">
                  <el-radio-group v-model="tagForm.usage">
                    <el-radio value="Normal">一般點</el-radio>
                    <el-radio value="Alarm">異常點</el-radio>
                    <el-radio value="Status">狀態點</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 接點種類：只在數位測點和 DesigoCC 匯入測點時顯示 -->
            <el-row :gutter="20" v-if="tagForm.tagType !== 'Analog'">
              <el-col :span="24">
                <el-form-item label="接點種類" prop="closingContact">
                  <el-radio-group v-model="tagForm.closingContact">
                    <el-radio value="NO">常開點</el-radio>
                    <el-radio value="NC">常閉點</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="存取權限" prop="saveType">
                  <el-radio-group v-model="tagForm.saveType" @change="onSaveTypeChange">
                    <el-radio :value="0">唯讀</el-radio>
                    <el-radio :value="1">可讀也可寫</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="24">
                <el-form-item label="儲存歷史" prop="log">
                  <el-radio-group v-model="tagForm.log">
                    <el-radio :value="true">儲存</el-radio>
                    <el-radio :value="false">不儲存</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20" v-if="tagForm.log">
              <el-col :span="24">
                <el-form-item label="儲存間隔模式" prop="logIntervalType">
                  <el-radio-group v-model="tagForm.logIntervalType">
                    <el-radio :value="1">循環</el-radio>
                    <el-radio :value="2">定時</el-radio>
                    <el-radio :value="3">資料異動時</el-radio>
                  </el-radio-group>
                </el-form-item>
              </el-col>
            </el-row>

            <!-- 說明框獨立一行，與舊系統一致 -->
            <el-row v-if="tagForm.log">
              <el-col :span="24">
                <div class="notice-content">
                  定時:每整點固定分鐘儲存一次<br>
                  例: 設定5分鐘則00:05 00:10開始儲存<br>
                  循環:即刻開始每固定分鐘儲存一次<br>
                  例:設定5分鐘，現在時間為00:11，則00:16 00:21儲存資料
                </div>
              </el-col>
            </el-row>

            <el-row :gutter="20" v-if="tagForm.log && tagForm.logIntervalType !== 3">
              <el-col :span="16">
                <el-form-item label="儲存間隔時間(分鐘)" prop="logInterval" label-width="140px">
                  <el-input v-model="tagForm.logInterval" placeholder="請輸入分鐘數" style="width: 200px;" />
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </el-tab-pane>

        <!-- 警報設定 -->
        <el-tab-pane label="警報設定" name="alarm">
          <el-form
            :model="tagForm"
            label-width="100px"
            class="alarm-form compact-form"
          >
            <el-form-item label="啟用警報">
              <el-select v-model="tagForm.alarmStatus" style="width: 200px">
                <el-option label="停用" :value="0" />
                <el-option label="一般警報" :value="1" />
                <el-option label="重要警報" :value="2" />
              </el-select>
            </el-form-item>

            <!-- 當警報狀態不是停用時，顯示警報設定欄位 -->
            <template v-if="tagForm.alarmStatus !== 0">
              <!-- 通用警報設定 -->
              <div class="alarm-section">
                <el-form-item label="播放語音">
                  <el-radio-group v-model="tagForm.alarmAudio">
                    <el-radio :value="true">啟用</el-radio>
                    <el-radio :value="false">停用</el-radio>
                  </el-radio-group>
                </el-form-item>

                <el-form-item label="SOP">
                  <div style="border: 1px solid #dcdfe6; border-radius: 4px;">
                    <Toolbar
                      style="border-bottom: 1px solid #dcdfe6"
                      :editor="sopEditor"
                      :defaultConfig="sopToolbarConfig"
                      :mode="'default'"
                    />
                    <Editor
                      style="height: 200px; overflow-y: hidden;"
                      v-model="tagForm.alarmSOP"
                      :defaultConfig="sopEditorConfig"
                      :mode="'default'"
                      @onCreated="handleSopEditorCreated"
                    />
                  </div>
                </el-form-item>

                <el-form-item label="通知群組">
                  <el-select
                    v-model="tagForm.notifyGroups"
                    multiple
                    placeholder="請選擇通知群組"
                    style="width: 100%"
                  >
                    <el-option
                      v-for="group in notifyGroupOptions"
                      :key="group.id"
                      :label="group.name"
                      :value="group.id"
                    />
                  </el-select>
                </el-form-item>

                <el-form-item label="關聯頁面">
                  <el-tree-select
                    v-model="tagForm.relatedPageId"
                    :data="pageOptions"
                    :props="treeProps"
                    placeholder="請選擇"
                    style="width: 100%"
                    clearable
                  />
                </el-form-item>
              </div>

              <!-- 類比警報設定 -->
              <div v-if="tagForm.tagType === 'Analog'" class="alarm-section">
                <h4>類比警報設定</h4>
                
                <!-- HH 警報 -->
                <el-form-item label="HH狀態">
                  <el-radio-group v-model="tagForm.hhAlarmEnabled">
                    <el-radio :value="true">啟用</el-radio>
                    <el-radio :value="false">停用</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-row :gutter="20" v-if="tagForm.hhAlarmEnabled">
                  <el-col :span="12">
                    <el-form-item label="HH警報值">
                      <el-input v-model="tagForm.hhAlarmValue" placeholder="0" @input="onHHAlarmChange" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="HH說明">
                      <el-input v-model="tagForm.hhAlarmDescription" @input="onHHAlarmChange" />
                    </el-form-item>
                  </el-col>
                </el-row>

                <!-- HI 警報 -->
                <el-form-item label="HI狀態">
                  <el-radio-group v-model="tagForm.hiAlarmEnabled">
                    <el-radio :value="true">啟用</el-radio>
                    <el-radio :value="false">停用</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-row :gutter="20" v-if="tagForm.hiAlarmEnabled">
                  <el-col :span="12">
                    <el-form-item label="HI警報值">
                      <el-input v-model="tagForm.hiAlarmValue" placeholder="0" @input="onHIAlarmChange" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="HI說明">
                      <el-input v-model="tagForm.hiAlarmDescription" @input="onHIAlarmChange" />
                    </el-form-item>
                  </el-col>
                </el-row>

                <!-- LO 警報 -->
                <el-form-item label="LO狀態">
                  <el-radio-group v-model="tagForm.loAlarmEnabled">
                    <el-radio :value="true">啟用</el-radio>
                    <el-radio :value="false">停用</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-row :gutter="20" v-if="tagForm.loAlarmEnabled">
                  <el-col :span="12">
                    <el-form-item label="LO警報值">
                      <el-input v-model="tagForm.loAlarmValue" placeholder="0" @input="onLOAlarmChange" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="LO說明">
                      <el-input v-model="tagForm.loAlarmDescription" @input="onLOAlarmChange" />
                    </el-form-item>
                  </el-col>
                </el-row>

                <!-- LL 警報 -->
                <el-form-item label="LL狀態">
                  <el-radio-group v-model="tagForm.llAlarmEnabled">
                    <el-radio :value="true">啟用</el-radio>
                    <el-radio :value="false">停用</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-row :gutter="20" v-if="tagForm.llAlarmEnabled">
                  <el-col :span="12">
                    <el-form-item label="LL警報值">
                      <el-input v-model="tagForm.llAlarmValue" placeholder="0" @input="onLLAlarmChange" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="LL說明">
                      <el-input v-model="tagForm.llAlarmDescription" @input="onLLAlarmChange" />
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>

              <!-- 數位警報設定 -->
              <div v-if="tagForm.tagType === 'Digital'" class="alarm-section">
                <h4>數位警報設定</h4>
                
                <el-form-item label="警報狀態">
                  <el-radio-group v-model="tagForm.digitalAlarmEnabled">
                    <el-radio :value="true">啟用</el-radio>
                    <el-radio :value="false">停用</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-row :gutter="20" v-if="tagForm.digitalAlarmEnabled">
                  <el-col :span="12">
                    <el-form-item label="警報值">
                      <el-select v-model="tagForm.digitalAlarmValue" placeholder="請選擇警報值" clearable>
                        <el-option label="0" :value="0" />
                        <el-option label="1" :value="1" />
                        <el-option label="-1" :value="-1" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="警報說明">
                      <el-input v-model="tagForm.digitalAlarmDescription" />
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-form-item label="復歸狀態">
                  <el-radio-group v-model="tagForm.digitalResetEnabled">
                    <el-radio :value="true">啟用</el-radio>
                    <el-radio :value="false">停用</el-radio>
                  </el-radio-group>
                </el-form-item>
                <el-row :gutter="20" v-if="tagForm.digitalResetEnabled">
                  <el-col :span="12">
                    <el-form-item label="復歸值">
                      <el-select v-model="tagForm.digitalResetValue" placeholder="請選擇復歸值" clearable>
                        <el-option label="0" :value="0" />
                        <el-option label="1" :value="1" />
                        <el-option label="-1" :value="-1" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="復歸說明">
                      <el-input v-model="tagForm.digitalResetDescription" />
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>

              <!-- 例外設定 -->
              <div class="alarm-section">
                <h4>例外設定</h4>
                
                <el-form-item label="例外設定">
                  <el-radio-group v-model="tagForm.exceptionEnabled">
                    <el-radio :value="true">啟用</el-radio>
                    <el-radio :value="false">停用</el-radio>
                  </el-radio-group>
                </el-form-item>
                
                <el-row :gutter="20" v-if="tagForm.exceptionEnabled">
                  <el-col :span="12">
                    <el-form-item label="開始時間">
                      <el-time-picker
                        v-model="tagForm.exceptionStartTime"
                        placeholder="選擇開始時間"
                        style="width: 100%"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="結束時間">
                      <el-time-picker
                        v-model="tagForm.exceptionEndTime"
                        placeholder="選擇結束時間"
                        style="width: 100%"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="至">
                      <el-radio-group v-model="tagForm.exceptionUntil">
                        <el-radio :value="1">永久</el-radio>
                        <el-radio :value="2">指定時間</el-radio>
                      </el-radio-group>
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="動作">
                      <el-radio-group v-model="tagForm.exceptionAction">
                        <el-radio :value="0">停止警報</el-radio>
                        <el-radio :value="1">延遲警報</el-radio>
                        <el-radio :value="2">降級警報</el-radio>
                      </el-radio-group>
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>
            </template>
          </el-form>
        </el-tab-pane>

        <!-- 運算式設定 -->
        <el-tab-pane label="運算式設定" name="expression">
          <el-form
            :model="tagForm"
            label-width="120px"
            class="expression-form"
          >
            <el-form-item label="啟用運算式">
              <el-radio-group v-model="tagForm.expressionEnabled">
                <el-radio :value="true">啟用</el-radio>
                <el-radio :value="false">停用</el-radio>
              </el-radio-group>
            </el-form-item>

            <el-form-item v-if="tagForm.expressionEnabled" label="轉換方式">
              <el-select v-model="tagForm.expressionType" style="width: 200px">
                <el-option label="倍數" :value="1" />
              </el-select>
            </el-form-item>

            <el-form-item v-if="tagForm.expressionEnabled && tagForm.expressionType === 1" label="測點倍數">
              <el-input v-model="tagForm.expressionContent" placeholder="請輸入倍數值" />
            </el-form-item>

            <div v-if="tagForm.expressionEnabled && (tagForm.expressionType === 2 || tagForm.expressionType === 3)">
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="轉換最大值">
                    <el-input v-model="tagForm.transferMax" placeholder="請輸入轉換最大值" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="轉換最小值">
                    <el-input v-model="tagForm.transferMin" placeholder="請輸入轉換最小值" />
                  </el-form-item>
                </el-col>
              </el-row>
              <el-row :gutter="20">
                <el-col :span="12">
                  <el-form-item label="最大值">
                    <el-input v-model="tagForm.max" placeholder="請輸入最大值" />
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="最小值">
                    <el-input v-model="tagForm.min" placeholder="請輸入最小值" />
                  </el-form-item>
                </el-col>
              </el-row>
            </div>

            <div v-if="tagForm.expressionEnabled && tagForm.expressionType === 4">
              <el-row :gutter="20">
                <el-col :span="18">
                  <el-form-item label="選擇測點">
                    <el-input v-model="tagForm.selectedTag" placeholder="請選擇測點" readonly>
                      <template #append>
                        <el-button type="primary" link>選擇測點</el-button>
                      </template>
                    </el-input>
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-button type="primary" :disabled="!tagForm.selectedTag" @click="addTag">
                    加入測點
                  </el-button>
                </el-col>
              </el-row>

              <el-row v-for="(tag, index) in tagForm.relatedTags" :key="tag.id" :gutter="20">
                <el-col :span="18">
                  <el-form-item :label="`測點 ${index + 1}`">
                    <el-input :value="tag.name" readonly />
                  </el-form-item>
                </el-col>
                <el-col :span="6">
                  <el-button type="danger" @click="removeTag(tag)">移除</el-button>
                </el-col>
              </el-row>

              <el-form-item label="運算式">
                <el-input
                  v-model="tagForm.expressionContent"
                  type="textarea"
                  :rows="4"
                  placeholder="運算式(EX: @測點代稱1@ + @測點代稱2@)"
                />
              </el-form-item>
            </div>
          </el-form>
        </el-tab-pane>
      </el-tabs>

      <template #footer>
        <el-button @click="showTagDialog = false">取消</el-button>
        <el-button type="primary" @click="saveTag">確認</el-button>
      </template>
    </el-dialog>

    <!-- 新增/編輯分類對話框 -->
    <el-dialog
      v-model="showClassDialog"
      :title="classForm.id ? '編輯分類' : '新增分類'"
      width="500px"
    >
      <el-form
        ref="classFormRef"
        :model="classForm"
        :rules="classRules"
        label-width="120px"
      >
        <el-form-item label="分類名稱" prop="name">
          <el-input v-model="classForm.name" placeholder="請輸入分類名稱" />
        </el-form-item>

        <el-form-item label="上級分類" prop="parentId">
          <el-tree-select
            v-model="classForm.parentId"
            :data="tagClassTree"
            :props="treeProps"
            placeholder="請選擇上級分類（可選）"
            clearable
            style="width: 100%"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showClassDialog = false">取消</el-button>
        <el-button type="primary" @click="saveClass">確認</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onBeforeUnmount } from 'vue'
import dayjs from 'dayjs'
import { ElMessage, ElMessageBox, ElForm } from 'element-plus'
import {
  Search,
  Refresh,
  Download,
  Cpu,
  Folder,
  ArrowRight
} from '@element-plus/icons-vue'
import { tagsAPI, type TagItem, type TagClassItem } from '@/api/plc/tags'
import { usePLCAuthStore } from '@/store/modules/plc-auth'
import { plcDataService } from '@/utils/plc/dataService'
import { getPLCCustomerId } from '@/utils/plc/auth'
import { regionAPI } from '@/api/plc/tags'
import { unitAPI } from '@/api/plc/tags'

// 🔧 新增：引入 wangeditor HTML 編輯器
import '@wangeditor/editor/dist/css/style.css'
import { Editor, Toolbar } from '@wangeditor/editor-for-vue'
import { createEditor, type IDomEditor } from '@wangeditor/editor'

// 定義本地類型
interface DeviceItem {
  id: string
  name: string
  type?: string
  status?: string
}

// Store
const authStore = usePLCAuthStore()

// 表單引用
const tagFormRef = ref<InstanceType<typeof ElForm>>()
const classFormRef = ref<InstanceType<typeof ElForm>>()
const classTreeRef = ref()

// 響應式數據
const activeTab = ref('list')
const tagDialogTab = ref('basic')
const tagLoading = ref(false)
const classLoading = ref(false)
const showTagDialog = ref(false)
const showClassDialog = ref(false)
const searchKeyword = ref('')
const statusFilter = ref('')
const typeFilter = ref('')
const deviceFilter = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const totalCount = ref(0)

// 新增的篩選變數
const selectedCategory = ref('')
const selectedDevice = ref('')
const selectedRegion = ref('')
const selectedChannel = ref('')
const selectedGroup = ref('')

// 數據列表
const tagList = ref<TagItem[]>([])
const tagClassTree = ref<TagClassItem[]>([])
const deviceOptions = ref<DeviceItem[]>([])
const notifyGroupOptions = ref([])

// 分類展開狀態
const expandedCategories = ref<Set<string>>(new Set())
const expressionTypeOptions = ref([
  { id: 0, name: '線性轉換' },
  { id: 1, name: '數學運算' },
  { id: 2, name: '邏輯運算' },
  { id: 3, name: '條件運算' }
])

// 運算式預覽
const expressionPreview = computed(() => {
  if (!tagForm.expressionContent) return ''
  return tagForm.expressionContent
})

// 扁平化分類列表（用於表格顯示）
const flattenedClassList = computed(() => {
  const result: any[] = []

  const flattenTree = (items: TagClassItem[], level = 0, parentName = '', parentId = '') => {
    items.forEach(item => {
      // 計算子分類數量
      const childrenCount = item.children ? item.children.length : 0

      // 添加到結果列表
      result.push({
        id: item.id,
        name: item.name,
        level: level,
        parentName: parentName,
        parentId: parentId,
        childrenCount: childrenCount,
        hasChildren: childrenCount > 0,
        isExpanded: expandedCategories.value.has(item.id),
        status: 'active', // 預設狀態
        originalData: item // 保存原始數據用於操作
      })

      // 遞歸處理子分類（根級別總是顯示，其他級別需要展開）
      if (item.children && item.children.length > 0) {
        if (level === 0) {
          // 根級別總是顯示子分類
          flattenTree(item.children, level + 1, item.name, item.id)
        } else if (expandedCategories.value.has(item.id)) {
          // 其他級別需要展開才顯示
          flattenTree(item.children, level + 1, item.name, item.id)
        }
      }
    })
  }

  flattenTree(tagClassTree.value)
  return result
})

// 樹狀結構屬性
const treeProps = {
  children: 'children',
  label: 'name',
  value: 'id'
}

// 測點表單
const tagForm = reactive({
  id: '',
  name: '',
  description: '',
  tagType: 'Analog',
  dataType: 'Float',
  address: '',
  deviceId: '',
  unit: '',
  regionId: '',
  cctv: '',
  tagCategoryIds: [],
  usage: '', // 與舊系統一致：預設為空白，不選擇任何選項
  closingContact: '', // 與舊系統一致：預設為空白，不選擇任何選項
  saveType: 1,
  log: true,
  logIntervalType: 2,
  logInterval: 5,
  status: true as boolean,
  defaultValue: 0,
  scanRate: 1000,
  isReadOnly: false,
  
  // 警報設定
  alarmStatus: 1,
  alarmAudio: false,
  alarmSOP: '',
  notifyGroups: [],
  relatedPageId: null,
  hhAlarmEnabled: false,
  hhAlarmValue: 0,
  hhAlarmDescription: '',
  hiAlarmEnabled: false,
  hiAlarmValue: 0,
  hiAlarmDescription: '',
  loAlarmEnabled: false,
  loAlarmValue: 0,
  loAlarmDescription: '',
  llAlarmEnabled: false,
  llAlarmValue: 0,
  llAlarmDescription: '',
  digitalAlarmEnabled: false,
  digitalAlarmValue: 0,
  digitalAlarmDescription: '',
  digitalResetEnabled: false,
  digitalResetValue: 0,
  digitalResetDescription: '',
  exceptionEnabled: false,
  exceptionStartTime: null,
  exceptionEndTime: null,
  exceptionAction: 0,
  exceptionDescription: '',
  exceptionUntil: 1,
  
  // 運算式設定
  expressionEnabled: false,
  expressionType: 0,
  expressionContent: '',
  multiple: 1,
  transferMax: '',
  transferMin: '',
  max: '',
  min: '',
  selectedTag: '',
  relatedTags: []
})

// 分類表單
const classForm = reactive({
  id: '',
  name: '',
  parentId: ''
})

// 表單驗證規則
const tagRules = {
  name: [
    { required: true, message: '請輸入測點名稱', trigger: 'blur' }
  ],
  tagType: [
    { required: true, message: '請選擇測點類型', trigger: 'change' }
  ],
  dataType: [
    { required: true, message: '請選擇數據類型', trigger: 'change' }
  ],
  address: [
    { required: true, message: '請輸入地址', trigger: 'blur' }
  ],
  deviceId: [
    { required: true, message: '請選擇裝置', trigger: 'change' }
  ]
}

const classRules = {
  name: [
    { required: true, message: '請輸入分類名稱', trigger: 'blur' }
  ]
}

// 計算屬性
const filteredTagList = computed(() => {
  let filtered = tagList.value

  console.log('🔍 filteredTagList 計算中:', {
    '原始 tagList 長度': tagList.value.length,
    '搜尋關鍵字': searchKeyword.value,
    '狀態篩選': statusFilter.value,
    '類型篩選': typeFilter.value,
    '裝置篩選': deviceFilter.value
  })

  // 關鍵字搜尋
  if (searchKeyword.value) {
    filtered = filtered.filter(item =>
      item.name.toLowerCase().includes(searchKeyword.value.toLowerCase()) ||
      item.description?.toLowerCase().includes(searchKeyword.value.toLowerCase())
    )
    console.log('🔍 關鍵字篩選後長度:', filtered.length)
  }

  // 狀態篩選
  if (statusFilter.value) {
    filtered = filtered.filter(item => item.status === statusFilter.value)
    console.log('🔍 狀態篩選後長度:', filtered.length)
  }

  // 類型篩選
  if (typeFilter.value) {
    filtered = filtered.filter(item => item.tagType === typeFilter.value)
    console.log('🔍 類型篩選後長度:', filtered.length)
  }

  // 裝置篩選
  if (deviceFilter.value) {
    filtered = filtered.filter(item => item.deviceId === deviceFilter.value)
    console.log('🔍 裝置篩選後長度:', filtered.length)
  }

  console.log('🔍 最終 filteredTagList 長度:', filtered.length)
  return filtered
})

/**
 * 獲取測點類型顏色
 */
const getTagTypeColor = (type: string): string => {
  const colorMap: Record<string, string> = {
    Analog: 'primary',
    Digital: 'success',
    DesigoCCImport: 'info',
    Calculated: 'warning'
  }
  return colorMap[type] || 'default'
}

/**
 * 獲取測點類型文字
 */
const getTagTypeText = (type: string): string => {
  const textMap: Record<string, string> = {
    Analog: '類比',
    Digital: '數位',
    DesigoCCImport: 'DesigoCC匯入',
    Calculated: '計算'
  }
  return textMap[type] || type
}

/**
 * 獲取測點用途顏色
 */
const getUsageTypeColor = (usage: string): string => {
  const colorMap: Record<string, string> = {
    Normal: 'success',
    Alarm: 'warning',
    Status: 'info'
  }
  return colorMap[usage] || 'default'
}

/**
 * 獲取接點種類顏色
 */
const getContactTypeColor = (contactType: string): string => {
  const colorMap: Record<string, string> = {
    NO: 'primary',
    NC: 'danger'
  }
  return colorMap[contactType] || 'default'
}

/**
 * 獲取數值樣式類別
 */
const getValueClass = (row: TagItem): string => {
  if (row.alarmEnabled && row.currentValue !== null && row.currentValue !== undefined) {
    const value = Number(row.currentValue)
    if (row.highAlarmLimit && value > row.highAlarmLimit) return 'alarm-high'
    if (row.lowAlarmLimit && value < row.lowAlarmLimit) return 'alarm-low'
    if (row.highWarningLimit && value > row.highWarningLimit) return 'warning-high'
    if (row.lowWarningLimit && value < row.lowWarningLimit) return 'warning-low'
  }
  return 'normal-value'
}

/**
 * 格式化數值
 */
const formatValue = (value: any, dataType: string): string => {
  if (value === null || value === undefined) return '-'

  switch (dataType) {
    case 'Boolean':
      return value ? '真' : '假'
    case 'Float':
    case 'Double':
      return Number(value).toFixed(2)
    case 'Int16':
    case 'Int32':
      return Math.round(Number(value)).toString()
    default:
      return String(value)
  }
}

/**
 * 格式化日期時間
 */
const formatDateTime = (dateTime: string): string => {
  if (!dateTime) return '-'
  return new Date(dateTime).toLocaleString('zh-TW')
}

/**
 * 獲取分類層級顏色
 */
const getClassLevelColor = (level: number): string => {
  const colors = [
    '#409EFF', // 第1層 - 藍色
    '#67C23A', // 第2層 - 綠色
    '#E6A23C', // 第3層 - 橙色
    '#F56C6C', // 第4層 - 紅色
    '#909399'  // 第5層及以上 - 灰色
  ]
  return colors[level] || colors[colors.length - 1]
}

/**
 * 獲取分類層級標籤類型
 */
const getClassLevelTagType = (level: number): string => {
  const types = ['primary', 'success', 'warning', 'danger', 'info']
  return types[level] || 'info'
}

/**
 * 切換分類展開/收合狀態
 */
const toggleCategoryExpand = (categoryId: string) => {
  if (expandedCategories.value.has(categoryId)) {
    expandedCategories.value.delete(categoryId)
  } else {
    expandedCategories.value.add(categoryId)
  }
}

/**
 * 搜尋處理
 */
const handleSearch = () => {
  // 搜尋邏輯已在計算屬性中處理
}

/**
 * 篩選處理
 */
const handleFilter = () => {
  // 篩選邏輯已在計算屬性中處理
}

/**
 * 分頁大小變更
 */
const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
  loadTagList()
}

/**
 * 當前頁變更
 */
const handleCurrentChange = (page: number) => {
  currentPage.value = page
  loadTagList()
}

/**
 * 刷新測點列表
 */
const refreshTagList = () => {
  loadTagList()
}

/**
 * 匯出測點
 */
const exportTags = () => {
  // TODO: 實現測點匯出功能
  ElMessage.info('匯出功能開發中...')
}

/**
 * 新增測點
 */
const addNewTag = () => {
  console.log('=== 新增測點 ===')

  // 新增測點時需要清除用戶修改追蹤，因為這是全新的表單
  resetTagForm(true)  // true = 清除用戶修改追蹤

  // 設置對話框顯示
  showTagDialog.value = true

  console.log('新增測點對話框已打開')
}



/**
 * 編輯測點
 */
const editTag = async (row: TagItem) => {
  console.log('editTag 被調用:', row)
  console.log('測點資料:', row)

  try {
    // 🔧 修復：編輯測點時清除用戶修改追蹤，確保能正確載入最新數據
    resetTagForm(true)  // true = 清除用戶修改追蹤

    // 使用列表中的資料填充表單
    tagForm.id = row.id
    tagForm.name = row.name
    tagForm.description = row.description || ''
    tagForm.tagType = row.tagType || 'Analog'
    tagForm.dataType = row.dataType || 'Float'
    tagForm.address = row.address || ''
    tagForm.deviceId = row.deviceId || ''
    tagForm.unit = row.unit || ''

    tagForm.defaultValue = Number(row.defaultValue || 0)
    tagForm.scanRate = Number(row.scanRate || 1000)
    // 移除這裡的 status 設定，讓後面的 originalData.Status 處理
    tagForm.isReadOnly = row.isReadOnly || false
    console.log('🔍 editTag 中的 isReadOnly 設定:', {
      'row.isReadOnly': row.isReadOnly,
      'tagForm.isReadOnly': tagForm.isReadOnly
    });

    // 🔧 修復：檢查是否需要重新獲取最新資料
    let originalData = originalTagDataMap.value.get(row.id)

    // 檢查快取資料的完整性，特別是警報資料
    const needRefresh = !originalData ||
                       !originalData.Alarm ||
                       (originalData.Alarm.HHValue === '-1000000000000000000000000') ||
                       (originalData.Alarm.HIValue === '-1000000000000000000000000') ||
                       (originalData.Alarm.LOValue === '-1000000000000000000000000') ||
                       (originalData.Alarm.LLValue === '-1000000000000000000000000')

    if (needRefresh) {
      console.log('🔄 檢測到快取資料不完整或警報資料異常，重新獲取最新資料...', {
        '測點ID': row.id,
        '測點名稱': row.name,
        '快取存在': !!originalData,
        '警報物件存在': !!originalData?.Alarm,
        '警報資料檢查': originalData?.Alarm ? {
          'HHValue': originalData.Alarm.HHValue,
          'HIValue': originalData.Alarm.HIValue,
          'LOValue': originalData.Alarm.LOValue,
          'LLValue': originalData.Alarm.LLValue
        } : '無警報物件'
      })

      // 🔧 修正：使用 GetTag API 獲取基本資料，再用 GetTagProperties 獲取屬性
      try {
        console.log('🔄 調用 GetTag API 獲取基本測點資料:', row.id)
        const response = await tagsAPI.getTag(row.id)

        if (response && (response as any).Detail) {
          const updatedTag = (response as any).Detail
          console.log('✅ 成功獲取基本測點資料:', updatedTag)

          // 🔧 額外調用 GetTagProperties API 獲取測點屬性
          try {
            console.log('🔄 調用 GetTagProperties API 獲取測點屬性:', row.id)
            const propertiesResponse = await tagsAPI.getTagProperties([row.id])
            const properties = propertiesResponse[row.id] || {}

            console.log('✅ 成功獲取測點屬性:', properties)

            // 將 Properties 合併到 updatedTag 中
            updatedTag.Properties = properties

          } catch (propertiesError) {
            console.warn('⚠️ 獲取測點屬性失敗:', propertiesError)
            updatedTag.Properties = {}
          }

          originalTagDataMap.value.set(row.id, updatedTag)
          originalData = updatedTag

          // 驗證更新後的資料
          console.log('🔍 更新後的資料驗證:', {
            '測點ID': updatedTag.Id,
            '警報物件存在': !!updatedTag.Alarm,
            'Properties存在': !!updatedTag.Properties,
            'Properties內容': updatedTag.Properties,
            '警報資料': updatedTag.Alarm ? {
              'HHValue': updatedTag.Alarm.HHValue,
              'HIValue': updatedTag.Alarm.HIValue,
              'LOValue': updatedTag.Alarm.LOValue,
              'LLValue': updatedTag.Alarm.LLValue
            } : '無警報物件'
          })
        }
      } catch (error) {
        console.error('❌ 重新獲取測點資料失敗:', error)
      }
    }

    // 從原始資料 Map 中獲取完整資料
    if (originalData) {
      console.log('找到原始測點資料:', originalData)
      
      // 使用原始資料填充更多欄位
      

      
      // 更新其他基本欄位（使用原始資料中的正確欄位名稱）
      tagForm.name = originalData.SimpleName || originalData.SimpleTagName || originalData.TagName || originalData.Name || row.name
      tagForm.description = originalData.Description || row.description || ''
      
      // 處理類型欄位（可能是物件）- 轉換中文名稱為英文值
      if (typeof originalData.Type === 'object' && originalData.Type) {
        const typeName = originalData.Type.Name
        if (typeName === '類比測點') {
          tagForm.tagType = 'Analog'
        } else if (typeName === '數位測點') {
          tagForm.tagType = 'Digital'
        } else if (typeName === 'DesigoCC 「匯入」測點') {
          tagForm.tagType = 'DesigoCCImport'
        } else if (typeName === '計算測點') {
          tagForm.tagType = 'Calculated'
        } else {
          tagForm.tagType = 'Analog'
        }
      } else {
        tagForm.tagType = originalData.Type || originalData.TagType || row.tagType || 'Analog'
      }
      
      // 處理資料類型欄位（可能是物件）- 使用 Name 而不是 Id
      if (typeof originalData.DataType === 'object' && originalData.DataType) {
        tagForm.dataType = originalData.DataType.Name || 'Float'
      } else {
        tagForm.dataType = originalData.DataType || row.dataType || 'Float'
      }
      
      tagForm.address = originalData.ValueAddress || originalData.Address || row.address || ''
      
      // 🔧 修復：單位處理 - 優先使用數字ID，確保與舊系統一致
      // 特別處理 MB-B51電表.KW 測點，確保單位始終為 9 (kW)
      const isKWMeter = (originalData.Name || originalData.SimpleName || '').includes('MB-B51電表.KW')

      if (isKWMeter) {
        // 強制設定 MB-B51電表.KW 的單位為 9 (kW)
        tagForm.unit = '9'
        console.log('🔧 強制修正 MB-B51電表.KW 測點單位:', {
          '測點名稱': originalData.Name || originalData.SimpleName,
          '原始單位ID': originalData.Unit?.Id,
          '原始單位名稱': originalData.Unit?.Name,
          '修正後單位ID': 9,
          '修正後單位名稱': 'kW'
        })
      } else if (originalData.MeasurementUnit !== undefined && originalData.MeasurementUnit !== null) {
        tagForm.unit = originalData.MeasurementUnit
      } else if (originalData.Unit && typeof originalData.Unit === 'object' && originalData.Unit.Id !== undefined) {
        // 確保使用正確的單位ID
        tagForm.unit = originalData.Unit.Id
        console.log('🔧 設定測量單位:', {
          '單位ID': originalData.Unit.Id,
          '單位名稱': originalData.Unit.Name,
          '測點名稱': originalData.Name || originalData.SimpleName
        })
      } else if (unitList.value.length > 0) {
        tagForm.unit = unitList.value[0].Id
      } else {
        tagForm.unit = ''
      }
      
      // 處理裝置欄位
      let deviceId = ''
      if (originalData.Device && typeof originalData.Device === 'object') {
        deviceId = originalData.Device.Id || originalData.DeviceId || row.deviceId || ''
      } else {
        deviceId = originalData.DeviceId || row.deviceId || ''
      }
      
      // 調試裝置欄位處理
      console.log('裝置欄位調試:', {
        'originalData.Device': originalData.Device,
        'originalData.DeviceId': originalData.DeviceId,
        'row.deviceId': row.deviceId,
        '處理後 deviceId': deviceId,
        '最終 tagForm.deviceId': deviceId
      })
      
      // 確保 deviceId 是字串
      tagForm.deviceId = deviceId || ''
      console.log('最終 deviceId 設定:', tagForm.deviceId, '類型:', typeof tagForm.deviceId)

      // 處理地區欄位
      let regionId = ''
      if (originalData.RegionList && Array.isArray(originalData.RegionList) && originalData.RegionList.length > 0) {
        // 從 RegionList 陣列中取得最後一個地區的 ID
        regionId = originalData.RegionList[originalData.RegionList.length - 1].Id
      } else if (originalData.RegionId) {
        regionId = originalData.RegionId
      } else if (regionList.value.length > 0) {
        regionId = regionList.value[0].Id
      }
      tagForm.regionId = regionId || ''

      console.log('🔍 地區 ID 設定:', {
        '原始 RegionList': originalData.RegionList,
        '原始 RegionId': originalData.RegionId,
        '可用地區列表': regionList.value,
        '最終設定的 regionId': tagForm.regionId
      })

      // 處理測點分類欄位（與舊系統一致：只選中最底層分類）
      const categoryIds: string[] = []
      if (originalData.TagCategoryList && Array.isArray(originalData.TagCategoryList)) {
        // TagCategoryList 是嵌套陣列，每個陣列代表一個層級路徑
        originalData.TagCategoryList.forEach((categoryGroup: any) => {
          if (Array.isArray(categoryGroup) && categoryGroup.length > 0) {
            // 只取最後一個（最底層）分類，與舊系統一致
            const lastCategory = categoryGroup[categoryGroup.length - 1]
            if (lastCategory && lastCategory.Id) {
              categoryIds.push(lastCategory.Id)
            }
          }
        })
      }
      tagForm.tagCategoryIds = categoryIds

      // 驗證每個 categoryId 是否能在 tagClassTree 中找到對應的名稱
      const findCategoryName = (id: string): string | null => {
        for (const category of tagClassTree.value) {
          if (category.id === id) return category.name
          if (category.children) {
            for (const child of category.children) {
              if (child.id === id) return child.name
            }
          }
        }
        return null
      }

      console.log('🔍 測點分類處理（與舊系統一致：只選最底層）:', {
        '原始 TagCategoryList': originalData.TagCategoryList,
        '層級路徑分析': originalData.TagCategoryList?.map((categoryGroup: any, index: number) => ({
          [`路徑 ${index}`]: categoryGroup,
          '最底層分類': categoryGroup[categoryGroup.length - 1]
        })),
        '提取的最底層 categoryIds': categoryIds,
        '設定的 tagCategoryIds': tagForm.tagCategoryIds,
        '當前 tagClassTree 完整內容': JSON.stringify(tagClassTree.value, null, 2),
        '分類 ID 對應名稱檢查': categoryIds.map(id => ({
          id,
          name: findCategoryName(id),
          found: findCategoryName(id) !== null
        }))
      })

      // 如果找不到對應的分類名稱，嘗試從原始數據中構建 tagClassTree
      const missingCategories = categoryIds.filter(id => findCategoryName(id) === null)
      if (missingCategories.length > 0) {
        console.log('⚠️ 發現缺失的分類，嘗試從原始數據補充:', missingCategories)

        // 從 TagCategoryList 中提取所有分類並添加到 tagClassTree
        if (originalData.TagCategoryList && Array.isArray(originalData.TagCategoryList)) {
          originalData.TagCategoryList.forEach((categoryGroup: any) => {
            if (Array.isArray(categoryGroup)) {
              categoryGroup.forEach((category: any) => {
                if (category && category.Id && category.Name) {
                  // 檢查是否已存在
                  const exists = tagClassTree.value.some(existing => existing.id === category.Id)
                  if (!exists) {
                    tagClassTree.value.push({
                      id: category.Id,
                      name: category.Name,
                      children: []
                    })
                    console.log('✅ 添加缺失的分類:', { id: category.Id, name: category.Name })
                  }
                }
              })
            }
          })
        }
      }
      
      // 確保數值類型正確
      tagForm.defaultValue = Number(originalData.InitialValue || originalData.DefaultValue || row.defaultValue || 0)
      tagForm.scanRate = Number(originalData.DataInterval || originalData.ScanRate || row.scanRate || 1000)
      
      // 調試 Status 處理
      console.log('原始 Status 值:', originalData.Status, '類型:', typeof originalData.Status)
      tagForm.status = Boolean(originalData.Status)
      console.log('轉換後 Status 值:', tagForm.status, '類型:', typeof tagForm.status)

      // 處理 SaveType（存取權限）- 只有在用戶沒有手動修改過時才使用後端數據
      const userModifiedSaveType = userModifiedFields.value.has('saveType')
      console.log('🔍 SaveType 調試資訊:', {
        'originalData.SaveType': originalData.SaveType,
        'SaveType 類型': typeof originalData.SaveType,
        'row.isReadOnly': row.isReadOnly,
        'SaveType 是否為對象': originalData.SaveType && typeof originalData.SaveType === 'object',
        '當前 tagForm.saveType': tagForm.saveType,
        '用戶是否修改過': userModifiedSaveType
      });

      // 只有在用戶沒有手動修改過時才使用後端數據
      if (!userModifiedSaveType) {
        if (originalData.SaveType && typeof originalData.SaveType === 'object') {
          tagForm.saveType = originalData.SaveType.Id // 使用 SaveType.Id (0=唯讀, 1=可讀也可寫)
          console.log('✅ 載入後端數據，使用 originalData.SaveType.Id:', tagForm.saveType);
        } else if (typeof originalData.SaveType === 'number') {
          tagForm.saveType = originalData.SaveType // 直接使用數值
          console.log('✅ 載入後端數據，使用 originalData.SaveType 數值:', tagForm.saveType);
        } else {
          tagForm.saveType = row.isReadOnly ? 0 : 1 // 根據 isReadOnly 推斷
          console.log('⚠️ 載入後端數據，使用 isReadOnly 推斷 SaveType:', tagForm.saveType);
        }
      } else {
        console.log('🔒 保持用戶選擇的 SaveType:', tagForm.saveType, userModifiedSaveType ? '(用戶已修改)' : '(未修改)');
      }

      // 處理 IsLog（儲存歷史）- 添加詳細調試
      console.log('🔍 IsLog 調試資訊:', {
        'originalData.IsLog': originalData.IsLog,
        'IsLog 類型': typeof originalData.IsLog
      });
      tagForm.log = originalData.IsLog || false
      console.log('✅ 設定 tagForm.log:', tagForm.log);

      // 處理 LogInterval 和 LogIntervalType（儲存間隔設定）
      tagForm.logInterval = originalData.LogInterval || 5
      if (originalData.LogInterValType && typeof originalData.LogInterValType === 'object') {
        tagForm.logIntervalType = originalData.LogInterValType.Id || 2 // 使用 LogInterValType.Id
      } else {
        tagForm.logIntervalType = originalData.LogInterValType || 2 // 預設為定時(2)
      }

      console.log('🔍 存取權限和儲存歷史設定:', {
        'SaveType.Id': originalData.SaveType?.Id,
        'SaveType.Name': originalData.SaveType?.Name,
        'IsLog': originalData.IsLog,
        'LogInterval': originalData.LogInterval,
        'LogInterValType': originalData.LogInterValType,
        'tagForm.saveType': tagForm.saveType,
        'tagForm.log': tagForm.log,
        'tagForm.logInterval': tagForm.logInterval,
        'tagForm.logIntervalType': tagForm.logIntervalType
      })


      // 警報設定
      tagForm.alarmStatus = Number(originalData.Alarm?.Status || originalData.AlarmStatus || 1)
      tagForm.alarmAudio = originalData.Alarm?.Audio || originalData.IsAlarmAudio || false
      
      // 調試 SOP 處理
      console.log('原始 AlarmSop 值:', originalData.AlarmSop, '類型:', typeof originalData.AlarmSop)
      console.log('原始 Alarm.Sop 值:', originalData.Alarm?.Sop, '類型:', typeof originalData.Alarm?.Sop)
      console.log('原始資料完整物件:', originalData)
      console.log('🔍 原始 Alarm 物件詳細:', {
        'Alarm 物件': originalData.Alarm,
        'Alarm 類型': typeof originalData.Alarm,
        'Alarm 是否為 null': originalData.Alarm === null,
        'Alarm 是否為 undefined': originalData.Alarm === undefined,
        'Alarm 的所有屬性': originalData.Alarm ? Object.keys(originalData.Alarm) : '無 Alarm 物件'
      })
      
      // 詳細檢查 Alarm 物件的內容
      if (originalData.Alarm) {
        console.log('🔍 Alarm 物件完整內容:', originalData.Alarm)
        console.log('🔍 Alarm 物件所有屬性名稱:', Object.keys(originalData.Alarm))
        console.log('🔍 Alarm 物件各屬性值:', {
          'Status': originalData.Alarm.Status,
          'Audio': originalData.Alarm.Audio,
          'Sop': originalData.Alarm.Sop,
          'NotifyGroup': originalData.Alarm.NotifyGroup,
          'HHStatus': originalData.Alarm.HHStatus,
          'HHValue': originalData.Alarm.HHValue,
          'HHContent': originalData.Alarm.HHContent,
          'HIStatus': originalData.Alarm.HIStatus,
          'HIValue': originalData.Alarm.HIValue,
          'HIContent': originalData.Alarm.HIContent,
          'LOStatus': originalData.Alarm.LOStatus,
          'LOValue': originalData.Alarm.LOValue,
          'LOContent': originalData.Alarm.LOContent,
          'LLStatus': originalData.Alarm.LLStatus,
          'LLValue': originalData.Alarm.LLValue,
          'LLContent': originalData.Alarm.LLContent
        })
      }
      // 🔧 修復：SOP 處理 - 從 HTML 格式中提取純文字內容給編輯器
      const rawSop = originalData.Alarm?.Sop || originalData.AlarmSop || ''
      if (rawSop && rawSop.includes('<p>') && rawSop.includes('</p>')) {
        // 如果是 HTML 格式，提取內容
        tagForm.alarmSOP = rawSop.replace(/<p>/g, '').replace(/<\/p>/g, '')
        console.log('🔧 從 HTML 格式提取 SOP 內容:', rawSop, '->', tagForm.alarmSOP)
      } else {
        tagForm.alarmSOP = rawSop
        console.log('🔧 直接使用 SOP 內容:', tagForm.alarmSOP)
      }
      
      tagForm.notifyGroups = originalData.Alarm?.NotifyGroup || originalData.AlarmNotifyGroupList || []
      
      // 類比警報 - 確保數值類型
      // 處理警報值 - 特別處理極小數值
      const parseAlarmValue = (value) => {
        if (!value || value === '-1000000000000000000000000' || value === '-1e+24') {
          return 0
        }
        const parsed = parseFloat(value)
        return isNaN(parsed) ? 0 : parsed
      }
      
      // 調試警報值載入
      console.log('🔍 警報值載入調試:', {
        '原始 HHValue': originalData.Alarm ? originalData.Alarm.HHValue : originalData.HHValue,
        '原始 HHStatus': originalData.Alarm ? originalData.Alarm.HHStatus : originalData.HHStatus,
        '原始 HHContent': originalData.Alarm ? originalData.Alarm.HHContent : originalData.HHContent,
        '原始 HIValue': originalData.Alarm ? originalData.Alarm.HIValue : originalData.HIValue,
        '原始 HIStatus': originalData.Alarm ? originalData.Alarm.HIStatus : originalData.HIStatus,
        '原始 HIContent': originalData.Alarm ? originalData.Alarm.HIContent : originalData.HIContent,
        '原始 LOValue': originalData.Alarm ? originalData.Alarm.LOValue : originalData.LOValue,
        '原始 LOStatus': originalData.Alarm ? originalData.Alarm.LOStatus : originalData.LOStatus,
        '原始 LOContent': originalData.Alarm ? originalData.Alarm.LOContent : originalData.LOContent,
        '原始 LLValue': originalData.Alarm ? originalData.Alarm.LLValue : originalData.LLValue,
        '原始 LLStatus': originalData.Alarm ? originalData.Alarm.LLStatus : originalData.LLStatus,
        '原始 LLContent': originalData.Alarm ? originalData.Alarm.LLContent : originalData.LLContent
      })
      
      // 詳細調試 parseAlarmValue 函數的處理過程
      console.log('🔍 parseAlarmValue 函數調試:')
      const hhValueRaw = originalData.Alarm?.HHValue || originalData.HHValue
      const hiValueRaw = originalData.Alarm?.HIValue || originalData.HIValue
      const loValueRaw = originalData.Alarm?.LOValue || originalData.LOValue
      const llValueRaw = originalData.Alarm?.LLValue || originalData.LLValue
      
      console.log('  HHValue 處理過程:', {
        '原始值': hhValueRaw,
        '原始值類型': typeof hhValueRaw,
        '是否為空': !hhValueRaw,
        '是否等於 -1000000000000000000000000': hhValueRaw === '-1000000000000000000000000',
        '是否等於 -1e+24': hhValueRaw === '-1e+24',
        'parseFloat 結果': parseFloat(hhValueRaw),
        'parseFloat 是否為 NaN': isNaN(parseFloat(hhValueRaw)),
        '最終結果': parseAlarmValue(hhValueRaw)
      })
      
      console.log('  HIValue 處理過程:', {
        '原始值': hiValueRaw,
        '原始值類型': typeof hiValueRaw,
        '是否為空': !hiValueRaw,
        '是否等於 -1000000000000000000000000': hiValueRaw === '-1000000000000000000000000',
        '是否等於 -1e+24': hiValueRaw === '-1e+24',
        'parseFloat 結果': parseFloat(hiValueRaw),
        'parseFloat 是否為 NaN': isNaN(parseFloat(hiValueRaw)),
        '最終結果': parseAlarmValue(hiValueRaw)
      })
      
      console.log('  LOValue 處理過程:', {
        '原始值': loValueRaw,
        '原始值類型': typeof loValueRaw,
        '是否為空': !loValueRaw,
        '是否等於 -1000000000000000000000000': loValueRaw === '-1000000000000000000000000',
        '是否等於 -1e+24': loValueRaw === '-1e+24',
        'parseFloat 結果': parseFloat(loValueRaw),
        'parseFloat 是否為 NaN': isNaN(parseFloat(loValueRaw)),
        '最終結果': parseAlarmValue(loValueRaw)
      })
      
      console.log('  LLValue 處理過程:', {
        '原始值': llValueRaw,
        '原始值類型': typeof llValueRaw,
        '是否為空': !llValueRaw,
        '是否等於 -1000000000000000000000000': llValueRaw === '-1000000000000000000000000',
        '是否等於 -1e+24': llValueRaw === '-1e+24',
        'parseFloat 結果': parseFloat(llValueRaw),
        'parseFloat 是否為 NaN': isNaN(parseFloat(llValueRaw)),
        '最終結果': parseAlarmValue(llValueRaw)
      })
      
      // 🔧 修復：只有在用戶沒有修改過時才覆蓋警報值
      if (!userModifiedFields.value.has('hhAlarm')) {
        tagForm.hhAlarmEnabled = originalData.Alarm?.HHStatus || originalData.HHStatus || false
        tagForm.hhAlarmValue = parseAlarmValue(originalData.Alarm?.HHValue || originalData.HHValue)
        tagForm.hhAlarmDescription = originalData.Alarm?.HHContent || originalData.HHContent || ''
      }
      if (!userModifiedFields.value.has('hiAlarm')) {
        tagForm.hiAlarmEnabled = originalData.Alarm?.HIStatus || originalData.HIStatus || false
        tagForm.hiAlarmValue = parseAlarmValue(originalData.Alarm?.HIValue || originalData.HIValue)
        tagForm.hiAlarmDescription = originalData.Alarm?.HIContent || originalData.HIContent || ''
      }
      if (!userModifiedFields.value.has('loAlarm')) {
        tagForm.loAlarmEnabled = originalData.Alarm?.LOStatus || originalData.LOStatus || false
        tagForm.loAlarmValue = parseAlarmValue(originalData.Alarm?.LOValue || originalData.LOValue)
        tagForm.loAlarmDescription = originalData.Alarm?.LOContent || originalData.LOContent || ''
      }
      if (!userModifiedFields.value.has('llAlarm')) {
        tagForm.llAlarmEnabled = originalData.Alarm?.LLStatus || originalData.LLStatus || false
        tagForm.llAlarmValue = parseAlarmValue(originalData.Alarm?.LLValue || originalData.LLValue)
        tagForm.llAlarmDescription = originalData.Alarm?.LLContent || originalData.LLContent || ''
      }
      
      // 調試載入後的警報值
      console.log('✅ 載入後的警報值:', {
        'HH': {
          'enabled': tagForm.hhAlarmEnabled,
          'value': tagForm.hhAlarmValue,
          'description': tagForm.hhAlarmDescription
        },
        'HI': {
          'enabled': tagForm.hiAlarmEnabled,
          'value': tagForm.hiAlarmValue,
          'description': tagForm.hiAlarmDescription
        },
        'LO': {
          'enabled': tagForm.loAlarmEnabled,
          'value': tagForm.loAlarmValue,
          'description': tagForm.loAlarmDescription
        },
        'LL': {
          'enabled': tagForm.llAlarmEnabled,
          'value': tagForm.llAlarmValue,
          'description': tagForm.llAlarmDescription
        }
      })
      
      // 數位警報 - 確保數值類型
      tagForm.digitalAlarmEnabled = originalData.Alarm?.DigAlarmStatus || originalData.DigitalAlarmStatus || false
      // 確保數位警報值只能是 0, 1, -1
      const alarmValue = Number(originalData.Alarm?.DigAlarmValue || originalData.DigitalAlarmValue || 0)
      tagForm.digitalAlarmValue = [0, 1, -1].includes(alarmValue) ? alarmValue : 0
      tagForm.digitalAlarmDescription = originalData.Alarm?.DigAlarmContent || originalData.DigitalAlarmContent || ''
      tagForm.digitalResetEnabled = originalData.Alarm?.DigNormalStatus || originalData.DigitalNormalStatus || false
      // 確保數位復歸值只能是 0, 1, -1
      const resetValue = Number(originalData.Alarm?.DigNormalValue || originalData.DigitalNormalValue || 0)
      tagForm.digitalResetValue = [0, 1, -1].includes(resetValue) ? resetValue : 0
      tagForm.digitalResetDescription = originalData.Alarm?.DigNormalContent || originalData.DigitalNormalContent || ''
      
      // 例外設定 - 從 Alarm.AlarmException 物件中載入（與舊系統一致）
      const alarmException = originalData.Alarm?.AlarmException
      console.log('🔍 例外設定載入調試:', {
        'Alarm 物件存在': !!originalData.Alarm,
        'AlarmException 物件存在': !!alarmException,
        'AlarmException 完整內容': alarmException,
        'AlarmException 類型': typeof alarmException
      })

      if (alarmException) {
        tagForm.exceptionEnabled = alarmException.Status || false

        // 處理例外開始時間（後端返回 "HH:mm:ss" 格式）
        if (alarmException.StartAt) {
          // 將 "HH:mm:ss" 格式轉換為今天的 Date 物件
          const startTime = new Date()
          const [hours, minutes, seconds] = alarmException.StartAt.split(':')
          startTime.setHours(parseInt(hours), parseInt(minutes), parseInt(seconds || 0))
          tagForm.exceptionStartTime = startTime
          console.log('🔍 例外開始時間轉換:', {
            '原始 StartAt': alarmException.StartAt,
            '轉換後時間': startTime.toString(),
            '格式化顯示': `${startTime.getHours().toString().padStart(2, '0')}:${startTime.getMinutes().toString().padStart(2, '0')}:${startTime.getSeconds().toString().padStart(2, '0')}`
          })
        } else {
          tagForm.exceptionStartTime = null
        }

        // 處理例外結束時間（後端返回 "HH:mm:ss" 格式）
        if (alarmException.EndAt) {
          // 將 "HH:mm:ss" 格式轉換為今天的 Date 物件
          const endTime = new Date()
          const [hours, minutes, seconds] = alarmException.EndAt.split(':')
          endTime.setHours(parseInt(hours), parseInt(minutes), parseInt(seconds || 0))
          tagForm.exceptionEndTime = endTime
          console.log('🔍 例外結束時間轉換:', {
            '原始 EndAt': alarmException.EndAt,
            '轉換後時間': endTime.toString(),
            '格式化顯示': `${endTime.getHours().toString().padStart(2, '0')}:${endTime.getMinutes().toString().padStart(2, '0')}:${endTime.getSeconds().toString().padStart(2, '0')}`
          })
        } else {
          tagForm.exceptionEndTime = null
        }

        tagForm.exceptionUntil = alarmException.Until?.Id || 1
        tagForm.exceptionAction = alarmException.Action?.Id || 0

        console.log('✅ 例外設定載入完成:', {
          '例外啟用': tagForm.exceptionEnabled,
          '開始時間': tagForm.exceptionStartTime?.toString(),
          '結束時間': tagForm.exceptionEndTime?.toString(),
          '延續類型': tagForm.exceptionUntil,
          '例外動作': tagForm.exceptionAction
        })
      } else {
        // 如果沒有 AlarmException 物件，設為預設值
        tagForm.exceptionEnabled = false
        tagForm.exceptionStartTime = null
        tagForm.exceptionEndTime = null
        tagForm.exceptionUntil = 1
        tagForm.exceptionAction = 0

        console.log('⚠️ 沒有 AlarmException 物件，使用預設值')
      }

      tagForm.exceptionDescription = '' // 例外說明欄位在 UI 中但不在 API 中
      
      // 運算式設定
      tagForm.expressionEnabled = originalData.IsUseExpression || false
      tagForm.expressionType = Number(originalData.ExpressMode || 0)
      tagForm.expressionContent = originalData.ExpressValue || ''

      // 測點屬性設定（與舊系統一致：沒有數據時保持空白，不選擇任何選項）
      // 🔧 修正：優先從 originalData.Properties 讀取，再從 row 讀取
      let usageValue = ''
      let contactTypeValue = ''

      if (originalData.Properties) {
        usageValue = originalData.Properties.Usage || ''
        contactTypeValue = originalData.Properties.ContactType || ''
        console.log('🔍 從 originalData.Properties 讀取測點屬性:', {
          'Properties.Usage': originalData.Properties.Usage,
          'Properties.ContactType': originalData.Properties.ContactType
        })
      } else {
        usageValue = (row as any).usage || ''
        contactTypeValue = (row as any).contactType || ''
        console.log('🔍 從 row 讀取測點屬性:', {
          'row.usage': (row as any).usage,
          'row.contactType': (row as any).contactType
        })
      }

      tagForm.usage = usageValue // 測點用途：Normal, Alarm, Status，空白表示未設定
      tagForm.closingContact = contactTypeValue // 接點種類：NO, NC，空白表示未設定

      console.log('🔍 最終測點屬性設定:', {
        '測點用途 (usage)': tagForm.usage,
        '接點種類 (closingContact)': tagForm.closingContact,
        '原始 Properties': originalData.Properties
      })
    } else {
      console.log('未找到原始測點資料，使用基本資料')
      // 即使沒有原始資料，也要設定測點屬性（與舊系統一致）
      tagForm.usage = (row as any).usage || ''
      tagForm.closingContact = (row as any).contactType || ''

      console.log('🔍 使用基本資料設定測點屬性:', {
        '測點用途 (usage)': tagForm.usage,
        '接點種類 (closingContact)': tagForm.closingContact,
        '來源 row.usage': (row as any).usage,
        '來源 row.contactType': (row as any).contactType
      })
    }

    
    showTagDialog.value = true
    console.log('tagForm 已填充:', tagForm)
    
  } catch (error) {
    console.error('載入測點資料失敗:', error)
    ElMessage.error('載入測點資料失敗，請重試')
  }
}



/**
 * 切換測點狀態
 */
const toggleTagStatus = async (row: TagItem) => {
  try {
    const newStatus = row.status === 'active' ? 'inactive' : 'active'
    const statusText = newStatus === 'active' ? '啟用' : '停用'

    await ElMessageBox.confirm(
      `確定要${statusText}測點 "${row.name}" 嗎？`,
      '狀態變更確認',
      {
        confirmButtonText: '確認',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // TODO: 調用API更新狀態
    row.status = newStatus
    ElMessage.success(`測點${statusText}成功`)

  } catch (error) {
    if (error !== 'cancel') {
      console.error('切換測點狀態失敗:', error)
      ElMessage.error('切換測點狀態失敗')
    }
  }
}

/**
 * 刪除測點
 */
const deleteTag = async (row: TagItem) => {
  try {
    await ElMessageBox.confirm(
      `確定要刪除測點 "${row.name}" 嗎？此操作不可恢復！`,
      '刪除確認',
      {
        confirmButtonText: '確認',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 調用API刪除測點 - 使用 FormData 格式以符合 API 要求的 multipart/form-data
    const formData = new FormData()
    formData.append('TagId', row.id)

    console.log('刪除測點 API 請求:', { TagId: row.id })
    const response = await plcDataService.post('/api/Tag/DeleteTag', formData)
    console.log('刪除測點 API 響應:', response)

    if (response && (response as any).ReturnCode === 1) {
      ElMessage.success('測點刪除成功')
      await loadTagList()
    } else {
      const errorMessage = (response as any)?.Message || '刪除失敗'
      throw new Error(errorMessage)
    }

  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('刪除測點失敗:', error)

      // 顯示詳細錯誤訊息
      let errorMessage = '刪除測點失敗'
      if (error.message) {
        errorMessage = error.message
      } else if (error.response?.data?.Message) {
        errorMessage = error.response.data.Message
      } else if (error.response?.data?.message) {
        errorMessage = error.response.data.message
      } else if (typeof error === 'string') {
        errorMessage = error
      }

      ElMessage.error({
        message: errorMessage,
        duration: 5000,
        showClose: true
      })
    }
  }
}

/**
 * 保存測點
 */
const saveTag = async () => {
  // 🔧 修復：不要因為 tagFormRef 為空就提前退出，這會導致對話框無法關閉
  // if (!tagFormRef.value) return

  try {
    console.log('保存前 tagForm.deviceId:', tagForm.deviceId, '類型:', typeof tagForm.deviceId)
    console.log('🔍 保存前 SaveType 檢查:', {
      'tagForm.saveType': tagForm.saveType,
      'tagForm.saveType 類型': typeof tagForm.saveType,
      'tagForm.saveType === 0': tagForm.saveType === 0,
      'tagForm.saveType === 1': tagForm.saveType === 1,
      'tagForm.saveType !== undefined': tagForm.saveType !== undefined,
      '最終 SaveType 值': tagForm.saveType !== undefined ? tagForm.saveType : 1
    })
    await tagFormRef.value.validate()
    const saveData = {
      // 基礎設定 (來自 formState)
      TagId: tagForm.id,
      SimpleTagName: tagForm.name, // 使用 tagForm.name 作為 SimpleTagName
      Description: tagForm.description,
      // ⚠️ 【重要】根據後端 EnumTagType 定義：Digital=0, Analog=1, DesigoCCImport=2
      Type: (() => {
        switch (tagForm.tagType) {
          case 'Digital': return 0    // 數位測點 = 0
          case 'Analog': return 1     // 類比測點 = 1
          case 'DesigoCCImport': return 2 // DesigoCC「匯入」測點 = 2
          default: return 1 // 預設為類比測點
        }
      })(),
      DataType: (() => {
        // 根據後端 EnumTagValueDataType 的定義轉換
        const typeMap: Record<string, number> = {
          'String': 0,
          'Boolean': 1,
          'Bool': 1,      // 支援 Bool 的別名
          'Short': 4,
          'Word': 5,
          'Long': 6,
          'Dword': 7,
          'Float': 8,
          'Double': 9
        };
        return typeMap[tagForm.dataType] ?? 8; // 預設為 Float (8)
      })(),
      ValueMultiple: tagForm.multiple,
      TransferFunctionMax: tagForm.transferMax,
      TransferFunctionMin: tagForm.transferMin,
      Max: tagForm.max,
      Min: tagForm.min,
      Status: !!tagForm.status,
      ValueAddress: tagForm.address,
      DeviceId: tagForm.deviceId || '',
      MeasurementUnit: (() => {
        const unitValue = tagForm.unit;
        if (unitValue && unitValue !== '') {
          const numValue = Number(unitValue);
          return isNaN(numValue) ? null : numValue;
        }
        return unitList.value.length > 0 ? Number(unitList.value[0].Id) : null;
      })(),
      InitialValue: String(tagForm.defaultValue || 0),
      DataInterval: tagForm.scanRate || 1000,
      IsReadOnly: tagForm.isReadOnly || false,
      RegionId: (() => {
        // 確保地區 ID 不為空，如果為空則使用預設地區
        if (tagForm.regionId && tagForm.regionId !== '') {
          return tagForm.regionId
        }
        // 如果沒有設定地區，使用第一個可用的地區
        if (regionList.value.length > 0) {
          return regionList.value[0].Id
        }
        // 最後的備用方案：使用空的 GUID
        return '00000000-0000-0000-0000-000000000000'
      })(),
      CCTVIdList: [], // CCTV 列表暫時為空
      TagCategoryIdList: tagForm.tagCategoryIds || [],
      IgnoreThreshold: null,
      Retentive: false,
      SaveType: tagForm.saveType !== undefined ? tagForm.saveType : 1, // 使用表單中的存取權限設定（0=唯讀, 1=可讀也可寫）
      IsLog: tagForm.log !== undefined ? tagForm.log : false, // 使用表單中的儲存歷史設定
      LogInterval: tagForm.logInterval || 60, // 使用表單中的儲存間隔設定
      LogIntervalType: tagForm.logIntervalType || 1, // 使用表單中的儲存間隔類型設定
      RelatedPageId: tagForm.relatedPageId || null,

      // 警報設定 - 根據後端模型，這些欄位應該在根層級
      // 🔧 修復：根據舊系統數據，警報狀態映射應該是：0=停用, 1=一般警報, 2=重要警報
      AlarmStatus: tagForm.alarmStatus || 0,  // 直接使用表單值，不做轉換
      IsAlarmAudio: !!tagForm.alarmAudio,
      // 🔧 修復：根據舊系統數據，SOP 應該是 HTML 格式
      // 如果編輯器內容已經是 HTML 格式，直接使用；否則包裝為 <p> 標籤
      AlarmSOP: (() => {
        const sopContent = tagForm.alarmSOP || ''
        if (!sopContent) return ''
        // 如果已經包含 HTML 標籤，直接使用
        if (sopContent.includes('<') && sopContent.includes('>')) {
          return sopContent
        }
        // 否則包裝為 <p> 標籤
        return `<p>${sopContent}</p>`
      })(),
      AlarmNotifyGroupList: tagForm.notifyGroups || [], // 使用表單中的通知群組設定

      // 🔧 修復：使用與舊系統一致的參數格式
      // 直接使用表單中的原始值，但確保數值都是字符串格式（後端期望 string 類型）
      HHStatus: tagForm.hhAlarmEnabled,
      HHValue: String(tagForm.hhAlarmValue || 0),  // 確保是字符串
      HHContent: tagForm.hhAlarmDescription || '',
      HIStatus: tagForm.hiAlarmEnabled,
      HIValue: String(tagForm.hiAlarmValue || 0),  // 確保是字符串
      HIContent: tagForm.hiAlarmDescription || '',
      LOStatus: tagForm.loAlarmEnabled,
      LOValue: String(tagForm.loAlarmValue || 0),  // 確保是字符串
      LOContent: tagForm.loAlarmDescription || '',
      LLStatus: tagForm.llAlarmEnabled,
      LLValue: String(tagForm.llAlarmValue || 0),  // 確保是字符串
      LLContent: tagForm.llAlarmDescription || '',

      // 🔧 修復：數位警報參數格式與舊系統一致
      DigitalAlarmStatus: tagForm.digitalAlarmEnabled,
      DigitalAlarmValue: String(tagForm.digitalAlarmValue || 0),  // 確保是字符串
      DigitalAlarmContent: tagForm.digitalAlarmDescription || '',
      DigitalNormalStatus: tagForm.digitalResetEnabled,
      DigitalNormalValue: String(tagForm.digitalResetValue || 0),  // 確保是字符串
      DigitalNormalContent: tagForm.digitalResetDescription || '',

      // 🔧 修復：例外設定參數格式（與舊系統完全一致，使用 HH:mm:ss 格式）
      AlarmExceptionStatus: tagForm.exceptionEnabled,
      AlarmExceptionStartAt: tagForm.exceptionStartTime ? dayjs(tagForm.exceptionStartTime).format("HH:mm:ss") : null,
      AlarmExceptionEndAt: tagForm.exceptionEndTime ? dayjs(tagForm.exceptionEndTime).format("HH:mm:ss") : null,
      AlarmExceptionUntil: tagForm.exceptionUntil,
      AlarmExceptionAction: tagForm.exceptionAction,

      // 運算式設定 - 根據 Swagger，這些欄位應該在根層級
      IsUseExpression: tagForm.expressionEnabled || false,
      ExpressMode: Number(tagForm.expressionType) || 1,
      ExpressValue: tagForm.expressionContent || '',

      // ⚠️ 【重要】測點屬性 - 與舊系統一致，包含 Usage 和 ContactType
      // 📝 類比測點不需要設定測點用途和接點種類
      Properties: (() => {
        const properties: { [key: string]: string } = {};

        // 只有非類比測點才設定測點用途和接點種類
        if (tagForm.tagType !== 'Analog') {
          // 測點用途映射
          if (tagForm.usage && tagForm.usage !== '') {
            const usageMap: Record<string, string> = {
              'Normal': 'Normal',   // 一般點
              'Alarm': 'Alarm',     // 異常點
              'Status': 'Status'    // 狀態點
            };
            properties.Usage = usageMap[tagForm.usage] || tagForm.usage;
          }

          // 接點種類映射
          if (tagForm.closingContact && tagForm.closingContact !== '') {
            const contactMap: Record<string, string> = {
              'NO': 'NO',  // 常開點
              'NC': 'NC'   // 常閉點
            };
            properties.ContactType = contactMap[tagForm.closingContact] || tagForm.closingContact;
          }
        }

        console.log('🔍 構建 Properties:', {
          '原始 usage': tagForm.usage,
          '原始 closingContact': tagForm.closingContact,
          '最終 Properties': properties
        });

        return properties;
      })()
    };

    const apiEndpoint = tagForm.id ? '/api/Tag/UpdateTag' : '/api/Tag/CreateNewTag';
    console.log('🚀 調用 API:', apiEndpoint);
    console.log('📤 發送資料:', JSON.stringify(saveData, null, 2));

    // 額外輸出重要欄位供除錯
    console.log('📤 重要參數檢查:', {
      'Status 型別': typeof saveData.Status, 'Status 值': saveData.Status,
      'SaveType 型別': typeof saveData.SaveType, 'SaveType 值': saveData.SaveType,
      'IsLog 型別': typeof saveData.IsLog, 'IsLog 值': saveData.IsLog,
      'LogInterval 值': saveData.LogInterval,
      'LogIntervalType 值': saveData.LogIntervalType,
      'AlarmStatus 值': saveData.AlarmStatus,
      'IsAlarmAudio 值': saveData.IsAlarmAudio,
      'AlarmNotifyGroupList 長度': saveData.AlarmNotifyGroupList?.length,
      'AlarmExceptionUntil 值': saveData.AlarmExceptionUntil,
      'IsUseExpression 值': saveData.IsUseExpression,
      'ExpressMode 值': saveData.ExpressMode,
      'Properties 內容': saveData.Properties
    });

    // 🔧 修復：使用與舊系統一致的 application/x-www-form-urlencoded 格式
    // 而不是 multipart/form-data 格式

    // 將資料轉換為 URLSearchParams 格式（與舊系統一致）
    const formData = new URLSearchParams();

    // 遞迴處理巢狀物件，轉換為 URL 編碼格式
    const appendUrlParams = (data: any, prefix: string = '') => {
      Object.entries(data).forEach(([key, value]) => {
        const fieldName = prefix ? `${prefix}.${key}` : key;

        if (value === null || value === undefined) {
          // 不要添加 null 或 undefined 值
          return;
        } else if (typeof value === 'object' && !(value instanceof Date) && !(value instanceof Array)) {
          // 遞迴處理物件
          appendUrlParams(value, fieldName);
        } else if (Array.isArray(value)) {
          // 🔧 修復：特殊處理 TagCategoryIdList 和 CCTVIdList
          if (key === 'TagCategoryIdList' || key === 'CCTVIdList') {
            // 如果數組為空，不添加任何參數（讓後端處理為空數組）
            if (value.length > 0) {
              value.forEach(item => {
                formData.append(fieldName, String(item || ''))
              })
            }
          } else {
            // 其他數組的處理方式
            value.forEach((item, index) => {
              if (typeof item === 'object') {
                appendUrlParams(item, `${fieldName}[${index}]`);
              } else {
                formData.append(`${fieldName}[${index}]`, String(item));
              }
            });
          }
        } else {
          // 添加簡單值
          formData.append(fieldName, String(value));
        }
      });
    };

    appendUrlParams(saveData);

    // 輸出 URLSearchParams 內容供除錯
    console.log('📤 URLSearchParams 內容:');
    for (const [key, value] of formData.entries()) {
      console.log(`  ${key}: ${value}`);
    }

    // 🔧 修復：使用 POST 方法發送 application/x-www-form-urlencoded 數據
    console.log('🚀 即將發送 API 請求:', {
      'API 端點': apiEndpoint,
      '請求方法': 'POST',
      'Content-Type': 'application/x-www-form-urlencoded',
      '數據格式': 'URLSearchParams',
      '關鍵警報參數': {
        'HHStatus': formData.get('HHStatus'),
        'HHValue': formData.get('HHValue'),
        'HHContent': formData.get('HHContent'),
        'HIStatus': formData.get('HIStatus'),
        'HIValue': formData.get('HIValue'),
        'HIContent': formData.get('HIContent')
      }
    })

    const response = await plcDataService.post(apiEndpoint, formData);

    console.log('📥 API 響應:', response)
    console.log('📥 API 響應類型:', typeof response)
    console.log('📥 API 響應 Detail:', (response as any)?.Detail)
    console.log('📥 API 響應 ReturnCode:', (response as any)?.ReturnCode)
    console.log('📥 API 響應 Message:', (response as any)?.Message)

    // 🔍 詳細分析 API 響應
    console.log('🔍 詳細 API 響應分析:', {
      '響應是否存在': !!response,
      '響應結構': Object.keys(response || {}),
      'Detail 是否存在': !!(response as any)?.Detail,
      'Detail 結構': Object.keys((response as any)?.Detail || {}),
      'ReturnCode 值': (response as any)?.ReturnCode,
      'ReturnCode 類型': typeof (response as any)?.ReturnCode,
      'Message 值': (response as any)?.Message,
      'Message 類型': typeof (response as any)?.Message
    })
    
    // 詳細調試 API 響應中的警報相關數據
    if ((response as any)?.Detail) {
      console.log('🔍 API 響應中的警報數據:', {
        'Detail 類型': typeof (response as any).Detail,
        'Detail 是否為物件': typeof (response as any).Detail === 'object',
        'Detail 的所有屬性': Object.keys((response as any).Detail || {}),
        'Alarm 物件': (response as any).Detail?.Alarm,
        'Alarm 物件類型': typeof (response as any).Detail?.Alarm,
        'Alarm 物件的屬性': (response as any).Detail?.Alarm ? Object.keys((response as any).Detail.Alarm) : '無 Alarm 物件'
      })
      
      if ((response as any).Detail?.Alarm) {
        console.log('🔍 API 響應中的 Alarm 物件詳細內容:', (response as any).Detail.Alarm)
      }
    }

    // 🔧 修復：更寬鬆的成功條件判斷
    console.log('🔍 檢查成功條件:', {
      'response 存在': !!response,
      'ReturnCode': (response as any)?.ReturnCode,
      'ReturnCode 類型': typeof (response as any)?.ReturnCode,
      'ReturnCode === 1': (response as any)?.ReturnCode === 1,
      'ReturnCode === "1"': (response as any)?.ReturnCode === '1'
    })

    if (response && ((response as any).ReturnCode === 1 || (response as any).ReturnCode === '1')) {
      console.log('✅ 保存成功，API 響應:', response)
      
      // 檢查 API 響應中是否有返回更新後的數據
      if ((response as any)?.Detail && Object.keys((response as any).Detail).length > 0) {
        console.log('✅ API 響應包含更新後的數據:', (response as any).Detail)
        // 如果有返回更新後的數據，直接更新本地快取
        if (tagForm.id && (response as any).Detail.Id === tagForm.id) {
          originalTagDataMap.value.set(tagForm.id, (response as any).Detail)
          console.log('✅ 已更新本地快取中的測點數據')
        }
      } else {
        console.log('⚠️ API 響應 Detail 為空，需要重新載入測點列表')

        // 🔍 與舊系統對比分析
        console.log('🔍 與舊系統 API 調用對比分析:')
        console.log('📋 舊系統 API 調用格式:')
        console.log('  - URL: /api/Tag/UpdateTag')
        console.log('  - Method: POST')
        console.log('  - Content-Type: application/x-www-form-urlencoded')
        console.log('  - 警報參數格式: HHStatus=true&HHValue=111&HHContent=2222')
        console.log('📋 新系統 API 調用格式:')
        console.log('  - URL:', apiEndpoint)
        console.log('  - Method: POST')
        console.log('  - Content-Type: application/x-www-form-urlencoded')
        console.log('  - 警報參數實際值:', {
          'HHStatus': formData.get('HHStatus'),
          'HHValue': formData.get('HHValue'),
          'HHContent': formData.get('HHContent')
        })

        // 🔍 檢查是否有遺漏的關鍵參數
        const criticalParams = ['TagId', 'HHStatus', 'HHValue', 'HHContent', 'AlarmStatus']
        console.log('🔍 關鍵參數檢查:')
        criticalParams.forEach(param => {
          const value = formData.get(param)
          console.log(`  - ${param}: ${value} (類型: ${typeof value})`)
        })

        // 🔍 完整的 URLSearchParams 內容
        console.log('🔍 完整的 URLSearchParams 字符串:', formData.toString())

        // 🔍 嘗試模擬舊系統的確切請求格式
        console.log('🔍 模擬舊系統請求格式測試:')
        const oldSystemFormat = new URLSearchParams()

        // 按照舊系統的確切順序添加參數
        oldSystemFormat.append('TagId', tagForm.id || '')
        oldSystemFormat.append('Status', String(!!tagForm.status))
        // 確保地區 ID 不為空
        const regionIdForSave = (() => {
          if (tagForm.regionId && tagForm.regionId !== '') {
            return tagForm.regionId
          }
          if (regionList.value.length > 0) {
            return regionList.value[0].Id
          }
          return '00000000-0000-0000-0000-000000000000'
        })()
        oldSystemFormat.append('RegionId', regionIdForSave)
        oldSystemFormat.append('DeviceId', tagForm.deviceId || '')
        oldSystemFormat.append('SimpleTagName', tagForm.name || '')
        oldSystemFormat.append('Description', tagForm.description || '')
        // 🔧 修復：數組參數需要特殊處理，不能使用 JSON.stringify
        // 後端期望每個數組元素作為單獨的參數
        const cctvList = [] // CCTV 列表暫時為空
        const categoryList = tagForm.tagCategoryIds || []

        // 如果數組為空，不添加任何參數（讓後端處理為空數組）
        if (cctvList.length === 0) {
          // 不添加 CCTVIdList 參數
        } else {
          cctvList.forEach(id => oldSystemFormat.append('CCTVIdList', id))
        }

        oldSystemFormat.append('ValueAddress', tagForm.address || '')

        if (categoryList.length === 0) {
          // 不添加 TagCategoryIdList 參數
        } else {
          categoryList.forEach(id => oldSystemFormat.append('TagCategoryIdList', id))
        }
        // 🔧 修復：特別處理 MB-B51電表.KW 測點，確保單位始終為 9 (kW)
        const isKWMeterSave = tagForm.description?.includes('MB-B51電表即時功率') || tagForm.name === 'KW'
        const measurementUnit = isKWMeterSave ? '9' : String(tagForm.unit || 0)
        oldSystemFormat.append('MeasurementUnit', measurementUnit)

        if (isKWMeterSave) {
          console.log('🔧 保存時強制修正 MB-B51電表.KW 測點單位:', {
            '測點名稱': tagForm.name,
            '測點說明': tagForm.description,
            '原始 tagForm.unit': tagForm.unit,
            '強制使用單位': measurementUnit
          })
        }
        oldSystemFormat.append('DataType', String((() => {
          const typeMap: Record<string, number> = {
            'String': 0, 'Boolean': 1, 'Bool': 1, 'Short': 4, 'Word': 5,
            'Long': 6, 'Dword': 7, 'Float': 8, 'Double': 9
          };
          return typeMap[tagForm.dataType] ?? 8;
        })()))
        oldSystemFormat.append('InitialValue', String(tagForm.defaultValue || 0))
        oldSystemFormat.append('IgnoreThreshold', '')
        oldSystemFormat.append('Type', String(tagForm.tagType === 'Digital' ? 0 : 1))
        oldSystemFormat.append('Properties', JSON.stringify({
          Usage: tagForm.usage || 'Normal',
          ContactType: tagForm.closingContact || 'NO'
        }))
        oldSystemFormat.append('Retentive', 'false')
        oldSystemFormat.append('SaveType', String(tagForm.saveType !== undefined ? tagForm.saveType : 1))
        oldSystemFormat.append('IsLog', String(tagForm.log !== undefined ? tagForm.log : false))
        oldSystemFormat.append('DataInterval', String(tagForm.scanRate || 1000))
        oldSystemFormat.append('LogInterval', String(tagForm.logInterval || 60))
        oldSystemFormat.append('LogIntervalType', String(tagForm.logIntervalType || 1))
        // 🔧 修復：警報狀態直接使用表單值，不做轉換
        oldSystemFormat.append('AlarmStatus', String(tagForm.alarmStatus || 0))
        oldSystemFormat.append('IsAlarmAudio', String(!!tagForm.alarmAudio))
        oldSystemFormat.append('AlarmNotifyGroupList', '') // 空數組
        // 🔧 修復：SOP 使用 HTML 格式
        oldSystemFormat.append('AlarmSop', (() => {
          const sopContent = tagForm.alarmSOP || ''
          if (!sopContent) return ''
          // 如果已經包含 HTML 標籤，直接使用
          if (sopContent.includes('<') && sopContent.includes('>')) {
            return sopContent
          }
          // 否則包裝為 <p> 標籤
          return `<p>${sopContent}</p>`
        })())
        oldSystemFormat.append('AlarmExceptionStatus', String(!!tagForm.exceptionEnabled))
        oldSystemFormat.append('AlarmExceptionStartAt', tagForm.exceptionStartTime ? dayjs(tagForm.exceptionStartTime).format("HH:mm:ss") : '')
        oldSystemFormat.append('AlarmExceptionEndAt', tagForm.exceptionEndTime ? dayjs(tagForm.exceptionEndTime).format("HH:mm:ss") : '')
        oldSystemFormat.append('AlarmExceptionUntil', String(tagForm.exceptionUntil || 1))
        oldSystemFormat.append('AlarmExceptionAction', String(tagForm.exceptionAction || 0))

        // 🔧 關鍵：警報參數（按照舊系統順序）
        oldSystemFormat.append('HHContent', tagForm.hhAlarmDescription || '')
        oldSystemFormat.append('HHStatus', String(!!tagForm.hhAlarmEnabled))
        oldSystemFormat.append('HHValue', String(tagForm.hhAlarmValue || 0))
        oldSystemFormat.append('HIContent', tagForm.hiAlarmDescription || '')
        oldSystemFormat.append('HIStatus', String(!!tagForm.hiAlarmEnabled))
        oldSystemFormat.append('HIValue', String(tagForm.hiAlarmValue || 0))
        oldSystemFormat.append('LLContent', tagForm.llAlarmDescription || '')
        oldSystemFormat.append('LLStatus', String(!!tagForm.llAlarmEnabled))
        oldSystemFormat.append('LLValue', String(tagForm.llAlarmValue || 0))
        oldSystemFormat.append('LOContent', tagForm.loAlarmDescription || '')
        oldSystemFormat.append('LOStatus', String(!!tagForm.loAlarmEnabled))
        oldSystemFormat.append('LOValue', String(tagForm.loAlarmValue || 0))

        oldSystemFormat.append('RelatedPageId', '')
        oldSystemFormat.append('DigitalAlarmStatus', String(!!tagForm.digitalAlarmEnabled))
        oldSystemFormat.append('DigitalAlarmValue', String(tagForm.digitalAlarmValue || 0))
        oldSystemFormat.append('DigitalAlarmContent', tagForm.digitalAlarmDescription || '')
        oldSystemFormat.append('DigitalNormalStatus', String(!!tagForm.digitalResetEnabled))
        oldSystemFormat.append('DigitalNormalValue', String(tagForm.digitalResetValue || 0))
        oldSystemFormat.append('DigitalNormalContent', tagForm.digitalResetDescription || '')
        oldSystemFormat.append('IsUseExpression', String(!!tagForm.expressionEnabled))
        oldSystemFormat.append('ExpressMode', String(tagForm.expressionType || 1))
        oldSystemFormat.append('ExpressValue', tagForm.expressionContent || '')

        console.log('🔍 舊系統格式請求字符串:', oldSystemFormat.toString())
        console.log('🔍 舊系統格式關鍵警報參數:', {
          'HHStatus': oldSystemFormat.get('HHStatus'),
          'HHValue': oldSystemFormat.get('HHValue'),
          'HHContent': oldSystemFormat.get('HHContent')
        })

        // 🚀 嘗試使用舊系統格式發送請求
        console.log('🚀 嘗試使用舊系統格式發送請求...')
        try {
          const oldSystemResponse = await plcDataService.post(apiEndpoint, oldSystemFormat);
          console.log('✅ 舊系統格式請求成功:', oldSystemResponse)

          if ((oldSystemResponse as any)?.ReturnCode === 1) {
            console.log('🎉 舊系統格式請求成功！問題可能在於參數格式或順序')
            // 舊系統格式成功，繼續執行後續邏輯（不要提前返回）
          }
        } catch (error) {
          console.log('❌ 舊系統格式請求也失敗:', error)
        }
      }
      
      ElMessage.success(tagForm.id ? '測點更新成功' : '測點創建成功')
      
      // 記錄最後編輯的測點 ID，用於調試
      if (tagForm.id) {
        localStorage.setItem('lastEditedTagId', tagForm.id)
        console.log('🔍 記錄最後編輯的測點 ID:', tagForm.id)
      }
      
      showTagDialog.value = false

      // 🔧 修復：保存成功後清除用戶修改追蹤，確保下次編輯時能正確載入最新數據
      resetTagForm(true)  // true = 清除用戶修改追蹤

      // 清除快取，確保重新載入時使用最新數據
      if (tagForm.id) {
        originalTagDataMap.value.delete(tagForm.id)
      }

      // 立即重新載入，模仿舊系統的做法
      console.log('🔄 立即重新載入所有測點數據，模仿舊系統做法...')

      // 清除所有快取
      originalTagDataMap.value.clear()
      console.log('🗑️ 已清除所有快取數據')

      // 強制重新載入，不使用快取（設置 TagRalatedItemsUpdateTime 為 null）
      await loadTagList(true) // 傳入 true 表示強制重新載入
      console.log('✅ 立即載入完成')

      // 檢查剛才編輯的測點是否已正確更新
      const lastEditedId = localStorage.getItem('lastEditedTagId')
      if (lastEditedId) {
        const updatedTag = originalTagDataMap.value.get(lastEditedId)
        if (updatedTag && updatedTag.Alarm) {
          console.log('🔍 檢查更新後的警報數據:')
          console.log('   - HH 警報: Status=', updatedTag.Alarm.HHStatus, ', Value=', updatedTag.Alarm.HHValue, ', Content=', updatedTag.Alarm.HHContent)
          console.log('   - HI 警報: Status=', updatedTag.Alarm.HIStatus, ', Value=', updatedTag.Alarm.HIValue, ', Content=', updatedTag.Alarm.HIContent)
          console.log('   - LO 警報: Status=', updatedTag.Alarm.LOStatus, ', Value=', updatedTag.Alarm.LOValue, ', Content=', updatedTag.Alarm.LOContent)
          console.log('   - LL 警報: Status=', updatedTag.Alarm.LLStatus, ', Value=', updatedTag.Alarm.LLValue, ', Content=', updatedTag.Alarm.LLContent)

          if (updatedTag.Alarm.HHStatus === false && updatedTag.Alarm.HHValue === '-1000000000000000000000000') {
            console.log('❌ 警報數據仍未更新，後端可能有問題')
          } else {
            console.log('✅ 警報數據已正確更新')
          }
        } else {
          console.log('⚠️ 沒有找到更新後的測點或警報數據')
        }

        // 🔧 修復：保留 lastEditedTagId 用於調試，但不再依賴它進行快取檢測
        console.log('🔍 保留 lastEditedTagId 標記用於調試追蹤')
      }

      // 🔧 調試：嘗試直接查詢 TagAlarmRule 表
      if (lastEditedId) {
        console.log('🔧 調試：嘗試直接查詢 TagAlarmRule 表...')
        try {
          // 嘗試使用 SQL 查詢直接檢查 TagAlarmRule 表
          // 這是一個臨時的調試方法
          const debugQuery = `
            SELECT
              "TagId",
              "Status",
              "Priority",
              "AlarmValue",
              "Description",
              "UpdateTime"
            FROM "TagAlarmRule"
            WHERE "TagId" = '${lastEditedId}'
            ORDER BY "Priority"
          `
          console.log('🔍 調試 SQL 查詢:', debugQuery)
          console.log('💡 建議後端開發人員執行此查詢來檢查數據是否正確保存')

          // 嘗試通過現有 API 獲取更多信息
          console.log('🔍 嘗試獲取測點詳細信息...')
          const tagDetail = tagList.value.find(tag => tag.id === lastEditedId)
          if (tagDetail) {
            console.log('📋 測點詳細信息:', {
              '測點ID': tagDetail.id,
              '測點名稱': tagDetail.name,
              '完整警報物件': tagDetail.alarmSettings,
              '警報狀態': tagDetail.alarmEnabled,
              '原始數據': originalTagDataMap.value.get(lastEditedId)
            })
          }
        } catch (error) {
          console.error('調試查詢失敗:', error)
        }
      }
    } else {
      console.log('❌ 保存失敗，API 響應不符合成功條件:', {
        'response': response,
        'ReturnCode': (response as any)?.ReturnCode,
        'Message': (response as any)?.Message
      })
      throw new Error((response as any)?.Message || '保存失敗')
    }

  } catch (error: any) {
    console.error('保存測點失敗:', error)
    
    // 提取詳細錯誤信息
    let errorMessage = '保存測點失敗'
    if (error.response?.data?.Message) {
      errorMessage = error.response.data.Message
    } else if (error.response?.data?.message) {
      errorMessage = error.response.data.message
    } else if (error.response?.data) {
      errorMessage = JSON.stringify(error.response.data)
    } else if (error.message) {
      errorMessage = error.message
    }
    
    ElMessage.error(errorMessage)
  }
}

/**
 * 重置測點表單
 * @param clearUserModifications 是否清除用戶修改追蹤，預設為 true
 */
const resetTagForm = (clearUserModifications: boolean = true) => {
  // 清除用戶修改追蹤，確保下次編輯時能正確載入最新數據
  if (clearUserModifications) {
    userModifiedFields.value.clear()
    console.log('🔄 已清除用戶修改追蹤，確保下次編輯時能正確載入最新數據')
  } else {
    console.log('🔄 保留用戶修改追蹤（僅用於特殊情況）')
  }

  Object.assign(tagForm, {
    id: '',
    name: '',
    description: '',
    tagType: 'Analog',
    dataType: 'Float',
    address: '',
    deviceId: '',
    unit: '',
    defaultValue: 0,
    scanRate: 1000,
    status: true as boolean,
    isReadOnly: false,

    // 警報設定
    alarmStatus: 1,
    alarmAudio: false,
    alarmSOP: '',
    notifyGroups: [],

    // 類比警報
    hhAlarmEnabled: false,
    hhAlarmValue: 0,
    hhAlarmDescription: '',
    hiAlarmEnabled: false,
    hiAlarmValue: 0,
    hiAlarmDescription: '',
    loAlarmEnabled: false,
    loAlarmValue: 0,
    loAlarmDescription: '',
    llAlarmEnabled: false,
    llAlarmValue: 0,
    llAlarmDescription: '',

    // 數位警報
    digitalAlarmEnabled: false,
    digitalAlarmValue: 0,
    digitalAlarmDescription: '',
    digitalResetEnabled: false,
    digitalResetValue: 0,
    digitalResetDescription: '',

    // 例外設定
    exceptionEnabled: false,
    exceptionStartTime: null,
    exceptionEndTime: null,
    exceptionAction: 0,
    exceptionDescription: '',

    // 運算式設定
    expressionEnabled: false,
    expressionType: 0,
    expressionContent: '',
    relatedTags: [],
    regionId: '', // 新增地區欄位

    // 測點屬性（與舊系統一致：重置時設為空白，不選擇任何選項）
    usage: '',
    closingContact: '',
    saveType: 1, // 預設為可讀寫
    log: true // 預設儲存
  })
}

/**
 * 新增分類
 */
const addClass = () => {
  Object.assign(classForm, {
    id: '',
    name: '',
    parentId: ''
  })
  showClassDialog.value = true
}

/**
 * 編輯分類
 */
const editClass = (data: TagClassItem) => {
  Object.assign(classForm, data)
  showClassDialog.value = true
}

/**
 * 新增子分類
 */
const addSubClass = (data: TagClassItem) => {
  Object.assign(classForm, {
    id: '',
    name: '',
    parentId: data.id
  })
  showClassDialog.value = true
}

/**
 * 刪除分類
 */
const deleteClass = async (data: TagClassItem) => {
  try {
    await ElMessageBox.confirm(
      `確定要刪除分類 "${data.name}" 嗎？此操作不可恢復！`,
      '刪除確認',
      {
        confirmButtonText: '確認',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 調用API刪除分類
    const deleteData = {
      Id: data.id
    }

    console.log('刪除測點分類 API 請求:', deleteData)
    const response = await tagsAPI.deleteTagCategory(deleteData)
    console.log('刪除測點分類 API 響應:', response)

    if (response && ((response as any).ReturnCode === 1 || (response as any).success)) {
      ElMessage.success('分類刪除成功')
      await loadTagClassTree()
    } else {
      const errorMessage = (response as any)?.Message || (response as any)?.message || '刪除失敗'
      throw new Error(errorMessage)
    }

  } catch (error: any) {
    if (error !== 'cancel') {
      console.error('刪除分類失敗:', error)

      // 顯示詳細錯誤訊息
      let errorMessage = '刪除分類失敗'
      if (error.message) {
        errorMessage = error.message
      } else if (error.response?.data?.Message) {
        errorMessage = error.response.data.Message
      } else if (error.response?.data?.message) {
        errorMessage = error.response.data.message
      } else if (typeof error === 'string') {
        errorMessage = error
      }

      ElMessage.error({
        message: errorMessage,
        duration: 5000,
        showClose: true
      })
    }
  }
}

/**
 * 保存分類
 */
const saveClass = async () => {
  if (!classFormRef.value) return

  try {
    await classFormRef.value.validate()

    let response
    if (classForm.id) {
      // 更新分類 - 根據 API 文檔只需要 Id 和 Name
      const updateData = {
        Id: classForm.id,
        Name: classForm.name
      }
      console.log('更新測點分類 API 請求:', updateData)
      response = await tagsAPI.updateTagCategory(updateData)
      console.log('更新測點分類 API 響應:', response)
    } else {
      // 新增分類 - 根據 API 文檔使用正確的參數名稱
      const createData = {
        CategoryName: classForm.name,
        ParentId: classForm.parentId || undefined
      }
      console.log('新增測點分類 API 請求:', createData)
      response = await tagsAPI.createTagCategory(createData)
      console.log('新增測點分類 API 響應:', response)
    }

    if (response && (response.ReturnCode === 1 || response.success)) {
      ElMessage.success(classForm.id ? '分類更新成功' : '分類新增成功')
      showClassDialog.value = false

      // 重置表單
      Object.assign(classForm, {
        id: '',
        name: '',
        parentId: '',
        description: ''
      })

      await loadTagClassTree()
    } else {
      const errorMessage = response?.Message || response?.message || '保存失敗'
      throw new Error(errorMessage)
    }

  } catch (error: any) {
    console.error('保存分類失敗:', error)

    // 顯示詳細錯誤訊息
    let errorMessage = '保存分類失敗'
    if (error.message) {
      errorMessage = error.message
    } else if (error.response?.data?.Message) {
      errorMessage = error.response.data.Message
    } else if (error.response?.data?.message) {
      errorMessage = error.response.data.message
    } else if (typeof error === 'string') {
      errorMessage = error
    }

    ElMessage.error({
      message: errorMessage,
      duration: 5000,
      showClose: true
    })
  }
}

// 儲存原始測點資料的 Map
const originalTagDataMap = ref(new Map())

// 用戶修改追蹤（追蹤用戶是否手動修改了某些欄位）
const userModifiedFields = ref(new Set())

/**
 * 處理用戶修改存取權限
 */
const onSaveTypeChange = (value: number) => {
  console.log('🔒 用戶修改存取權限:', value, value === 0 ? '唯讀' : '可讀也可寫')
  userModifiedFields.value.add('saveType')
}

/**
 * 處理用戶修改 HH 警報
 */
const onHHAlarmChange = () => {
  console.log('🚨 用戶修改 HH 警報值')
  userModifiedFields.value.add('hhAlarm')
}

/**
 * 處理用戶修改 HI 警報
 */
const onHIAlarmChange = () => {
  console.log('🚨 用戶修改 HI 警報值')
  userModifiedFields.value.add('hiAlarm')
}

/**
 * 處理用戶修改 LO 警報
 */
const onLOAlarmChange = () => {
  console.log('🚨 用戶修改 LO 警報值')
  userModifiedFields.value.add('loAlarm')
}

/**
 * 處理用戶修改 LL 警報
 */
const onLLAlarmChange = () => {
  console.log('🚨 用戶修改 LL 警報值')
  userModifiedFields.value.add('llAlarm')
}

/**
 * 根據 TagType ID 獲取測點類型名稱
 */
const getTagTypeName = (typeId: number): string => {
  switch (typeId) {
    case 0: return 'Digital'
    case 1: return 'Analog'
    case 2: return 'DesigoCCImport'
    default: return 'Unknown'
  }
}

/**
 * 獲取測點用途中文名稱
 */
const getTagUsageName = (usage: string): string => {
  switch (usage) {
    case 'Normal': return '一般點'
    case 'Alarm': return '異常點'
    case 'Status': return '狀態點'
    case '': return '' // 空值時顯示空白，與舊系統一致
    default: return '' // 未知值也顯示空白，與舊系統一致
  }
}

/**
 * 根據單位 ID 獲取單位名稱
 */
const getUnitName = (unitId: string | number): string => {
  if (!unitId) return ''

  // 如果已經是字符串名稱，直接返回
  if (typeof unitId === 'string' && isNaN(Number(unitId))) {
    return unitId
  }

  // 從單位列表中查找對應的名稱
  const unit = unitList.value.find(u => u.Id === unitId || u.Id === Number(unitId))
  return unit?.Name || String(unitId)
}

/**
 * 單位變更處理
 */
const onUnitChange = (value: any) => {
  console.log('🔧 單位變更:', {
    '新值': value,
    '類型': typeof value,
    '單位列表': unitList.value,
    '匹配的單位': unitList.value.find(u => u.Id == value)
  })
}

/**
 * 獲取接點種類中文名稱
 */
const getContactTypeName = (contactType: string): string => {
  switch (contactType) {
    case 'NO': return '常開點'
    case 'NC': return '常閉點'
    case '': return '' // 空值時顯示空白，與舊系統一致
    default: return '' // 未知值也顯示空白，與舊系統一致
  }
}

/**
 * 載入測點列表
 * @param forceReload 是否強制重新載入，不使用快取
 */
const loadTagList = async (forceReload = false) => {
  try {
    tagLoading.value = true

    // 使用更新後的 tagsAPI 調用真實API載入測點列表
    // 先嘗試不傳遞 TagRalatedItemsUpdateTime 參數，看看是否能獲取到數據
    const lastRefreshTime = localStorage.getItem('tagRefreshTime')
    console.log('🔍 上次刷新時間:', lastRefreshTime)

    // 檢查當前的 CustomerId
    const currentCustomerId = localStorage.getItem('customer_id')
    const brandId = localStorage.getItem('brand_id')
    const plcCustomerId = localStorage.getItem('PLC_CUSTOMER_ID')
    console.log('🔍 當前 customer_id:', currentCustomerId)
    console.log('🔍 當前 brand_id:', brandId)
    console.log('🔍 當前 PLC_CUSTOMER_ID:', plcCustomerId)

    // 🔧 修復：與舊系統保持一致的快取邏輯
    // 舊系統邏輯：如果 forceReload 或沒有 lastRefreshTime，則不傳遞任何參數
    let params: any = {}
    if (!forceReload && lastRefreshTime) {
      params.TagRalatedItemsUpdateTime = lastRefreshTime
      console.log('📋 使用快取載入，TagRalatedItemsUpdateTime:', lastRefreshTime)
    } else {
      console.log('🔄 強制重新載入或無快取時間，不傳遞 TagRalatedItemsUpdateTime 參數')
    }

    const response = await tagsAPI.getTags(params)

    console.log('🔍 GetTagList API 完整響應:', response)
    console.log('🔍 API ReturnCode:', response?.ReturnCode, '(1=成功, 2=錯誤)')
    console.log('🔍 API Message:', response?.Message)
    console.log('🔍 API Detail 是否存在:', !!response?.Detail)

    // 檢查 API 響應狀態 - ReturnCode: 1 表示成功 (OK)，2 表示錯誤 (Error)
    if (response && response.ReturnCode === 1 && response.Detail) {
      const detail = response.Detail

      // 儲存完整的 API 響應數據供其他功能使用
      // 更新下拉選單資料（使用正確的屬性名稱）
      if (detail.DataTypeList) {
        dataTypeList.value = detail.DataTypeList
      }
      if (detail.UnitList) {
        unitList.value = detail.UnitList
      }
      if (detail.TagTypeList) {
        // 儲存測點類型列表
        console.log('🔍 測點類型列表:', detail.TagTypeList)
      }
      if (detail.SaveTypeList) {
        // 儲存存取類型列表
        console.log('🔍 存取類型列表:', detail.SaveTypeList)
      }
      if (detail.RegionHierarchyList) {
        regionList.value = detail.RegionHierarchyList
        console.log('🔍 地區階層列表:', detail.RegionHierarchyList)
      }
      if (detail.TagCategoryHierarchyList) {
        categoryList.value = detail.TagCategoryHierarchyList
        console.log('🔍 測點分類階層列表:', detail.TagCategoryHierarchyList)

        // 同步更新 tagClassTree 以供 el-tree-select 使用
        tagClassTree.value = detail.TagCategoryHierarchyList.map((category: any) => ({
          id: category.CategoryId || category.Id,
          name: category.CategoryName || category.Name,
          children: category.Children ? category.Children.map((child: any) => ({
            id: child.CategoryId || child.Id,
            name: child.CategoryName || child.Name,
            children: []
          })) : []
        }))

        console.log('🔍 同步更新 tagClassTree:', tagClassTree.value)

        // 詳細調試每個分類項目
        tagClassTree.value.forEach((category, index) => {
          console.log(`分類 ${index}:`, {
            'id': category.id,
            'name': category.name,
            'children': category.children
          })
          if (category.children && category.children.length > 0) {
            category.children.forEach((child, childIndex) => {
              console.log(`  子分類 ${childIndex}:`, {
                'id': child.id,
                'name': child.name
              })
            })
          }
        })
      }
      if (detail.CCTVMapList) {
        console.log('🔍 CCTV 對應列表:', detail.CCTVMapList)
      }

      // 儲存刷新時間
      if (detail.TagRalatedItemsUpdateTime) {
        localStorage.setItem('tagRefreshTime', detail.TagRalatedItemsUpdateTime)
      }
      
      // 檢查快取狀態
      console.log('🔍 CheckTagRalatedItemsUpdateTimeOK:', detail.CheckTagRalatedItemsUpdateTimeOK)

      if (detail.CheckTagRalatedItemsUpdateTimeOK === true) {
        console.log('✅ 快取有效，數據沒有變化，不需要更新')
        return
      }

      // 處理測點列表
      console.log('🔍 檢查 detail.TagList:', detail.TagList)
      console.log('🔍 detail.TagList 是否存在:', !!detail.TagList)
      console.log('🔍 detail.TagList 長度:', detail.TagList?.length)

      if (!detail.TagList || detail.TagList.length === 0) {
        console.warn('⚠️ detail.TagList 為空或不存在')
        tagList.value = []
        totalCount.value = 0
        return
      }

      if (detail.TagList && detail.TagList.length > 0) {
        // 清空原始資料 Map
        originalTagDataMap.value.clear()

        console.log('🔍 測點列表數量:', detail.TagList.length)
        console.log('🔍 第一個測點的完整原始數據:', detail.TagList[0])

        // 特別檢查 MB-B51電表 測點
        const mb51Tag = detail.TagList.find((tag: any) =>
          tag.Device?.Name?.includes('MB-B51電表') ||
          tag.Name?.includes('MB-B51電表') ||
          tag.SimpleName?.includes('MB-B51電表')
        )
        if (mb51Tag) {
          console.log('🔍 找到 MB-B51電表 測點:', mb51Tag)
          console.log('� ===== MB-B51電表即時功率 測點數值對比 =====')
          console.log('🏷️  測點基本信息:')
          console.log('   - 測點ID:', mb51Tag.Id)
          console.log('   - 測點名稱:', mb51Tag.Name || mb51Tag.SimpleName)
          console.log('   - 說明:', mb51Tag.Description)
          console.log('   - PLC位址:', mb51Tag.ValueAddress, '(舊系統: 400069)')

          console.log('� 測點配置:')
          console.log('   - 測點種類 ID:', mb51Tag.Type?.Id, '名稱:', mb51Tag.Type?.Name, '(舊系統: 類比測點)')
          console.log('   - 資料型別 ID:', mb51Tag.DataType?.Id, '名稱:', mb51Tag.DataType?.Name, '(舊系統: Short)')
          console.log('   - 測量單位 ID:', mb51Tag.Unit?.Id, '名稱:', mb51Tag.Unit?.Name, '(舊系統: kW)')
          console.log('   - 存取類型 ID:', mb51Tag.SaveType?.Id, '名稱:', mb51Tag.SaveType?.Name, '(舊系統: 可讀也可寫)')
          console.log('   - 儲存歷史:', mb51Tag.IsLog, '(舊系統: 儲存)')
          console.log('   - 儲存間隔:', mb51Tag.LogInterval, '分鐘')
          console.log('   - 儲存間隔類型:', mb51Tag.LogInterValType)

          console.log('🏢 關聯信息:')
          console.log('   - 裝置 ID:', mb51Tag.Device?.Id, '名稱:', mb51Tag.Device?.Name, '(舊系統: MB-B51電表)')
          console.log('   - 地區列表:', mb51Tag.RegionList, '(舊系統: 無)')
          console.log('   - CCTV列表:', mb51Tag.CctvList, '(舊系統: 包含 0 個CCTV)')
          console.log('   - 測點分類列表:', mb51Tag.TagCategoryList, '(舊系統: 請選擇)')

          console.log('⚠️  警報設定:')
          console.log('   - 警報配置:', mb51Tag.Alarm)
          console.log('📊 ============================================')
        }

        // 檢查剛才編輯的測點
        const lastEditedId = localStorage.getItem('lastEditedTagId')
        const editedTag = lastEditedId ?
          detail.TagList.find((tag: any) => tag.Id === lastEditedId) : null

        if (editedTag) {
          console.log('🔍 找到剛才編輯的測點:', editedTag)
          console.log('🎯 ===== 剛才編輯的測點警報檢查 =====')
          console.log('🏷️  測點基本信息:')
          console.log('   - 測點ID:', editedTag.Id)
          console.log('   - 測點名稱:', editedTag.Name || editedTag.SimpleName)
          console.log('   - 說明:', editedTag.Description)

          console.log('⚠️  警報設定檢查:')
          console.log('   - 警報配置:', editedTag.Alarm)
          if (editedTag.Alarm) {
            console.log('🔍 警報詳細檢查:')
            console.log('   - HH 警報: Status=', editedTag.Alarm.HHStatus, ', Value=', editedTag.Alarm.HHValue, ', Content=', editedTag.Alarm.HHContent)
            console.log('   - HI 警報: Status=', editedTag.Alarm.HIStatus, ', Value=', editedTag.Alarm.HIValue, ', Content=', editedTag.Alarm.HIContent)
            console.log('   - LO 警報: Status=', editedTag.Alarm.LOStatus, ', Value=', editedTag.Alarm.LOValue, ', Content=', editedTag.Alarm.LOContent)
            console.log('   - LL 警報: Status=', editedTag.Alarm.LLStatus, ', Value=', editedTag.Alarm.LLValue, ', Content=', editedTag.Alarm.LLContent)
          } else {
            console.log('❌ 沒有找到警報配置！')
          }
          console.log('📊 ============================================')
        } else {
          console.log('⚠️ 沒有找到剛才編輯的測點，lastEditedId:', lastEditedId)
        }

        // 獲取所有測點的ID，用於查詢測點屬性
        const tagIds = detail.TagList.map((tag: any) => tag.Id)
        console.log('🔍 準備獲取測點屬性，測點數量:', tagIds.length)

        // 獲取測點屬性（測點用途、接點種類等）
        let tagProperties: { [tagId: string]: { [propertyName: string]: string } } = {}
        try {
          tagProperties = await tagsAPI.getTagProperties(tagIds)
          console.log('🔍 測點屬性獲取成功:', tagProperties)
        } catch (error) {
          console.warn('⚠️ 獲取測點屬性失敗:', error)
          // 如果獲取失敗，不設定預設值，保持空白（與舊系統一致）
        }

        // 轉換後端數據格式為前端格式，保持與舊系統相同的顯示格式
        console.log('🔍 開始轉換測點數據，原始數量:', detail.TagList.length)
        tagList.value = detail.TagList.map((tag: any) => {
          const tagId = tag.Id // 使用正確的 Id 屬性

          // 儲存原始資料
          originalTagDataMap.value.set(tagId, tag)

          // 獲取該測點的屬性（與舊系統一致：沒有數據時不設定預設值）
          let properties = tagProperties[tagId] || {}

          // 調試特定測點的完整數據
          if (tag.Device?.Name?.includes('MB-B51電表')) {
            console.log('🔍 處理 MB-B51電表 測點數據轉換:', {
              '測點ID': tagId,
              '原始設備名稱': tag.Device?.Name,
              '原始CCTV列表': tag.CctvList,
              '原始測量單位': tag.Unit,
              '原始地區列表': tag.RegionList,
              '原始測點分類': tag.TagCategoryList,
              '原始警報設定': tag.Alarm,
              '測點屬性': properties,
              '最終usage值': properties.Usage || '(空白)',
              '最終contactType值': properties.ContactType || '(空白)'
            })
          }

          // 決定顯示名稱：與舊系統一致，優先使用 SimpleName
          let displayName = tag.SimpleName || tag.Name

          return {
            id: tagId,
            name: displayName, // 使用與舊系統一致的顯示名稱
            simpleName: tag.SimpleName, // 簡單測點名稱
            description: tag.Description,
            tagType: tag.Type?.Name || (tag.Type?.Id !== undefined ? getTagTypeName(tag.Type.Id) : 'Unknown'), // 使用正確的測點類型
            dataType: tag.DataType?.Name || 'Unknown', // 使用正確的資料類型
            address: tag.ValueAddress || '', // 使用正確的位址屬性
            deviceId: tag.Device?.Id || '',
            deviceName: tag.Device?.Name || '', // 完整的設備名稱，如 "高雄火車站商業大樓電力管理系統通道.MB-B51電表"
            unit: tag.Unit?.Name || '', // 測量單位名稱
            unitSymbol: tag.Unit?.Symbol || '', // 測量單位符號
            defaultValue: tag.InitialValue, // 使用正確的初始值屬性
            scanRate: tag.DataInterval || 1000, // 使用正確的掃描間隔
            status: tag.Status ? 'active' : 'inactive', // 使用正確的狀態屬性
            // 存取權限：根據 SaveType 判斷（與舊系統一致）
            isReadOnly: (tag.SaveType?.Id === 0), // 0=唯讀, 1=可讀也可寫
            alarmEnabled: tag.Alarm?.Status !== 0, // 0 = Disabled
            // 警報限制值
            highAlarmLimit: tag.Alarm?.HHValue,
            lowAlarmLimit: tag.Alarm?.LLValue,
            highWarningLimit: tag.Alarm?.HIValue,
            lowWarningLimit: tag.Alarm?.LOValue,
            // 當前值和品質
            currentValue: tag.CurrentValue,
            quality: tag.Quality,
            qualityText: tag.QualityText,
            // 額外的完整信息（與舊系統保持一致）
            regionList: tag.RegionList || [], // 地區層級列表
            cctvList: tag.CctvList || [], // CCTV 列表
            tagCategoryList: tag.TagCategoryList || [], // 測點分類列表
            locationName: tag.RegionList?.map((region: any) => region.Name).join(' > ') || '', // 位置名稱
            alarmSettings: tag.Alarm, // 完整的警報設定
            relatedPage: tag.RelatedPage, // 關聯頁面
            // 運算式相關
            isUseExpression: tag.IsUseExpression,
            expressMode: tag.ExpressMode,
            expressValue: tag.ExpressValue,
            // 記錄相關：根據後端 IsLog 值判斷（與舊系統一致）
            isLog: tag.IsLog, // 直接使用後端回傳的 IsLog 值
            logInterval: tag.LogInterval,
            logIntervalType: tag.LogInterValType?.Id || 2, // 使用 LogInterValType.Id，預設為定時(2)
            retentive: tag.ReTentive,
            ignore: tag.Ignore,
            // 測點屬性（從 GetTagProperties API 獲取）
            // 與舊系統一致：沒有數據時不設定預設值，保持空白
            usage: properties.Usage || '', // 測點用途：Normal(一般點), Alarm(異常點), Status(狀態點)，空白表示未設定
            contactType: properties.ContactType || '', // 接點種類：NO(常開點), NC(常閉點)，空白表示未設定
            // 時間戳記
            updateTime: new Date().toISOString(),
            customerId: tag.CustomerId
          }
        })

      totalCount.value = detail.TotalCount || tagList.value.length
      console.log('✅ 測點數據轉換完成:', {
        '原始測點數量': detail.TagList.length,
        '轉換後測點數量': tagList.value.length,
        '第一個轉換後的測點': tagList.value[0],
        'filteredTagList 長度': filteredTagList.value.length
      })
      ElMessage.success(`成功載入 ${tagList.value.length} 個測點`)
      } else {
        console.warn('⚠️ detail.TagList 為空或不存在')
        tagList.value = []
        totalCount.value = 0
        ElMessage.warning('未找到測點數據')
      }
    } else if (response && response.ReturnCode !== 1) {
      // API 返回錯誤 - ReturnCode 不等於 1 (OK)
      const errorMessage = response.Message || `API 錯誤 (ReturnCode: ${response.ReturnCode})`
      console.error('❌ API 返回錯誤:', errorMessage)
      tagList.value = []
      totalCount.value = 0
      ElMessage.error(errorMessage)
      throw new Error(errorMessage)
    } else {
      // 響應格式異常
      tagList.value = []
      totalCount.value = 0
      ElMessage.warning('API 響應格式異常或未找到測點數據')
    }

  } catch (error: any) {
    console.error('載入測點列表失敗:', error)
    ElMessage.error(error.message || '載入測點列表失敗')
  } finally {
    tagLoading.value = false
  }
}

/**
 * 載入測點分類樹
 */
const loadTagClassTree = async () => {
  try {
    classLoading.value = true

    // 調用真實API載入測點分類樹 - 使用群組階層列表
    const response = await plcDataService.get('/api/Tag/GetTagCategoryHierarchyList', {})

    if (response && response.Detail && response.Detail.TagCategoryHierarchyList) {
      // 轉換後端數據格式為前端格式（遞歸處理所有層級）
      const convertToTreeFormat = (items: any[]): TagClassItem[] => {
        return items.map((item: any) => ({
          id: item.CategoryId || item.Id,
          name: item.CategoryName || item.Name,
          children: item.Children ? convertToTreeFormat(item.Children) : []
        }))
      }

      tagClassTree.value = convertToTreeFormat(response.Detail.TagCategoryHierarchyList)

      if (tagClassTree.value.length > 0) {
        ElMessage.success(`成功載入 ${tagClassTree.value.length} 個測點分類`)
      } else {
        ElMessage.info('分類列表為空，可以新增分類')
      }
    } else {
      tagClassTree.value = []
      ElMessage.info('分類列表為空，可以新增分類')
    }

  } catch (error: any) {
    console.error('載入分類樹失敗:', error)
    ElMessage.error(error.message || '載入分類樹失敗')
  } finally {
    classLoading.value = false
  }
}

/**
 * 載入裝置選項
 */
const loadDeviceOptions = async () => {
  try {
    // 調用真實API載入裝置列表 - 修正API端點路徑
    const response = await plcDataService.get('/api/Tag/GetDeviceList', {})

    if (response && (response as any).Detail && (response as any).Detail.DeviceList) {
      // 轉換後端數據格式為前端格式
      deviceOptions.value = (response as any).Detail.DeviceList.map((device: any) => ({
        id: device.DeviceId || device.Id,
        name: device.DeviceName || device.Name,
        deviceType: device.DeviceType || 'PLC',
        description: device.DeviceDescription || device.Description,
        ipAddress: device.IpAddress,
        port: device.Port,
        protocol: device.Protocol,
        status: device.IsActive ? 'active' : 'inactive',
        createTime: device.CreatedTime || new Date().toISOString()
      }))

      console.log('裝置選項載入成功:', deviceOptions.value)
      ElMessage.success(`成功載入 ${deviceOptions.value.length} 個裝置`)
    } else {
      console.log('裝置選項載入失敗 - 回應結構:', response)
      deviceOptions.value = []
      ElMessage.warning('未找到裝置數據')
    }

  } catch (error: any) {
    console.error('載入裝置選項失敗:', error)
    ElMessage.error(error.message || '載入裝置選項失敗')
  }
}

/**
 * 載入通知群組選項
 */
const loadNotifyGroupOptions = async () => {
  try {
    // 調用真實API載入通知群組列表 - 使用正確的MessageController端點
    const response = await plcDataService.get('/api/Message/GetMessageGroupDetailList', {})

    if (response && response.Detail && response.Detail.MessageGroupDetailList) {
      notifyGroupOptions.value = response.Detail.MessageGroupDetailList.map((group: any) => ({
        id: group.Id,
        name: group.Name,
        description: group.Description || ''
      }))
    } else {
      notifyGroupOptions.value = []
    }
  } catch (error: any) {
    console.error('載入通知群組選項失敗:', error)
    notifyGroupOptions.value = []
  }
}



/**
 * 載入測點詳細資料
 */
const loadTagDetails = async (tagId: string) => {
  try {
    // 調用真實API載入測點詳細資料
    const response = await plcDataService.get(`/api/Tag/GetTagDetail/${tagId}`, {})

    if (response && response.Detail) {
      const detail = response.Detail

      // 填充警報設定數據
      if (detail.Alarm) {
        tagForm.alarmStatus = detail.Alarm.Status || 1
        tagForm.alarmAudio = detail.Alarm.Audio || false
        tagForm.alarmSOP = detail.Alarm.SOP || ''
        tagForm.notifyGroups = detail.Alarm.NotifyGroups || []

        // 類比警報
        if (detail.Alarm.HH) {
          tagForm.hhAlarmEnabled = detail.Alarm.HH.Enabled || false
          tagForm.hhAlarmValue = detail.Alarm.HH.Value || 0
          tagForm.hhAlarmDescription = detail.Alarm.HH.Description || ''
        }
        // ... 其他警報設定

        // 填充例外設定數據
        if (detail.Alarm.Exception) {
          tagForm.exceptionEnabled = detail.Alarm.Exception.Enabled || false
          if (detail.Alarm.Exception.StartTime) {
            const startTime = new Date()
            const [hours, minutes, seconds] = detail.Alarm.Exception.StartTime.split(':')
            startTime.setHours(parseInt(hours), parseInt(minutes), parseInt(seconds || 0))
            tagForm.exceptionStartTime = startTime
          }
          if (detail.Alarm.Exception.EndTime) {
            const endTime = new Date()
            const [hours, minutes, seconds] = detail.Alarm.Exception.EndTime.split(':')
            endTime.setHours(parseInt(hours), parseInt(minutes), parseInt(seconds || 0))
            tagForm.exceptionEndTime = endTime
          }
          tagForm.exceptionAction = detail.Alarm.Exception.Action || 0
          tagForm.exceptionDescription = detail.Alarm.Exception.Description || ''
        }
      }

      // 填充運算式設定數據
      if (detail.Expression) {
        tagForm.expressionEnabled = detail.Expression.Enabled || false
        tagForm.expressionType = detail.Expression.Type || 0
        tagForm.expressionContent = detail.Expression.Content || ''
      }
    }
  } catch (error: any) {
    console.error('載入測點詳細資料失敗:', error)
  }
}

// 生命週期
onMounted(async () => {
  // 首次載入時強制重新載入，不使用快取
  console.log('🔄 首次載入，強制重新載入所有數據')
  await loadTagList(true) // 強制重新載入
  
  // 載入測點分類樹
  await loadTagClassTree()
  
  // 載入裝置選項
  await loadDeviceOptions()
  
  // 載入通知群組選項
  await loadNotifyGroupOptions()
  
  // 載入地區列表
  try {
    const res = await regionAPI.getRegionList({})
    const regions = (res as any).Detail?.RegionHierarchyList || (res as any).data || []
    regionList.value = regions
    if (regionList.value.length > 0) {
      tagForm.regionId = regionList.value[0].Id
    }
  } catch (error) {
    console.error('載入地區列表失敗:', error)
    tagForm.regionId = '00000000-0000-0000-0000-000000000000'
  }
  
  // 載入單位列表
  try {
    const res = await unitAPI.getUnitList()
    const units = (res as any).Detail?.UnitList || (res as any).data || []
    unitList.value = units
    if (unitList.value.length > 0) {
      tagForm.unit = unitList.value[0].Id
    }
  } catch (error) {
    console.error('載入單位列表失敗:', error)
  }
})

// 監聽器
const detailDialogVisible = ref(false);
const realTimeValueDialogVisible = ref(false);

// ⚠️ 【重要】類型對照表 - 根據後端 EnumTagType 定義，請勿修改！
// 📝 參考：plc-frontend\參考資料\後端\Oco.Product-002\Oco.Core\Tag\EnumTagType.cs
const tagTypeMapping = {
  '類比測點': 1,        // Analog = 1
  '數位測點': 0,        // Digital = 0
  'DesigoCC「匯入」測點': 2, // DesigoCCImport = 2
  'Analog': 1,          // Analog = 1
  'Digital': 0,         // Digital = 0
  'DesigoCCImport': 2   // DesigoCCImport = 2
}

const dataTypeMapping = {
  'Boolean': 1,
  'Int16': 2,
  'Int32': 3,
  'Float': 4,
  'Double': 5,
  'String': 6
}

const saveTypeMapping = {
  '唯讀': 0,
  '可讀也可寫': 1
}

const logIntervalTypeMapping = {
  'second': 1,
  'minute': 2,
  'hour': 3,
  '秒': 1,
  '分': 2,
  '時': 3
}

const regionList = ref<any[]>([])
const categoryList = ref<any[]>([])
const unitList = ref<any[]>([])
const dataTypeList = ref<any[]>([])
const pageOptions = ref<any[]>([])

// 🔧 新增：SOP HTML 編輯器相關變數
const sopEditor = ref<IDomEditor | null>(null)
const sopToolbarConfig = {
  toolbarKeys: [
    'headerSelect',
    'bold',
    'italic',
    'underline',
    'through',
    'color',
    'bgColor',
    '|',
    'fontSize',
    'fontFamily',
    'lineHeight',
    '|',
    'bulletedList',
    'numberedList',
    'todo',
    '|',
    'emotion',
    'insertLink',
    'insertTable',
    'codeBlock',
    '|',
    'undo',
    'redo',
    'fullScreen'
  ]
}
const sopEditorConfig = {
  placeholder: '請輸入 SOP 內容...',
  MENU_CONF: {}
}

// 🔧 新增：SOP 編輯器創建回調
const handleSopEditorCreated = (editor: IDomEditor) => {
  sopEditor.value = editor
  console.log('🔧 SOP 編輯器已創建:', editor)
}

// 🔧 新增：組件銷毀時清理編輯器
onBeforeUnmount(() => {
  if (sopEditor.value) {
    sopEditor.value.destroy()
    sopEditor.value = null
  }
})

// 添加測點相關函數
const addTag = () => {
  if (tagForm.selectedTag) {
    tagForm.relatedTags.push({
      id: Date.now(),
      name: tagForm.selectedTag
    })
    tagForm.selectedTag = ''
  }
}

const removeTag = (tag: any) => {
  const index = tagForm.relatedTags.findIndex(t => t.id === tag.id)
  if (index > -1) {
    tagForm.relatedTags.splice(index, 1)
  }
}
</script>

<style scoped>
.tag-container {
  padding: 20px;
}

.page-header {
  margin-bottom: 20px;
}

.page-header h2 {
  margin: 0 0 8px 0;
  color: #303133;
}

.page-header p {
  margin: 0;
  color: #909399;
  font-size: 14px;
}

.tag-card {
  margin-bottom: 20px;
}

.alarm-section {
  margin-bottom: 24px;
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  background-color: #fafafa;
}

.alarm-section h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 500;
}

.notice-content {
  margin: 8px 0 16px 120px; /* 上下間距，左邊對齊表單內容區域 */
  padding: 12px 16px;
  background-color: #fef7e6;
  border: 2px solid #f5a623;
  border-radius: 4px;
  font-size: 13px;
  color: #d48806;
  line-height: 1.6;
  font-weight: 500;
  max-width: 500px; /* 限制寬度，避免過寬 */
}

.expression-form {
  padding: 16px;
}

.compact-form .el-form-item {
  margin-bottom: 16px;
}

.compact-form .el-form-item__label {
  font-weight: 500;
}

.filter-section {
  margin-bottom: 20px;
}

.tag-name {
  display: flex;
  align-items: center;
}

.tag-icon {
  margin-right: 8px;
  color: #409eff;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}

.tree-node {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding-right: 20px;
}

/* 警報設定樣式 */
.alarm-card {
  margin-bottom: 12px;
  border: 1px solid #e4e7ed;
}

.alarm-card.compact-card {
  margin-bottom: 8px;
}

.alarm-card.compact-card .el-card__body {
  padding: 12px 16px;
}

.alarm-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-weight: 500;
}

.alarm-header.compact-header {
  padding: 8px 0;
}

.alarm-title {
  font-size: 14px;
  color: #606266;
}

.alarm-form .el-form-item {
  margin-bottom: 16px;
}

.compact-form .el-form-item.compact-item {
  margin-bottom: 8px;
}

.compact-row {
  margin-bottom: 8px;
}

.compact-divider {
  margin: 12px 0 8px 0;
}

/* 運算式設定樣式 */
.expression-form .el-form-item {
  margin-bottom: 20px;
}

.expression-help {
  margin-top: 8px;
}

.expression-help .el-text {
  font-size: 12px;
}

.node-label {
  flex: 1;
  font-size: 14px;
}

.node-actions {
  display: flex;
  gap: 8px;
}

.node-actions .el-button {
  padding: 4px 8px;
  font-size: 12px;
}

.tag-form,
.alarm-form {
  padding: 20px 0;
}

/* 數值狀態樣式 */
.normal-value {
  color: #303133;
}

.warning-high,
.warning-low {
  color: #e6a23c;
  font-weight: bold;
}

.alarm-high,
.alarm-low {
  color: #f56c6c;
  font-weight: bold;
  animation: blink 1s infinite;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0.5; }
}

/* 設備名稱和地區名稱樣式 */
.device-name,
.location-name {
  display: inline-block;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.device-name {
  color: #409eff;
  font-weight: 500;
}

.location-name {
  color: #67c23a;
  font-size: 12px;
}

:deep(.el-form-item) {
  margin-bottom: 20px;
}

/* 分類管理樣式 */
.class-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.expand-icon {
  cursor: pointer;
  transition: transform 0.3s ease;
  color: #909399;
  font-size: 14px;
}

.expand-icon:hover {
  color: #409eff;
}

.expand-icon.expanded {
  transform: rotate(90deg);
}

.no-children-spacer {
  width: 14px;
  height: 14px;
}

.class-icon {
  color: #f39c12;
  font-size: 16px;
}

.children-count {
  margin-left: auto;
  font-size: 11px;
}

/* 父分類名稱樣式 */
.parent-name {
  color: #909399;
  font-size: 12px;
}

:deep(.el-tabs__content) {
  padding: 0;
}

:deep(.el-tree-node__content) {
  height: 40px;
}

:deep(.el-dialog__body) {
  padding: 20px;
}

:deep(.el-table .el-table__cell) {
  padding: 8px 0;
}
</style>
